using System.Diagnostics;
using Phoenix.Server.Common;

namespace UnitTests
{
    [TestFixture]
    public class AsyncDispatcherTests
    {
        private AsyncDispatcher m_dispatcher;

        [SetUp]
        public void Setup()
        {
            m_dispatcher = new AsyncDispatcher();
            m_dispatcher.Start();
        }

        [TearDown]
        public void TearDown()
        {
            m_dispatcher.Stop();
        }

        [Test]
        public void TestHighFrequencySmallTasks()
        {
            // 准备
            int taskCount = 100000;
            int completedTasks = 0;
            var completionEvent = new ManualResetEventSlim(false);
            var stopwatch = new Stopwatch();

            // 执行
            stopwatch.Start();
            for (int i = 0; i < taskCount; i++)
            {
                m_dispatcher.Post(() =>
                {
                    // 小任务：简单的计数器增加
                    Interlocked.Increment(ref completedTasks);
                    if (completedTasks == taskCount)
                    {
                        completionEvent.Set();
                    }
                });
            }

            // 等待所有任务完成
            bool completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            // 验证
            Assert.IsTrue(completed, "Not all tasks completed in time");
            Assert.AreEqual(taskCount, completedTasks);

            // 输出性能指标
            TestContext.WriteLine($"High Frequency Small Tasks Test:");
            TestContext.WriteLine($"Total tasks: {taskCount}");
            TestContext.WriteLine($"Total time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {taskCount * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");
            TestContext.WriteLine($"Average time per task: {stopwatch.ElapsedMilliseconds * 1000.0 / taskCount:N3} μs");
        }

        [Test]
        public void TestLowFrequencyLargeTasks()
        {
            // 准备
            int taskCount = 100;
            int completedTasks = 0;
            var completionEvent = new ManualResetEventSlim(false);
            var stopwatch = new Stopwatch();

            // 执行
            stopwatch.Start();
            for (int i = 0; i < taskCount; i++)
            {
                m_dispatcher.Post(() =>
                {
                    // 大任务：模拟计算密集型操作
                    SimulateCpuBoundWork(10);

                    Interlocked.Increment(ref completedTasks);
                    if (completedTasks == taskCount)
                    {
                        completionEvent.Set();
                    }
                });
            }

            // 等待所有任务完成
            bool completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            // 验证
            Assert.IsTrue(completed, "Not all tasks completed in time");
            Assert.AreEqual(taskCount, completedTasks);

            // 输出性能指标
            TestContext.WriteLine($"Low Frequency Large Tasks Test:");
            TestContext.WriteLine($"Total tasks: {taskCount}");
            TestContext.WriteLine($"Total time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {taskCount * 1000.0 / stopwatch.ElapsedMilliseconds:N2}");
            TestContext.WriteLine($"Average time per task: {stopwatch.ElapsedMilliseconds * 1.0 / taskCount:N2} ms");
        }

        [Test]
        public void TestMixedWorkload()
        {
            // 准备
            int smallTaskCount = 10000;
            int largeTaskCount = 50;
            int totalTaskCount = smallTaskCount + largeTaskCount;
            int completedTasks = 0;
            var completionEvent = new ManualResetEventSlim(false);
            var stopwatch = new Stopwatch();

            // 执行
            stopwatch.Start();

            // 提交大任务
            for (int i = 0; i < largeTaskCount; i++)
            {
                m_dispatcher.Post(() =>
                {
                    // 大任务：模拟计算密集型操作
                    SimulateCpuBoundWork(10);

                    Interlocked.Increment(ref completedTasks);
                    if (completedTasks == totalTaskCount)
                    {
                        completionEvent.Set();
                    }
                });
            }

            // 提交小任务
            for (int i = 0; i < smallTaskCount; i++)
            {
                m_dispatcher.Post(() =>
                {
                    // 小任务：简单的计数器增加
                    Interlocked.Increment(ref completedTasks);
                    if (completedTasks == totalTaskCount)
                    {
                        completionEvent.Set();
                    }
                });
            }

            // 等待所有任务完成
            bool completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            // 验证
            Assert.IsTrue(completed, "Not all tasks completed in time");
            Assert.AreEqual(totalTaskCount, completedTasks);

            // 输出性能指标
            TestContext.WriteLine($"Mixed Workload Test:");
            TestContext.WriteLine($"Total tasks: {totalTaskCount} (Small: {smallTaskCount}, Large: {largeTaskCount})");
            TestContext.WriteLine($"Total time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {totalTaskCount * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");
        }

        [Test]
        public void TestStressTest()
        {
            // 准备
            int producerCount = 10;
            int tasksPerProducer = 10000;
            int totalTaskCount = producerCount * tasksPerProducer;
            int completedTasks = 0;
            var completionEvent = new ManualResetEventSlim(false);
            var stopwatch = new Stopwatch();

            // 执行
            stopwatch.Start();

            // 创建多个生产者线程同时提交任务
            var producers = new Task[producerCount];
            for (int p = 0; p < producerCount; p++)
            {
                producers[p] = Task.Run(() =>
                {
                    for (int i = 0; i < tasksPerProducer; i++)
                    {
                        m_dispatcher.Post(() =>
                        {
                            // 混合任务：有时执行一些计算
                            if (i % 100 == 0)
                            {
                                SimulateCpuBoundWork(1);
                            }

                            int current = Interlocked.Increment(ref completedTasks);
                            if (current == totalTaskCount)
                            {
                                completionEvent.Set();
                            }
                        });
                    }
                });
            }

            // 等待所有生产者完成提交
            Task.WaitAll(producers);

            // 等待所有任务完成
            bool completed = completionEvent.Wait(TimeSpan.FromMinutes(5));
            stopwatch.Stop();

            // 验证
            Assert.IsTrue(completed, "Not all tasks completed in time");
            Assert.AreEqual(totalTaskCount, completedTasks);

            // 输出性能指标
            TestContext.WriteLine($"Stress Test:");
            TestContext.WriteLine($"Producers: {producerCount}");
            TestContext.WriteLine($"Tasks per producer: {tasksPerProducer}");
            TestContext.WriteLine($"Total tasks: {totalTaskCount}");
            TestContext.WriteLine($"Total time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {totalTaskCount * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");
            TestContext.WriteLine($"Average time per task: {stopwatch.ElapsedMilliseconds * 1000.0 / totalTaskCount:N3} μs");
        }

        [Test]
        public void CompareWithTaskRun()
        {
            int taskCount = 10000;

            // 测试 AsyncDispatcher
            TestContext.WriteLine("Testing AsyncDispatcher:");

            int completedTasks = 0;
            var completionEvent = new ManualResetEventSlim(false);
            var stopwatch = new Stopwatch();

            stopwatch.Start();
            for (int i = 0; i < taskCount; i++)
            {
                m_dispatcher.Post(() =>
                {
                    // 小任务
                    int result = 0;
                    for (int j = 0; j < 1000; j++)
                    {
                        result += j;
                    }

                    Interlocked.Increment(ref completedTasks);
                    if (completedTasks == taskCount)
                    {
                        completionEvent.Set();
                    }
                });
            }

            bool completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all AsyncDispatcher tasks completed in time");
            TestContext.WriteLine($"AsyncDispatcher time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {taskCount * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");

            // 测试 Task.Run
            TestContext.WriteLine("\nTesting Task.Run:");
            completedTasks = 0;
            completionEvent.Reset();
            stopwatch.Restart();

            for (int i = 0; i < taskCount; i++)
            {
                Task.Run(() =>
                {
                    // 相同的小任务
                    int result = 0;
                    for (int j = 0; j < 1000; j++)
                    {
                        result += j;
                    }

                    Interlocked.Increment(ref completedTasks);
                    if (completedTasks == taskCount)
                    {
                        completionEvent.Set();
                    }
                });
            }

            completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all Task.Run tasks completed in time");
            TestContext.WriteLine($"Task.Run time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {taskCount * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");

            // 测试 ThreadPool.QueueUserWorkItem
            TestContext.WriteLine("\nTesting ThreadPool.QueueUserWorkItem:");
            completedTasks = 0;
            completionEvent.Reset();
            stopwatch.Restart();

            for (int i = 0; i < taskCount; i++)
            {
                ThreadPool.QueueUserWorkItem(_ =>
                {
                    // 相同的小任务
                    int result = 0;
                    for (int j = 0; j < 1000; j++)
                    {
                        result += j;
                    }

                    Interlocked.Increment(ref completedTasks);
                    if (completedTasks == taskCount)
                    {
                        completionEvent.Set();
                    }
                });
            }

            completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all ThreadPool tasks completed in time");
            TestContext.WriteLine($"ThreadPool time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {taskCount * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");
        }

        [Test]
        public void TestClosurePerformance()
        {
            // 测试不使用闭包的情况
            TestContext.WriteLine("Testing without closures:");
            int completedTasks = 0;
            var stopwatch = new Stopwatch();

            stopwatch.Start();

            for (int i = 0; i < 100000; i++)
            {
                m_dispatcher.Post(SimpleAction);
            }

            // 等待一段时间让任务执行
            Thread.Sleep(2000);
            stopwatch.Stop();

            TestContext.WriteLine($"Time without closures: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Completed tasks: {Interlocked.Read(ref m_noClosureCounter)}");

            // 测试使用闭包的情况
            TestContext.WriteLine("\nTesting with closures:");
            completedTasks = 0;
            var completionEvent = new ManualResetEventSlim(false);
            stopwatch.Restart();

            for (int i = 0; i < 100000; i++)
            {
                int capturedI = i;
                m_dispatcher.Post(() =>
                {
                    // 使用捕获的变量
                    int result = capturedI * 2;

                    Interlocked.Increment(ref completedTasks);
                    if (completedTasks == 100000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            bool completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all closure tasks completed in time");
            TestContext.WriteLine($"Time with closures: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {100000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");
        }

        [Test]
        public void TestDifferentClosurePatterns()
        {
            // 测试1: 不使用闭包 - 直接传递预定义的Action
            TestContext.WriteLine("Test 1: No closures - predefined Action");
            m_noClosureCounter = 0;
            var stopwatch = Stopwatch.StartNew();

            for (int i = 0; i < 100000; i++)
            {
                m_dispatcher.Post(NoClosureAction);
            }

            // 等待一段时间让任务执行
            Thread.Sleep(2000);
            stopwatch.Stop();

            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Completed tasks: {Interlocked.Read(ref m_noClosureCounter)}");

            // 测试2: 简单闭包 - 捕获计数器
            TestContext.WriteLine("\nTest 2: Simple closure - capturing counter");
            long completedTasks = 0;
            stopwatch.Restart();

            for (int i = 0; i < 100000; i++)
            {
                m_dispatcher.Post(() =>
                {
                    Interlocked.Increment(ref completedTasks);
                });
            }

            // 等待一段时间让任务执行
            Thread.Sleep(2000);
            stopwatch.Stop();

            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Completed tasks: {Interlocked.Read(ref completedTasks)}");

            // 测试3: 复杂闭包 - 捕获循环变量
            TestContext.WriteLine("\nTest 3: Complex closure - capturing loop variable");
            completedTasks = 0;
            stopwatch.Restart();

            for (int i = 0; i < 100000; i++)
            {
                int capturedI = i;  // 正确捕获循环变量
                m_dispatcher.Post(() =>
                {
                    // 使用捕获的变量
                    int result = capturedI * 2;
                    Interlocked.Increment(ref completedTasks);
                });
            }

            // 等待一段时间让任务执行
            Thread.Sleep(2000);
            stopwatch.Stop();

            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Completed tasks: {Interlocked.Read(ref completedTasks)}");

            // 测试4: 多参数闭包 - 模拟 Post(() => action(arg1,arg2,arg3))
            TestContext.WriteLine("\nTest 4: Multi-parameter closure - simulating Post(() => action(arg1,arg2,arg3))");
            completedTasks = 0;
            stopwatch.Restart();

            for (int i = 0; i < 100000; i++)
            {
                int arg1 = i;
                string arg2 = $"string_{i}";
                double arg3 = i * 1.5;

                m_dispatcher.Post(() =>
                {
                    // 模拟调用带有多个参数的方法
                    MultiParamAction(arg1, arg2, arg3);
                    Interlocked.Increment(ref completedTasks);
                });
            }

            // 等待一段时间让任务执行
            Thread.Sleep(2000);
            stopwatch.Stop();

            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Completed tasks: {Interlocked.Read(ref completedTasks)}");

            // 测试5: 使用委托和参数 - 模拟更高效的方式
            TestContext.WriteLine("\nTest 5: Using delegate with parameters - more efficient approach");
            completedTasks = 0;
            stopwatch.Restart();

            var actionWithState = new ActionWithState(MultiParamActionWithState);

            for (int i = 0; i < 100000; i++)
            {
                var state = new ActionState
                {
                    Arg1 = i,
                    Arg2 = $"string_{i}",
                    Arg3 = i * 1.5
                };

                m_dispatcher.Post(() => actionWithState(state));
            }

            // 等待一段时间让任务执行
            Thread.Sleep(2000);
            stopwatch.Stop();

            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Completed tasks: {Interlocked.Read(ref completedTasks)}");

            // 测试6: 使用本地函数
            TestContext.WriteLine("\nTest 6: Using local functions");
            completedTasks = 0;
            stopwatch.Restart();

            for (int i = 0; i < 100000; i++)
            {
                int arg1 = i;
                string arg2 = $"string_{i}";
                double arg3 = i * 1.5;

                // 定义本地函数
                void LocalFunction()
                {
                    MultiParamAction(arg1, arg2, arg3);
                    Interlocked.Increment(ref completedTasks);
                }

                m_dispatcher.Post(LocalFunction);
            }

            // 等待一段时间让任务执行
            Thread.Sleep(2000);
            stopwatch.Stop();

            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Completed tasks: {Interlocked.Read(ref completedTasks)}");
        }

        [Test]
        public void TestRealWorldScenario()
        {
            // 模拟1: 使用闭包传递多个参数 - Post(() => action(arg1, arg2, arg3))
            TestContext.WriteLine("Scenario 1: Using closures to pass multiple parameters");
            var stopwatch = Stopwatch.StartNew();

            for (int i = 0; i < 10000; i++)
            {
                long playerId = 1000000 + i;
                string sessionId = $"session_{i}";
                bool relogin = i % 2 == 0;

                m_dispatcher.Post(() =>
                {
                    SimulatePlayerAction(playerId, sessionId, relogin);
                });
            }

            // 等待任务执行
            Thread.Sleep(2000);
            stopwatch.Stop();

            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Average time per task: {stopwatch.ElapsedMilliseconds * 1.0 / 10000:N3} ms");

            // 模拟2: 使用对象封装参数
            TestContext.WriteLine("\nScenario 2: Using object to encapsulate parameters");
            stopwatch.Restart();

            for (int i = 0; i < 10000; i++)
            {
                var playerAction = new PlayerAction
                {
                    PlayerId = 1000000 + i,
                    SessionId = $"session_{i}",
                    Relogin = i % 2 == 0
                };

                m_dispatcher.Post(() =>
                {
                    SimulatePlayerActionWithObject(playerAction);
                });
            }

            // 等待任务执行
            Thread.Sleep(2000);
            stopwatch.Stop();

            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Average time per task: {stopwatch.ElapsedMilliseconds * 1.0 / 10000:N3} ms");

            // 模拟3: 使用值类型结构体封装参数
            TestContext.WriteLine("\nScenario 3: Using value type struct to encapsulate parameters");
            stopwatch.Restart();

            for (int i = 0; i < 10000; i++)
            {
                var playerActionStruct = new PlayerActionStruct
                {
                    PlayerId = 1000000 + i,
                    SessionId = $"session_{i}",
                    Relogin = i % 2 == 0
                };

                m_dispatcher.Post(() =>
                {
                    SimulatePlayerActionWithStruct(playerActionStruct);
                });
            }

            // 等待任务执行
            Thread.Sleep(2000);
            stopwatch.Stop();

            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Average time per task: {stopwatch.ElapsedMilliseconds * 1.0 / 10000:N3} ms");
        }

        // 用于测试的辅助方法和类型
        private void SimpleAction()
        {
            // 简单操作，不使用任何捕获的变量
            Interlocked.Increment(ref m_noClosureCounter);
        }
        private long m_noClosureCounter;

        private void NoClosureAction()
        {
            // 简单操作，不使用任何捕获的变量
            Interlocked.Increment(ref m_noClosureCounter);
        }

        private void MultiParamAction(int arg1, string arg2, double arg3)
        {
            // 模拟使用多个参数的方法
            double result = arg1 * arg3;
            string output = arg2 + result;
        }

        private delegate void ActionWithState(ActionState state);

        private void MultiParamActionWithState(ActionState state)
        {
            // 与 MultiParamAction 相同的逻辑，但使用状态对象
            double result = state.Arg1 * state.Arg3;
            string output = state.Arg2 + result;
            Interlocked.Increment(ref m_noClosureCounter);
        }

        private class ActionState
        {
            public int Arg1 { get; set; }
            public string Arg2 { get; set; }
            public double Arg3 { get; set; }
        }

        // 模拟真实场景的方法和类型
        private void SimulatePlayerAction(long playerId, string sessionId, bool relogin)
        {
            // 模拟玩家操作
            Thread.Sleep(1); // 模拟一些处理时间
        }

        private void SimulatePlayerActionWithObject(PlayerAction action)
        {
            // 与 SimulatePlayerAction 相同的逻辑，但使用对象
            Thread.Sleep(1);
        }

        private void SimulatePlayerActionWithStruct(PlayerActionStruct action)
        {
            // 与 SimulatePlayerAction 相同的逻辑，但使用结构体
            Thread.Sleep(1);
        }

        private class PlayerAction
        {
            public long PlayerId { get; set; }
            public string SessionId { get; set; }
            public bool Relogin { get; set; }
        }

        private struct PlayerActionStruct
        {
            public long PlayerId { get; set; }
            public string SessionId { get; set; }
            public bool Relogin { get; set; }
        }

        // 辅助方法：模拟CPU密集型工作
        private void SimulateCpuBoundWork(int milliseconds)
        {
            var sw = Stopwatch.StartNew();
            while (sw.ElapsedMilliseconds < milliseconds)
            {
                // 执行一些计算以消耗CPU时间
                for (int i = 0; i < 10000; i++)
                {
                    Math.Sqrt(i * 123.456);
                }
            }
        }
    }
}
