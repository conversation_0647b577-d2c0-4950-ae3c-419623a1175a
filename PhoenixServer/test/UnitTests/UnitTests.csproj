<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <Configurations>Debug;Release</Configurations>
    <Platforms>x64</Platforms>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="IdGen" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="NUnit.Analyzers" />
    <PackageReference Include="coverlet.collector" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\ConfigData\ConfigData.csproj" />
    <ProjectReference Include="..\..\src\Phoenix.Server.CommonDataStructure\Phoenix.Server.CommonDataStructure.csproj" />
    <ProjectReference Include="..\..\src\Phoenix.Server.Common\Phoenix.Server.Common.csproj" />
    <ProjectReference Include="..\..\src\Phoenix.Server.Protocol\Phoenix.Server.Protocol.csproj" />
  </ItemGroup>

</Project>
