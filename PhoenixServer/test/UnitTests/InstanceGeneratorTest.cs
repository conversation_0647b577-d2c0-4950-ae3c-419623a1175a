/*using System.Collections.Concurrent;
using System.Diagnostics;
using IdGen;
using Phoenix.ProjectSS.CommonDataStructure;

namespace UnitTests;

[TestFixture]
public class InstanceGeneratorTest
{
    [SetUp]
    public void Setup()
    {
    }

    [Test]
    public void InstanceGeneratorTest1()
    {
        Stopwatch stopwatch = new();
        stopwatch.Start();
        List<Task> tasks = new();
        ConcurrentDictionary<long, long> ids = new();
        int generatorNum = 100000;
        int generatorCount = 100;
        for (int i = 0; i < generatorCount; i++)
        {
            int generatorIndex = i;
            Task t = Task.Run(() =>
            {
                IIdGenerator<long> generator = InstanceIdGenerator.CreateInstanceIdGenerator(generatorIndex);
                for (int j = 0; j < generatorNum; j++)
                {
                    long id = generator.CreateId();
                    bool ret = ids.TryAdd(id, id);
                    if (!ret)
                    {
                        throw new Exception("id conflict");
                    }
                }
            });
            tasks.Add(t);
        }

        Task.WaitAll(tasks.ToArray());
        stopwatch.Stop();
        Console.WriteLine(stopwatch.ElapsedMilliseconds);
        Assert.That(ids.Count, Is.EqualTo(generatorNum * generatorCount));
    }
}
*/
