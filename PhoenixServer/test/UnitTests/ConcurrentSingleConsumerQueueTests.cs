/*using Phoenix.ProjectSS.Utilities.Concurrent;

namespace UnitTests;

[TestFixture]
public class ConcurrentSingleConsumerQueueTests
{
    [SetUp]
    public void Setup()
    {
    }

    [Test]
    public void EnqueueDequeue_SingleThread()
    {
        ConcurrentSingleConsumerQueue<int> queue = new ConcurrentSingleConsumerQueue<int>();

        queue.Enqueue(1);
        queue.Enqueue(2);
        queue.Enqueue(3);

        Assert.True(queue.TryDequeue(out int item));
        Assert.That(item, Is.EqualTo(1));

        Assert.True(queue.TryDequeue(out item));
        Assert.That(item, Is.EqualTo(2));

        Assert.True(queue.TryDequeue(out item));
        Assert.That(item, Is.EqualTo(3));

        Assert.False(queue.TryDequeue(out _));
    }

    [Test]
    public async Task EnqueueDequeue_MultipleThreads()
    {
        ConcurrentSingleConsumerQueue<int> queue = new ConcurrentSingleConsumerQueue<int>();
        int sum = 0;

        // Multiple producers.
        Task[] producerTasks = new[]
        {
            Task.Run(() =>
            {
                for (int i = 0; i < 10000; i++)
                {
                    queue.Enqueue(i);
                }
            }),
            Task.Run(() =>
            {
                for (int i = 10000; i < 20000; i++)
                {
                    queue.Enqueue(i);
                }
            })
        };

        // Single consumer.
        Task consumerTask = Task.Run(() =>
        {
            while (true)
            {
                if (queue.TryDequeue(out int item))
                {
                    Interlocked.Add(ref sum, item);
                }
                else if (Task.WhenAll(producerTasks).IsCompleted) // If no items left and producers are done.
                {
                    break;
                }
                else
                {
                    Thread.Yield(); // If no items left but producers are still running.
                }
            }
        });

        await Task.WhenAll(producerTasks);
        await consumerTask;

        // The sum should be the sum of integers from 0 to 19999.
        Assert.That(sum, Is.EqualTo(199990000));
    }

    [Test]
    public async Task EnqueueDequeue_Order_MultipleThreads()
    {
        ConcurrentSingleConsumerQueue<int> queue = new ConcurrentSingleConsumerQueue<int>();
        int lastDequeued = -1;
        int itemCount = 10000000;

        // Producer.
        Task producerTask = Task.Run(() =>
        {
            for (int i = 0; i < itemCount; i++)
            {
                queue.Enqueue(i);
            }
        });

        // Consumer.
        Task consumerTask = Task.Run(async () =>
        {
            for (int i = 0; i < itemCount; i++)
            {
                int item;
                while (!queue.TryDequeue(out item))
                {
                    await Task.Delay(1); // Wait if no items.
                }

                Assert.True(item > lastDequeued); // Verify order.
                lastDequeued = item;
            }
        });

        await Task.WhenAll(producerTask, consumerTask);
    }
}
*/
