/*using System.Diagnostics;
using System.Security.Cryptography;
using System.Text;
using RSA = Phoenix.ProjectSS.Utilities.Security.RSA;

namespace UnitTests;

[TestFixture]
public class RSATests
{
    [SetUp]
    public void Setup()
    {
    }

    [Test]
    public void KeyGenTest()
    {
        RSACryptoServiceProvider rsaProvider = new();
        string? rsaPrivate = rsaProvider.ToXmlString(true);
        string? rsaPublic = rsaProvider.ToXmlString(false);
        Debug.Assert(rsaPrivate != null);
        Debug.Assert(rsaPublic != null);
    }

    [Test]
    public void SHA1Test()
    {
        RSACryptoServiceProvider rasProvider = new();
#pragma warning disable CA5350
        SHA1 sha1 = SHA1.Create();
#pragma warning restore CA5350

        string orgData = "this content is use to test sha1 sign";
        byte[] signedData = Encoding.UTF8.GetBytes(orgData);

        byte[] sign = rasProvider.SignData(signedData, sha1);
        string signStr = Encoding.UTF8.GetString(sign);
        bool verifyResult = rasProvider.VerifyData(signedData, sha1, sign);

#pragma warning disable CA5350
        SHA1 hash = SHA1.Create();
#pragma warning restore CA5350
        byte[] hashedData = hash.ComputeHash(signedData);
        rasProvider.VerifyHash(hashedData, CryptoConfig.MapNameToOID("SHA1")!, sign);

        Debug.Assert(verifyResult);
    }

    [Test]
    public void SignTest()
    {
        string currentDirectory = Directory.GetCurrentDirectory();
        string rsaPath = currentDirectory + Path.DirectorySeparatorChar + ".." + Path.DirectorySeparatorChar
                         + ".." + Path.DirectorySeparatorChar + ".." + Path.DirectorySeparatorChar + ".." +
                         Path.DirectorySeparatorChar + "Config" + Path.DirectorySeparatorChar + "RSA"
                         + Path.DirectorySeparatorChar + "RSAPrivate.xml";
        RSA.InitRSAParametersFromKeyFile(rsaPath);
        string data = "this is use to test sign";
        string sign = RSA.SignData(data);

        bool verifyResult = RSA.VerifyData(data, sign);
        Debug.Assert(verifyResult);
    }
}
*/
