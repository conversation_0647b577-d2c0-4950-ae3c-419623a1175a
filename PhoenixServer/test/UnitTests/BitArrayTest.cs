/*using Phoenix.ProjectSS.Utilites;

namespace UnitTests;

[TestFixture]
public class BitArrayTest
{
    [Test]
    public void SetBits_ShouldSetBitsInBitArray()
    {
        // Arrange
        BitArray bitArray = new BitArray();
        List<int> bitsToSet = new List<int> { 0, 2, 4 };

        // Act
        bitArray.SetBits(bitsToSet);

        // Assert
        Assert.IsTrue(bitArray.IsBitSet(0));
        Assert.IsFalse(bitArray.IsBitSet(1));
        Assert.IsTrue(bitArray.IsBitSet(2));
        Assert.IsFalse(bitArray.IsBitSet(3));
        Assert.IsTrue(bitArray.IsBitSet(4));
    }

    [Test]
    public void CleanBits_ShouldClearBitsInBitArray()
    {
        // Arrange
        BitArray bitArray = new BitArray();
        bitArray.SetBits(new List<int> { 0, 2, 4 });
        List<int> bitsToClean = new List<int> { 2, 4 };

        // Act
        bitArray.CleanBits(bitsToClean);

        // Assert
        Assert.IsTrue(bitArray.IsBitSet(0));
        Assert.IsFalse(bitArray.IsBitSet(1));
        Assert.IsFalse(bitArray.IsBitSet(2));
        Assert.IsFalse(bitArray.IsBitSet(3));
        Assert.IsFalse(bitArray.IsBitSet(4));
    }

    [Test]
    public void IsBitSet_ShouldReturnTrueIfBitIsSet()
    {
        // Arrange
        BitArray bitArray = new BitArray();
        bitArray.SetABit(3);

        // Act
        bool isBitSet = bitArray.IsBitSet(3);

        // Assert
        Assert.IsTrue(isBitSet);
    }

    [Test]
    public void IsBitSet_ShouldReturnFalseIfBitIsNotSet()
    {
        // Arrange
        BitArray bitArray = new BitArray();
        bitArray.SetABit(2);

        // Act
        bool isBitSet = bitArray.IsBitSet(3);

        // Assert
        Assert.IsFalse(isBitSet);
    }

    [Test]
    public void GetAllBitSets_ShouldReturnAllSetBits()
    {
        // Arrange
        BitArray bitArray = new BitArray();
        bitArray.SetBits(new List<int> { 1, 3, 5 });

        // Act
        IEnumerable<int> bitSets = bitArray.GetAllBitSets();

        // Assert
        CollectionAssert.AreEquivalent(new List<int> { 1, 3, 5 }, bitSets);
    }

    [Test]
    public void Clear_ShouldRemoveAllBits()
    {
        // Arrange
        BitArray bitArray = new BitArray();
        bitArray.SetBits(new List<int> { 0, 2, 4 });

        // Act
        bitArray.Clear();
*//*
        // Assert
        Assert.IsFalse(bitArray.IsBitSet(0));
        Assert.IsFalse(bitArray.IsBitSet(2));
        Assert.IsFalse(bitArray.IsBitSet(4));
    }
}
*/
