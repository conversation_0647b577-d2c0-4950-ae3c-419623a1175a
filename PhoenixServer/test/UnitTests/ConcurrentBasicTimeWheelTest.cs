/*using Phoenix.ProjectSS.Utilities;
using Phoenix.ProjectSS.Utilities.Concurrent;

namespace UnitTests;

[TestFixture]
public class ConcurrentBasicTimeWheelTest
{
    [SetUp]
    public void Setup()
    {
    }

    [Test]
    public async Task TestAddTimerDueTime()
    {
        ConcurrentBasicTimeWheel timeWheel = new(60);
        DateTime now = DateTime.UtcNow;
        DateTime dueTime = now.AddSeconds(10);
        long dueTimeTs = DateTimeHelper.GetTimestampSeconds(dueTime);
        (bool succeed, TimeWheelTimer timer) =
            await timeWheel.TryAddTimer(dueTime, async () => { await Task.Delay(1); });
        Assert.IsTrue(succeed);

        try
        {
            (succeed, timer) = await timeWheel.TryAddTimer(now, async () => { await Task.Delay(1); });

            Assert.IsTrue(false);
        }
        catch (ArgumentOutOfRangeException)
        {
            Assert.IsTrue(succeed);
        }

        await timeWheel.AdvanceToTimestamp(dueTimeTs + 1000);

        (succeed, timer) = await timeWheel.TryAddTimer(dueTime.AddSeconds(100), async () => { await Task.Delay(1); });

        Assert.IsTrue(!succeed);
    }

    [Test]
    public async Task TestAddTimerOrder()
    {
        long count = 0;
        ConcurrentBasicTimeWheel timeWheel = new(60);

        DateTime dueTime = DateTime.UtcNow.AddSeconds(10);
        int[] tests = new int[1000];
        for (int i = 0; i < tests.Length; i++)
        {
            tests[i] = i;
            int num = i + 1;
            (bool succeed, TimeWheelTimer timer) = await timeWheel.TryAddTimer(dueTime, async () =>
            {
                long inc = ++count;
                Assert.IsTrue(inc == num);
                await Task.Delay(1);
            });
        }

        await timeWheel.AdvanceToTimestamp(DateTimeHelper.GetTimestampSeconds(dueTime));

        while (count != tests.Length)
        {
            await Task.Delay(1000);
        }
    }


    [Test]
    public async Task TestTimerCount()
    {
        long count = 0;
        ConcurrentBasicTimeWheel timeWheel = new(60);

        DateTime now = DateTime.UtcNow;
        long nowTs = DateTimeHelper.GetTimestampSeconds(now);
        DateTime maxDueTime = now.AddSeconds(20);
        long maxDueTimeTs = DateTimeHelper.GetTimestampSeconds(maxDueTime);
        int[] tests = new int[10000];
        for (int i = 0; i < tests.Length; i++)
        {
            tests[i] = i;
        }

        TimeWheelTimer[] timers = new TimeWheelTimer[tests.Length];
        await Parallel.ForEachAsync(tests, async (i, ct) =>
        {
            int delta = (int)(maxDueTimeTs - nowTs);
            int rn = ConcurrentRandom.NextInt(delta / 2, delta);
            (bool succeed, TimeWheelTimer timer) = await timeWheel.TryAddTimer(now.AddSeconds(rn + 1), () =>
            {
                Interlocked.Increment(ref count);
                return Task.CompletedTask;
            });
            Assert.IsTrue(succeed);
            timers[i] = timer;
        });

        await Parallel.ForEachAsync(tests, async (i, ct) =>
        {
            if (i == 0)
            {
                for (long j = nowTs; j <= maxDueTimeTs; j++)
                {
                    await timeWheel.AdvanceToTimestamp(j);
                    await Task.Delay(1);
                }
            }
            else
            {
                int rn = ConcurrentRandom.NextInt(5);
                if (rn == 0)
                {
                    await Task.Delay(ConcurrentRandom.NextInt(5000));
                    if (await timeWheel.TryRemoveTimer(timers[i]))
                    {
                        Interlocked.Increment(ref count);
                    }
                }
            }
        });

        DateTime start = DateTime.UtcNow;
        while (count != tests.Length && (DateTime.UtcNow - start).TotalMinutes < 1)
        {
            await Task.Delay(1000);
        }

        Assert.That(count, Is.EqualTo(tests.Length));
    }
}
*/
