/*using System.Text;
using Phoenix.ProjectSS.Utilities.Concurrent;

namespace UnitTests;

[TestFixture]
public class ConcurrentWeakObjectPoolTest
{
    [SetUp]
    public void Setup()
    {
    }

    [Test]
    public async Task ConcurrentTest()
    {
        ConcurrentWeakObjectPool<StringBuilder> pool = new(() => new StringBuilder("new"), s =>
        {
            s.Remove(0, s.Length);
            s.Append("disposed");
        });
        int[] tests = new int[100];
        int newCount = 0;
        int reuseCount = 0;

        long quit = 0L;
        for (int i = 0; i < tests.Length; ++i)
        {
            tests[i] = i;
        }

        await Parallel.ForEachAsync(tests, async (i, ct) =>
        {
            if (i == 0)
            {
                for (int j = 0; j < 60; ++j)
                {
                    GC.Collect(0);
                    await Task.Delay(1000);
                }

                Interlocked.Exchange(ref quit, 1);
            }
            else
            {
                while (Interlocked.Read(ref quit) == 0)
                {
                    StringBuilder s = pool.GetOrCreate();
                    if (s.ToString() == "new")
                    {
                        Interlocked.Increment(ref newCount);
                    }
                    else if (s.ToString() == "disposed")
                    {
                        Interlocked.Increment(ref reuseCount);
                    }
                    else
                    {
                        Assert.That(s.ToString(), Is.AnyOf("new", "disposed"));
                    }

                    for (int j = 0; j < 10; ++j)
                    {
                        s.Append(i);
                        await Task.Delay(1);
                    }

                    pool.Return(s);
                }
            }
        });

        Assert.That(newCount, Is.GreaterThan(0));
        Assert.That(reuseCount, Is.GreaterThan(0));
    }
}
*/
