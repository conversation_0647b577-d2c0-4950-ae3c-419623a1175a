// Copyright (c) Phoenix All Rights Reserved.

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Phoenix.ConfigData.QuestGraph;

namespace UnitTests;

[TestFixture]
public class QuestTest
{

    [SetUp]
    public void Setup()
    {
    }

    [Test]
    public void TestJsonGraph()
    {
        // example json file
        var exampleFile = Path.Combine("../../../../../../src/ResourceLink/QuestGraphData/Example", "SG1000_1001.SG");
        Assert.IsTrue(File.Exists(exampleFile));
        // read the json file
        var json = File.ReadAllText(exampleFile);
        // parse the json file
        var jo = JObject.Parse(json);
        var graph = DeserializeUtility.DeserializeNodeGraph(jo);
        Assert.NotNull(graph);


        var settings = new JsonSerializerSettings();

        // settings.Converters.Add(new NodeGraphConverter());
        // settings.Converters.Add(new NodeConverter());
        //
        // var graph1 = JsonConvert.DeserializeObject<NodeGraph>(json, settings);


    }

}
