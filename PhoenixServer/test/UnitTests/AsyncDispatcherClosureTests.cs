using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using NUnit.Framework;
using Phoenix.Server.Common;

namespace UnitTests
{
    [TestFixture]
    public class AsyncDispatcherClosureTests
    {
        private AsyncDispatcher m_dispatcher;

        [SetUp]
        public void Setup()
        {
            m_dispatcher = new AsyncDispatcher();
            m_dispatcher.Start();
        }

        [TearDown]
        public void TearDown()
        {
            m_dispatcher.Stop();
        }

        [Test]
        public void TestClosurePatterns()
        {
            // 测试1: 直接调用方法 - 基准测试
            TestContext.WriteLine("Test 1: Direct method call (baseline)");
            var stopwatch = Stopwatch.StartNew();

            int iterations = 1000000;
            for (int i = 0; i < iterations; i++)
            {
                DirectMethodCall(i, $"string_{i}", i * 1.5);
            }

            stopwatch.Stop();
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Operations per second: {iterations * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");

            // 测试2: 使用闭包调用方法 - Post(() => action(arg1, arg2, arg3))
            TestContext.WriteLine("\nTest 2: Using closure to call method - Post(() => action(arg1, arg2, arg3))");
            int completedTasks = 0;
            var completionEvent = new ManualResetEventSlim(false);
            stopwatch.Restart();

            for (int i = 0; i < 100000; i++)
            {
                int arg1 = i;
                string arg2 = $"string_{i}";
                double arg3 = i * 1.5;

                m_dispatcher.Post(() =>
                {
                    DirectMethodCall(arg1, arg2, arg3);

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 100000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            bool completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {100000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");

            // 测试3: 使用对象封装参数
            TestContext.WriteLine("\nTest 3: Using object to encapsulate parameters");
            completedTasks = 0;
            completionEvent.Reset();
            stopwatch.Restart();

            for (int i = 0; i < 100000; i++)
            {
                var args = new MethodArgs
                {
                    Arg1 = i,
                    Arg2 = $"string_{i}",
                    Arg3 = i * 1.5
                };

                m_dispatcher.Post(() =>
                {
                    ObjectMethodCall(args);

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 100000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {100000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");

            // 测试4: 使用结构体封装参数
            TestContext.WriteLine("\nTest 4: Using struct to encapsulate parameters");
            completedTasks = 0;
            completionEvent.Reset();
            stopwatch.Restart();

            for (int i = 0; i < 100000; i++)
            {
                var args = new MethodArgsStruct
                {
                    Arg1 = i,
                    Arg2 = $"string_{i}",
                    Arg3 = i * 1.5
                };

                m_dispatcher.Post(() =>
                {
                    StructMethodCall(args);

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 100000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {100000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");

            // 测试5: 使用委托和参数 - 避免闭包
            TestContext.WriteLine("\nTest 5: Using delegate with parameters - avoiding closure");
            completedTasks = 0;
            completionEvent.Reset();
            stopwatch.Restart();

            var actionWithState = new ActionWithState(StateMethodCall);

            for (int i = 0; i < 100000; i++)
            {
                var state = new MethodArgs
                {
                    Arg1 = i,
                    Arg2 = $"string_{i}",
                    Arg3 = i * 1.5
                };

                m_dispatcher.Post(() =>
                {
                    actionWithState(state);

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 100000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {100000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");
        }

        [Test]
        public void TestMemoryAllocationWithClosures()
        {
            // 测试1: 使用闭包的内存分配 - Post(() => action(arg1, arg2, arg3))
            TestContext.WriteLine("Test 1: Memory allocation with closures - Post(() => action(arg1, arg2, arg3))");
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            long initialMemory = GC.GetTotalMemory(true);

            int taskCount = 100000;
            int batchSize = 10000;

            for (int batch = 0; batch < taskCount / batchSize; batch++)
            {
                for (int i = 0; i < batchSize; i++)
                {
                    int arg1 = i;
                    string arg2 = $"string_{i}";
                    double arg3 = i * 1.5;

                    m_dispatcher.Post(() =>
                    {
                        DirectMethodCall(arg1, arg2, arg3);
                    });
                }

                // 每批次后测量内存
                if ((batch + 1) % 10 == 0)
                {
                    long currentMemory = GC.GetTotalMemory(false);
                    TestContext.WriteLine($"After {(batch + 1) * batchSize} tasks, memory usage: {(currentMemory - initialMemory) / 1024} KB");
                }
            }

            // 等待任务执行
            Thread.Sleep(2000);

            // 最终内存使用
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            long finalMemory = GC.GetTotalMemory(true);

            TestContext.WriteLine($"Initial memory: {initialMemory / 1024} KB");
            TestContext.WriteLine($"Final memory: {finalMemory / 1024} KB");
            TestContext.WriteLine($"Difference: {(finalMemory - initialMemory) / 1024} KB");

            // 测试2: 使用对象封装参数的内存分配
            TestContext.WriteLine("\nTest 2: Memory allocation with object encapsulation");
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            initialMemory = GC.GetTotalMemory(true);

            for (int batch = 0; batch < taskCount / batchSize; batch++)
            {
                for (int i = 0; i < batchSize; i++)
                {
                    var args = new MethodArgs
                    {
                        Arg1 = i,
                        Arg2 = $"string_{i}",
                        Arg3 = i * 1.5
                    };

                    m_dispatcher.Post(() =>
                    {
                        ObjectMethodCall(args);
                    });
                }

                // 每批次后测量内存
                if ((batch + 1) % 10 == 0)
                {
                    long currentMemory = GC.GetTotalMemory(false);
                    TestContext.WriteLine($"After {(batch + 1) * batchSize} tasks, memory usage: {(currentMemory - initialMemory) / 1024} KB");
                }
            }

            // 等待任务执行
            Thread.Sleep(2000);

            // 最终内存使用
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            finalMemory = GC.GetTotalMemory(true);

            TestContext.WriteLine($"Initial memory: {initialMemory / 1024} KB");
            TestContext.WriteLine($"Final memory: {finalMemory / 1024} KB");
            TestContext.WriteLine($"Difference: {(finalMemory - initialMemory) / 1024} KB");

            // 测试3: 使用对象池减少内存分配
            TestContext.WriteLine("\nTest 3: Memory allocation with object pooling");
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            initialMemory = GC.GetTotalMemory(true);

            // 创建一个简单的对象池
            var argsPool = new ConcurrentBag<MethodArgs>();
            for (int i = 0; i < 1000; i++) // 预先创建1000个对象
            {
                argsPool.Add(new MethodArgs());
            }

            for (int batch = 0; batch < taskCount / batchSize; batch++)
            {
                for (int i = 0; i < batchSize; i++)
                {
                    // 尝试从池中获取对象
                    if (!argsPool.TryTake(out var args))
                    {
                        args = new MethodArgs();
                    }

                    // 设置状态
                    args.Arg1 = i;
                    args.Arg2 = $"string_{i}";
                    args.Arg3 = i * 1.5;

                    // 捕获状态对象
                    var capturedArgs = args;

                    m_dispatcher.Post(() =>
                    {
                        ObjectMethodCall(capturedArgs);
                        // 使用完后返回池中
                        argsPool.Add(capturedArgs);
                    });
                }

                // 每批次后测量内存
                if ((batch + 1) % 10 == 0)
                {
                    long currentMemory = GC.GetTotalMemory(false);
                    TestContext.WriteLine($"After {(batch + 1) * batchSize} tasks, memory usage: {(currentMemory - initialMemory) / 1024} KB");
                }
            }

            // 等待任务执行
            Thread.Sleep(2000);

            // 最终内存使用
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            finalMemory = GC.GetTotalMemory(true);

            TestContext.WriteLine($"Initial memory: {initialMemory / 1024} KB");
            TestContext.WriteLine($"Final memory: {finalMemory / 1024} KB");
            TestContext.WriteLine($"Difference: {(finalMemory - initialMemory) / 1024} KB");
        }

        [Test]
        public void TestNestedClosurePerformance()
        {
            // 测试1: 单层闭包 - Post(() => action(arg1, arg2, arg3))
            TestContext.WriteLine("Test 1: Single-level closure - Post(() => action(arg1, arg2, arg3))");
            int completedTasks = 0;
            var completionEvent = new ManualResetEventSlim(false);
            var stopwatch = Stopwatch.StartNew();

            for (int i = 0; i < 100000; i++)
            {
                int arg1 = i;
                string arg2 = $"string_{i}";
                double arg3 = i * 1.5;

                m_dispatcher.Post(() =>
                {
                    DirectMethodCall(arg1, arg2, arg3);

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 100000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            bool completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {100000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");

            // 测试2: 嵌套闭包 - Post(() => { var x = arg1; action(() => { use(x); }); })
            TestContext.WriteLine("\nTest 2: Nested closure - Post(() => { var x = arg1; action(() => { use(x); }); })");
            completedTasks = 0;
            completionEvent.Reset();
            stopwatch.Restart();

            for (int i = 0; i < 100000; i++)
            {
                int arg1 = i;
                string arg2 = $"string_{i}";
                double arg3 = i * 1.5;

                m_dispatcher.Post(() =>
                {
                    // 嵌套闭包
                    Action nestedAction = () =>
                    {
                        // 使用外层闭包捕获的变量
                        DirectMethodCall(arg1, arg2, arg3);
                    };

                    // 执行嵌套闭包
                    nestedAction();

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 100000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {100000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");

            // 测试3: 多层嵌套闭包
            TestContext.WriteLine("\nTest 3: Multi-level nested closure");
            completedTasks = 0;
            completionEvent.Reset();
            stopwatch.Restart();

            for (int i = 0; i < 100000; i++)
            {
                int arg1 = i;
                string arg2 = $"string_{i}";
                double arg3 = i * 1.5;

                m_dispatcher.Post(() =>
                {
                    // 第一层嵌套闭包
                    Action level1Action = () =>
                    {
                        // 第二层嵌套闭包
                        Action level2Action = () =>
                        {
                            // 第三层嵌套闭包
                            Action level3Action = () =>
                            {
                                // 使用最外层闭包捕获的变量
                                DirectMethodCall(arg1, arg2, arg3);
                            };

                            level3Action();
                        };

                        level2Action();
                    };

                    level1Action();

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 100000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {100000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");
        }

        // 用于测试的辅助方法和类型
        private void DirectMethodCall(int arg1, string arg2, double arg3)
        {
            // 模拟使用多个参数的方法
            double result = arg1 * arg3;
            string output = arg2 + result;
        }

        private void ObjectMethodCall(MethodArgs args)
        {
            // 与 DirectMethodCall 相同的逻辑，但使用对象
            double result = args.Arg1 * args.Arg3;
            string output = args.Arg2 + result;
        }

        private void StructMethodCall(MethodArgsStruct args)
        {
            // 与 DirectMethodCall 相同的逻辑，但使用结构体
            double result = args.Arg1 * args.Arg3;
            string output = args.Arg2 + result;
        }

        private delegate void ActionWithState(MethodArgs state);

        private void StateMethodCall(MethodArgs state)
        {
            // 与 DirectMethodCall 相同的逻辑，但使用状态对象
            double result = state.Arg1 * state.Arg3;
            string output = state.Arg2 + result;
        }

        private class MethodArgs
        {
            public int Arg1 { get; set; }
            public string Arg2 { get; set; }
            public double Arg3 { get; set; }
        }

        private struct MethodArgsStruct
        {
            public int Arg1 { get; set; }
            public string Arg2 { get; set; }
            public double Arg3 { get; set; }
        }
    }
}
