/*using Phoenix.ProjectSS.Utilities;

namespace UnitTests;

[TestFixture]
public class TimeTests
{
    [SetUp]
    public void Setup()
    {
    }

    [Test]
    public void TimeStampTest()
    {
        long timeStamp = DateTimeHelper.GetTimestampSeconds(DateTime.UtcNow.AddSeconds(3600));
        DateTime date1 = DateTimeHelper.GetUtcDateTimeFromTimestampSeconds(timeStamp);


        DateTime date = DateTime.Now;
        DateTime dateUtc = DateTime.UtcNow;
        long localTms = DateTimeHelper.GetTimestampMilliseconds(date);
        long utcTms = DateTimeHelper.GetTimestampMilliseconds(dateUtc);

        Assert.That(utcTms, Is.EqualTo(localTms));

        DateTime dateUtc2 = DateTimeHelper.GetUtcDateTimeFromTimestampMilliseconds(localTms);

        DateTime utcNow = DateTime.UtcNow;
        DateTime t1 = utcNow.GetInitialSecondTime();
        DateTime now = DateTime.Now;
        DateTime t2 = now.GetInitialSecondTime();

        Assert.That(utcNow.Ticks / TimeSpan.TicksPerSecond * TimeSpan.TicksPerSecond, Is.EqualTo(t1.Ticks));
        Assert.That(now.Ticks / TimeSpan.TicksPerSecond * TimeSpan.TicksPerSecond, Is.EqualTo(t2.Ticks));
        Console.WriteLine("{0:yyyy-MM-dd HH:mm:ss.fff}", utcNow);
        Console.WriteLine("{0:yyyy-MM-dd HH:mm:ss.fff}", t1);

        Assert.That(dateUtc.Year, Is.EqualTo(dateUtc2.Year));
        Assert.That(dateUtc.Month, Is.EqualTo(dateUtc2.Month));
        Assert.That(dateUtc.Day, Is.EqualTo(dateUtc2.Day));
        Assert.That(dateUtc.Hour, Is.EqualTo(dateUtc2.Hour));
        Assert.That(dateUtc.Minute, Is.EqualTo(dateUtc2.Minute));
        Assert.That(dateUtc.Second, Is.EqualTo(dateUtc2.Second));
    }
}
*/
