/*// TypeRegistry.cs
using MessagePack;
using MessagePack.Formatters;
using MessagePack.Resolvers;
using Mos.MsgPackLogic.Protocol;
using System.Buffers;
*//*public static class TypeRegistry
{
    private static readonly ConcurrentDictionary<Type, int> _typeToId = new();
    private static readonly ConcurrentDictionary<int, Type> _idToType = new();

    public static bool TryRegister(Type type, int typeId)
    {
        return _typeToId.TryAdd(type, typeId) &&
               _idToType.TryAdd(typeId, type);
    }

    public static bool TryGetTypeId(Type type, out int typeId)
        => _typeToId.TryGetValue(type, out typeId);

    public static bool TryGetType(int typeId, out Type type)
        => _idToType.TryGetValue(typeId, out type);
}*//*

// UniversalObjectFormatter.cs


public sealed class UniversalObjectFormatter : IMessagePackFormatter<object>
{
    public void Serialize(ref MessagePackWriter writer, object value, MessagePackSerializerOptions options)
    {
        if (value == null)
        {
            writer.WriteNil();
            return;
        }

        switch (value)
        {
            case int i: writer.Write(i); break;
            case string s: writer.Write(s); break;
            case bool b: writer.Write(b); break;
            case float f: writer.Write(f); break;
            case byte[] bs: writer.Write(bs); break;
            case double d: writer.Write(d); break;
            case List<object> list:
                new ObjectListFormatter().Serialize(ref writer, list, options);
                break;
            *//*case Dictionary<object, object> dict:
                writer.WriteMapHeader(dict.Count);
                foreach (var kvp in dict)
                {
                    // 序列化Key
                    MessagePackSerializer.Serialize<object>(ref writer, kvp.Key, options);

                    // 处理特殊类型标记
                    if (kvp.Value is MsgPackStructBase typeValue)
                    {
                        *//*if (!TypeRegistry.TryGetTypeId(typeValue, out var typeId))
                            throw new MessagePackSerializationException($"Unregistered type: {typeValue.Name}");*//*

                        writer.WriteMapHeader(2);
                        writer.Write("__type");
                        writer.Write(typeId);
                        writer.Write("data");
                        MessagePackSerializer.Serialize(typeValue, ref writer, kvp.Value, options);
                    }
                    else
                    {
                        MessagePackSerializer.Serialize<object>(ref writer, kvp.Value, options);
                    }
                }
                break;*//*
            case MsgPackStructBase:
                {
                    writer.Flush();
                    var msg = value as MsgPackStructBase;
                    var buffer = new ArrayBufferWriter<byte>();
                    var msg_writer = new MessagePackWriter(buffer);
                    msg_writer.WriteInt32((int)msg.ProtoCode);
                    var data = MsgPackProtoHelper.Serialize((int)msg.ProtoCode, msg);
                    msg_writer.Write(data);
                    msg_writer.Flush();
                    writer.WriteExtensionFormatHeader(new ExtensionHeader(122, buffer.WrittenCount));
                    writer.WriteRaw(buffer.WrittenSpan);
                    writer.Flush();
                }
                break;
            default:
                throw new MessagePackSerializationException($"Unsupported type: {value.GetType().Name}");
                *//*if (!TypeRegistry.TryGetTypeId(value.GetType(), out var typeId))
                    throw new MessagePackSerializationException($"Unregistered type: {value.GetType().Name}");                
                writer.WriteArrayHeader(2);
                writer.Write(typeId);
                MessagePackSerializer.Serialize(value.GetType(), ref writer, value, options);
                break;*//*
        }
    }

    public object Deserialize(ref MessagePackReader reader, MessagePackSerializerOptions options)
    {
        if (reader.TryReadNil()) return null;

        switch (reader.NextMessagePackType)
        {
            case MessagePackType.Integer:
                return reader.ReadInt32();
            case MessagePackType.String:
                return reader.ReadString();
            case MessagePackType.Boolean:
                return reader.ReadBoolean();
            case MessagePackType.Float:
                return reader.ReadDouble();
            case MessagePackType.Array:
                return ReadTypedArray(ref reader, options);
            case MessagePackType.Extension:
                {
                    var header = reader.ReadExtensionFormatHeader();
                    if (header.TypeCode == 122)
                    {
                        int protoCode = reader.ReadInt32();
                        var data = reader.ReadBytes();
                        return MsgPackProtoHelper.Deserialize(protoCode, data.Value.ToArray<byte>());
                        *//*                        Assert.IsTrue(data != null);
                                                return data;*//*
                    }
                    return reader.ReadExtensionFormat();
                }

            case MessagePackType.Unknown:
                return null;
                break;
            case MessagePackType.Nil:
                return null;
            case MessagePackType.Binary:
                return reader.ReadBytes();
            case MessagePackType.Map:
*//*                return ReadMapTyped(ref reader, options);*//*
            default:
                throw new MessagePackSerializationException($"Unsupported type: {reader.NextMessagePackType}");
        }

        *//*return reader.NextMessagePackType switch
        {
            MessagePackType.Integer => reader.ReadInt32(),
            MessagePackType.String => reader.ReadString(),
            MessagePackType.Boolean => reader.ReadBoolean(),
            MessagePackType.Float => reader.ReadDouble(),
            MessagePackType.Array => ReadTypedArray(ref reader, options),
            _ => throw new MessagePackSerializationException($"Unsupported type: {reader.NextMessagePackType}")
        };*//*
    }

    *//*private object ReadMapTyped(ref MessagePackReader reader, MessagePackSerializerOptions options)
    {
        var mapCount = reader.ReadMapHeader();
        var dictionary = new Dictionary<object, object>(mapCount);

        for (var i = 0; i < mapCount; i++)
        {
            // 反序列化Key
            var key = Deserialize(ref reader, options);

            // 特殊处理类型标记
            if (key is string keyStr && keyStr == "__type")
            {
                // 读取类型元数据
                var typeId = (int)Deserialize(ref reader, options);
                var data = Deserialize(ref reader, options);

                if (TypeRegistry.TryGetType(typeId, out var type))
                {
                    // 创建动态字典实例
                    var dynamicDict = new Dictionary<object, object>
                    {
                        ["__type"] = typeId,
                        ["data"] = data
                    };
                    return MessagePackSerializer.Deserialize(type,
                        MessagePackSerializer.Serialize(dynamicDict, options),
                        options);
                }
                throw new MessagePackSerializationException($"Unknown type ID: {typeId}");
            }
            else
            {
                // 常规值反序列化
                var value = Deserialize(ref reader, options);
                dictionary[key] = value;
            }
        }

        return dictionary;
    }*//*

    private object ReadTypedArray(ref MessagePackReader reader, MessagePackSerializerOptions options)
    {
        var count = reader.ReadArrayHeader();
        *//* if (count == 2)
        {
            var typeId = reader.ReadInt32();
            if (!TypeRegistry.TryGetType(typeId, out var type))
                throw new MessagePackSerializationException($"Unknown type ID: {typeId}");

            return MessagePackSerializer.Deserialize(type, ref reader, options);
        }*//*

        var list = new List<object>();
        for (int i = 0; i < count; i++)
            list.Add(Deserialize(ref reader, options));
        return list;
    }
}

// ObjectListFormatter.cs


public class ObjectListFormatter : IMessagePackFormatter<List<object>>
{
    public void Serialize(ref MessagePackWriter writer, List<object> value, MessagePackSerializerOptions options)
    {
        if (value == null)
        {
            writer.WriteNil();
            return;
        }

        writer.WriteArrayHeader(value.Count);
        foreach (var item in value)
            MessagePackSerializer.Serialize<object>(ref writer, item, options);
    }

    public List<object> Deserialize(ref MessagePackReader reader, MessagePackSerializerOptions options)
    {
        if (reader.TryReadNil()) return null;

        var count = reader.ReadArrayHeader();
        var list = new List<object>(count);
        for (int i = 0; i < count; i++)
            list.Add(MessagePackSerializer.Deserialize<object>(ref reader, options));
        return list;
    }
}

// CustomResolver.cs



public class CustomResolver : IFormatterResolver
{
    // 使用正确的 CompositeResolver 构建方式
    public static readonly IFormatterResolver Instance = CompositeResolver.Create(
        new IMessagePackFormatter[]
        {
            new UniversalObjectFormatter(),
            // new DynamicDictionaryFormatter(),
            new ObjectListFormatter()
        },
        new IFormatterResolver[]
        {
            StandardResolver.Instance,
            DynamicContractlessObjectResolver.Instance
        });

    public IMessagePackFormatter<T> GetFormatter<T>() => Instance.GetFormatter<T>();
}


// MessagePackConfig.cs

public static class MessagePackConfig
{
    public static readonly MessagePackSerializerOptions Options =
        MessagePackSerializerOptions.Standard
            .WithResolver(CustomResolver.Instance);
    //.WithCompression(MessagePackCompression.Lz4Block);
}



[TestFixture]
public class MessagePackSerializationTests
{
    [SetUp]
    public void Setup()
    {
        // 注册测试用自定义类型
        // TypeRegistry.TryRegister(typeof(TestEntity), 1001);
        // TypeRegistry.TryRegister(typeof(NestedData), 1002);
    }

    [Test]
    public void Should_Serialize_PrimitiveTypes_Correctly()
    {
        // Arrange
        var testData = new List<object> { 42, "hello", true, 3.14 };

        // Act
        var bytes = MessagePackSerializer.Serialize(testData, MessagePackConfig.Options);
        var result = MessagePackSerializer.Deserialize<List<object>>(bytes, MessagePackConfig.Options);

        // Assert
        Assert.That(result[0], Is.EqualTo(42).And.TypeOf<int>());
        Assert.That(result[1], Is.EqualTo("hello").And.TypeOf<string>());
        Assert.That(result[2], Is.EqualTo(true).And.TypeOf<bool>());
        Assert.That(result[3], Is.EqualTo(3.14).Within(0.001).And.TypeOf<double>());
    }

    [Test]
    public void Should_Handle_CustomObjects()
    {
        // Arrange
        var original = new TestEntity(123, "Test Object");

        // Act
        var bytes = MessagePackSerializer.Serialize(original, MessagePackConfig.Options);
        var result = MessagePackSerializer.Deserialize<TestEntity>(bytes, MessagePackConfig.Options);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result.Id, Is.EqualTo(original.Id));
            Assert.That(result.Name, Is.EqualTo(original.Name));
        });
    }

    [Test]
    public void Should_Serialize_MixedTypeList()
    {
        MsgPackProtoHelper.Instance.Init();
        // Arrange
        var original = new List<object>
        {
            42,
            "text",
            12.345f,
            99999L,
            new byte[] { 0x12, 0x34, 0x56 },
            new MsgPack_LoginByAuthToken_Req(),
            new MsgPack_S_NodeInfoListProto
            {
                NodeInfoList = new List<MsgPack_S_ServerNodeInfoProto>
                {
                    new MsgPack_S_ServerNodeInfoProto
                    {
                        NodeId = 11111,
                        Role = "xxxxx"
                    }
                }
            },
            new List<object>
            {
                new MsgPack_S_NodesReadyProto(),
            }
            // new TestEntity(456, "Nested Object"),
            // new NestedData(DateTime.UtcNow, 9.99),
            *//*new List<NestedData>
            {
                new NestedData(DateTime.UtcNow,19.99),
            }*/
/*new List<object>
{
    new NestedData(DateTime.UtcNow, 9.99),
    false
}*//*
};

// Act
var bytes = MessagePackSerializer.Serialize(original, MessagePackConfig.Options);
var reader = new MessagePackReader(bytes);
var result = MessagePackSerializer.Deserialize<List<object>>(bytes, MessagePackConfig.Options);

// Assert
Assert.That(result, Has.Count.EqualTo(4));
Assert.That(result[0], Is.TypeOf<int>());
Assert.That(result[1], Is.TypeOf<string>());
Assert.That(result[2], Is.TypeOf<TestEntity>());
Assert.That(result[3], Is.TypeOf<List<object>>());

var nestedList = result[3] as List<object>;
Assert.That(nestedList[0], Is.TypeOf<NestedData>());
Assert.That(nestedList[1], Is.TypeOf<bool>());
}

[Test]
public void Should_Throw_On_UnregisteredType()
{
// Arrange
var invalidData = new List<object> { new UnregisteredType() };

// Act & Assert
var ex = Assert.Throws<MessagePackSerializationException>(() =>
MessagePackSerializer.Serialize(invalidData, MessagePackConfig.Options));

Assert.That(ex.Message, Does.Contain("Unregistered type"));
}

[Test]
public void Should_Handle_NullValues()
{
// Arrange
var original = new List<object> { null, "not null", null };

// Act
var bytes = MessagePackSerializer.Serialize(original, MessagePackConfig.Options);
var result = MessagePackSerializer.Deserialize<List<object>>(bytes, MessagePackConfig.Options);

// Assert
Assert.That(result, Has.Count.EqualTo(3));
Assert.That(result[0], Is.Null);
Assert.That(result[1], Is.EqualTo("not null"));
Assert.That(result[2], Is.Null);
}

// 测试用类型定义
[MessagePackObject]
public class TestEntity
{
[Key(0)]
public int Id { get; }

[Key(1)]
public string Name { get; }

public TestEntity(int id, string name) => (Id, Name) = (id, name);
}

[MessagePackObject]
public class NestedData
{
[Key(0)]
public DateTime Timestamp { get; }

[Key(1)]
public double Value { get; }

public NestedData(DateTime timestamp, double value) =>
(Timestamp, Value) = (timestamp, value);
}

private class UnregisteredType { }
}
*/
