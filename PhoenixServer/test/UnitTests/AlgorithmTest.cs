/*using Phoenix.ProjectSS.Utilities;

namespace UnitTests;

[TestFixture]
public class AlgorithmTests
{
    [SetUp]
    public void Setup()
    {
    }

    [Test]
    public void RandomUIntSequenceUniqueTest()
    {
        Dictionary<uint, int> results = new();
        for (uint exclusiveMax = 1u; exclusiveMax <= 65536; exclusiveMax++)
        {
            foreach (uint n in Algorithm.RandomUIntSequence(exclusiveMax))
            {
                try
                {
                    results.Add(n, 0);
                }
                catch (ArgumentException e)
                {
                    Assert.Fail(e.Message);
                }
            }

            Assert.That(exclusiveMax, Is.EqualTo(results.Count));
            results.Clear();
        }
    }

    [Test]
    public void RandomUIntSequenceRandomnessTest()
    {
        //Dictionary<uint, int> results = new();
        //var totalTrails = 100000000;
        //for (var exclusiveMax = 3u; exclusiveMax <= 32; exclusiveMax++)
        //{
        //    for (var trials = 0; trials < totalTrails; ++trials)
        //    {
        //        foreach (var n in Algorithm.RandomUIntSequence(exclusiveMax))
        //        {
        //            if (results.ContainsKey(n))
        //            {
        //                results[n]++;
        //            }
        //            else
        //            {
        //                results.Add(n, 1);
        //            }

        //            break;
        //        }
        //    }

        //    foreach (var kv in results)
        //    {
        //        if (!(0.95 * totalTrails / exclusiveMax <= kv.Value && kv.Value <= 1.05 * totalTrails / exclusiveMax))
        //        {
        //            foreach (var kv2 in results)
        //            {
        //                Console.WriteLine($"{kv2.Key}: {kv2.Value}. error = {kv2.Value / (1.0 * totalTrails / exclusiveMax) - 1}");
        //            }
        //        }

        //        Assert.That(kv.Value, Is.AtMost(1.05 * totalTrails / exclusiveMax));
        //        Assert.That(kv.Value, Is.AtLeast(0.95 * totalTrails / exclusiveMax));
        //    }
        //    //Console.WriteLine("========================");
        //    results.Clear();
        //}
    }

    [Test]
    public void RandomItemFromSequenceTest()
    {
        var randomItems = new List<Tuple<RandomItem, int>>();
        {
            RandomItem? item = randomItems.RandomWeightedItemFromSequence();
            Assert.That(item, Is.EqualTo(null));
        }

        foreach (int i in Enumerable.Range(1, 10))
        {
            var tuple = new Tuple<RandomItem, int>(new RandomItem {Index = i}, i * 1000);
            randomItems.Add(tuple);
        }

        for (int i = 0; i < 10000; i++)
        {
            RandomItem? item = randomItems.RandomWeightedItemFromSequence();
            Assert.That(item, Is.Not.EqualTo(null));
        }
    }

    private class RandomItem
    {
        public int Index { get; set; }
    }
}
*/
