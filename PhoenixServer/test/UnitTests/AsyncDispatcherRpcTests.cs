using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using NUnit.Framework;
using Phoenix.Server.Common;

namespace UnitTests
{
    [TestFixture]
    public class AsyncDispatcherRpcTests
    {
        private AsyncDispatcher m_dispatcher;

        [SetUp]
        public void Setup()
        {
            m_dispatcher = new AsyncDispatcher();
            m_dispatcher.Start();
        }

        [TearDown]
        public void TearDown()
        {
            m_dispatcher.Stop();
        }

        [Test]
        public void TestRpcScenarios()
        {
            // 测试1: 模拟 RPC 调用 - 使用闭包传递参数
            TestContext.WriteLine("Test 1: Simulating RPC calls with closures");
            int completedTasks = 0;
            var completionEvent = new ManualResetEventSlim(false);
            var stopwatch = Stopwatch.StartNew();

            for (int i = 0; i < 10000; i++)
            {
                long playerId = 1000000 + i;
                string sessionId = $"session_{i}";
                bool relogin = i % 2 == 0;

                // 模拟 Post(() => ReqBindGate(reqInfo, playerId, sessionId, ip, loginKey, relogin))
                m_dispatcher.Post(() =>
                {
                    SimulateRpcCall(playerId, sessionId, relogin);

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 10000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            bool completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {10000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");
            TestContext.WriteLine($"Average time per task: {stopwatch.ElapsedMilliseconds * 1.0 / 10000:N3} ms");

            // 测试2: 使用对象封装 RPC 参数
            TestContext.WriteLine("\nTest 2: Using object to encapsulate RPC parameters");
            completedTasks = 0;
            completionEvent.Reset();
            stopwatch.Restart();

            for (int i = 0; i < 10000; i++)
            {
                var rpcArgs = new RpcArgs
                {
                    PlayerId = 1000000 + i,
                    SessionId = $"session_{i}",
                    Relogin = i % 2 == 0
                };

                m_dispatcher.Post(() =>
                {
                    SimulateRpcCallWithObject(rpcArgs);

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 10000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {10000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");
            TestContext.WriteLine($"Average time per task: {stopwatch.ElapsedMilliseconds * 1.0 / 10000:N3} ms");

            // 测试3: 使用结构体封装 RPC 参数
            TestContext.WriteLine("\nTest 3: Using struct to encapsulate RPC parameters");
            completedTasks = 0;
            completionEvent.Reset();
            stopwatch.Restart();

            for (int i = 0; i < 10000; i++)
            {
                var rpcArgs = new RpcArgsStruct
                {
                    PlayerId = 1000000 + i,
                    SessionId = $"session_{i}",
                    Relogin = i % 2 == 0
                };

                m_dispatcher.Post(() =>
                {
                    SimulateRpcCallWithStruct(rpcArgs);

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 10000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {10000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");
            TestContext.WriteLine($"Average time per task: {stopwatch.ElapsedMilliseconds * 1.0 / 10000:N3} ms");
        }

        [Test]
        public void TestRpcWithDifferentPayloadSizes()
        {
            // 测试1: 小型 RPC 负载
            TestContext.WriteLine("Test 1: Small RPC payload");
            int completedTasks = 0;
            var completionEvent = new ManualResetEventSlim(false);
            var stopwatch = Stopwatch.StartNew();

            for (int i = 0; i < 10000; i++)
            {
                long playerId = 1000000 + i;
                string sessionId = $"session_{i}";
                bool relogin = i % 2 == 0;

                m_dispatcher.Post(() =>
                {
                    SimulateRpcCall(playerId, sessionId, relogin);

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 10000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            bool completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {10000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");

            // 测试2: 中型 RPC 负载
            TestContext.WriteLine("\nTest 2: Medium RPC payload");
            completedTasks = 0;
            completionEvent.Reset();
            stopwatch.Restart();

            for (int i = 0; i < 10000; i++)
            {
                long playerId = 1000000 + i;
                string sessionId = $"session_{i}";
                bool relogin = i % 2 == 0;
                string[] additionalData = new string[10]; // 中等大小的数据
                for (int j = 0; j < additionalData.Length; j++)
                {
                    additionalData[j] = $"data_{i}_{j}";
                }

                m_dispatcher.Post(() =>
                {
                    SimulateRpcCallWithMediumPayload(playerId, sessionId, relogin, additionalData);

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 10000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {10000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");

            // 测试3: 大型 RPC 负载
            TestContext.WriteLine("\nTest 3: Large RPC payload");
            completedTasks = 0;
            completionEvent.Reset();
            stopwatch.Restart();

            for (int i = 0; i < 10000; i++)
            {
                long playerId = 1000000 + i;
                string sessionId = $"session_{i}";
                bool relogin = i % 2 == 0;
                string[] additionalData = new string[100]; // 大型数据
                for (int j = 0; j < additionalData.Length; j++)
                {
                    additionalData[j] = $"data_{i}_{j}_with_more_content_to_make_it_larger";
                }

                m_dispatcher.Post(() =>
                {
                    SimulateRpcCallWithLargePayload(playerId, sessionId, relogin, additionalData);

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 10000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {10000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");
        }

        [Test]
        public void TestRpcWithHighConcurrency()
        {
            // 准备
            int clientCount = 100; // 模拟100个客户端
            int requestsPerClient = 1000; // 每个客户端发送1000个请求
            int totalRequests = clientCount * requestsPerClient;
            int completedTasks = 0;
            var completionEvent = new ManualResetEventSlim(false);
            var stopwatch = new Stopwatch();

            // 执行
            stopwatch.Start();

            // 创建多个客户端线程同时发送请求
            var clients = new Task[clientCount];
            for (int c = 0; c < clientCount; c++)
            {
                int clientId = c;
                clients[c] = Task.Run(() =>
                {
                    for (int i = 0; i < requestsPerClient; i++)
                    {
                        long playerId = 1000000 + clientId * 10000 + i;
                        string sessionId = $"session_{clientId}_{i}";
                        bool relogin = i % 2 == 0;

                        m_dispatcher.Post(() =>
                        {
                            SimulateRpcCall(playerId, sessionId, relogin);

                            int current = Interlocked.Increment(ref completedTasks);
                            if (current == totalRequests)
                            {
                                completionEvent.Set();
                            }
                        });
                    }
                });
            }

            // 等待所有客户端完成请求发送
            Task.WaitAll(clients);

            // 等待所有任务完成
            bool completed = completionEvent.Wait(TimeSpan.FromMinutes(5));
            stopwatch.Stop();

            // 验证
            Assert.IsTrue(completed, "Not all tasks completed in time");
            Assert.AreEqual(totalRequests, completedTasks);

            // 输出性能指标
            TestContext.WriteLine($"High Concurrency RPC Test:");
            TestContext.WriteLine($"Clients: {clientCount}");
            TestContext.WriteLine($"Requests per client: {requestsPerClient}");
            TestContext.WriteLine($"Total requests: {totalRequests}");
            TestContext.WriteLine($"Total time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Requests per second: {totalRequests * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");
            TestContext.WriteLine($"Average time per request: {stopwatch.ElapsedMilliseconds * 1.0 / totalRequests:N3} ms");
        }

        [Test]
        public void TestOptimizedRpcPatterns()
        {
            // 测试1: 标准闭包方式
            TestContext.WriteLine("Test 1: Standard closure approach");
            int completedTasks = 0;
            var completionEvent = new ManualResetEventSlim(false);
            var stopwatch = Stopwatch.StartNew();

            for (int i = 0; i < 10000; i++)
            {
                long playerId = 1000000 + i;
                string sessionId = $"session_{i}";
                bool relogin = i % 2 == 0;

                m_dispatcher.Post(() =>
                {
                    SimulateRpcCall(playerId, sessionId, relogin);

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 10000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            bool completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {10000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");

            // 测试2: 使用对象池优化
            TestContext.WriteLine("\nTest 2: Using object pool optimization");
            completedTasks = 0;
            completionEvent.Reset();
            stopwatch.Restart();

            // 创建一个简单的对象池
            var argsPool = new ConcurrentBag<RpcArgs>();
            for (int i = 0; i < 1000; i++) // 预先创建1000个对象
            {
                argsPool.Add(new RpcArgs());
            }

            for (int i = 0; i < 10000; i++)
            {
                // 尝试从池中获取对象
                if (!argsPool.TryTake(out var args))
                {
                    args = new RpcArgs();
                }

                // 设置状态
                args.PlayerId = 1000000 + i;
                args.SessionId = $"session_{i}";
                args.Relogin = i % 2 == 0;

                // 捕获状态对象
                var capturedArgs = args;

                m_dispatcher.Post(() =>
                {
                    SimulateRpcCallWithObject(capturedArgs);

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 10000)
                    {
                        completionEvent.Set();
                    }

                    // 使用完后返回池中
                    argsPool.Add(capturedArgs);
                });
            }

            completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {10000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");

            // 测试3: 使用值类型结构体
            TestContext.WriteLine("\nTest 3: Using value type struct");
            completedTasks = 0;
            completionEvent.Reset();
            stopwatch.Restart();

            for (int i = 0; i < 10000; i++)
            {
                var args = new RpcArgsStruct
                {
                    PlayerId = 1000000 + i,
                    SessionId = $"session_{i}",
                    Relogin = i % 2 == 0
                };

                m_dispatcher.Post(() =>
                {
                    SimulateRpcCallWithStruct(args);

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 10000)
                    {
                        completionEvent.Set();
                    }
                });
            }

            completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {10000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");

            // 测试4: 使用预分配的委托
            TestContext.WriteLine("\nTest 4: Using pre-allocated delegates");
            completedTasks = 0;
            completionEvent.Reset();
            stopwatch.Restart();

            // 预分配一些委托
            var delegatePool = new ConcurrentBag<Action>();
            for (int i = 0; i < 1000; i++)
            {
                var args = new RpcArgs();
                delegatePool.Add(() =>
                {
                    SimulateRpcCallWithObject(args);
                });
            }

            for (int i = 0; i < 10000; i++)
            {
                // 尝试从池中获取委托
                if (!delegatePool.TryTake(out var action))
                {
                    action = () => SimulateRpcCallWithObject(new RpcArgs());
                }

                // 设置参数
                if (action.Target is RpcArgs args)
                {
                    args.PlayerId = 1000000 + i;
                    args.SessionId = $"session_{i}";
                    args.Relogin = i % 2 == 0;
                }

                m_dispatcher.Post(() =>
                {
                    action();

                    int current = Interlocked.Increment(ref completedTasks);
                    if (current == 10000)
                    {
                        completionEvent.Set();
                    }

                    // 使用完后返回池中
                    delegatePool.Add(action);
                });
            }

            completed = completionEvent.Wait(TimeSpan.FromMinutes(2));
            stopwatch.Stop();

            Assert.IsTrue(completed, "Not all tasks completed in time");
            TestContext.WriteLine($"Time: {stopwatch.ElapsedMilliseconds} ms");
            TestContext.WriteLine($"Tasks per second: {10000 * 1000.0 / stopwatch.ElapsedMilliseconds:N0}");
        }

        // 模拟 RPC 调用的方法
        private void SimulateRpcCall(long playerId, string sessionId, bool relogin)
        {
            // 模拟 RPC 处理逻辑
            Thread.Sleep(1); // 模拟一些处理时间
        }

        private void SimulateRpcCallWithObject(RpcArgs args)
        {
            // 与 SimulateRpcCall 相同的逻辑，但使用对象
            Thread.Sleep(1);
        }

        private void SimulateRpcCallWithStruct(RpcArgsStruct args)
        {
            // 与 SimulateRpcCall 相同的逻辑，但使用结构体
            Thread.Sleep(1);
        }

        private void SimulateRpcCallWithMediumPayload(long playerId, string sessionId, bool relogin, string[] additionalData)
        {
            // 模拟处理中等大小的负载
            Thread.Sleep(2); // 模拟更长的处理时间

            // 使用额外数据
            for (int i = 0; i < additionalData.Length; i++)
            {
                string temp = additionalData[i] + "_processed";
            }
        }

        private void SimulateRpcCallWithLargePayload(long playerId, string sessionId, bool relogin, string[] additionalData)
        {
            // 模拟处理大型负载
            Thread.Sleep(5); // 模拟更长的处理时间

            // 使用额外数据
            for (int i = 0; i < additionalData.Length; i++)
            {
                string temp = additionalData[i] + "_processed_with_more_operations";
            }
        }

        // 用于测试的类型
        private class RpcArgs
        {
            public long PlayerId { get; set; }
            public string SessionId { get; set; }
            public bool Relogin { get; set; }
        }

        private struct RpcArgsStruct
        {
            public long PlayerId { get; set; }
            public string SessionId { get; set; }
            public bool Relogin { get; set; }
        }
    }
}
