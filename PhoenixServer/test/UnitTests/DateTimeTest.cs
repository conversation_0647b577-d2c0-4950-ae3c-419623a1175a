/*using Phoenix.ProjectSS.Utilities;

namespace UnitTests;

[TestFixture]
public class DateTimeTests
{
    [SetUp]
    public void Setup()
    {
    }

    [Test]
    public void MomentsBetweenTest()
    {
        DateTime d1 = new(2023, 4, 10, 10, 23, 51);
        DateTime d2 = new(2023, 4, 10, 10, 23, 52);

        int moments = DateTimeHelper.MomentsBetweenDateTime(d1, d2, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 10, 10, 23, 51);
        d2 = new DateTime(2023, 4, 11, 10, 23, 51);

        moments = DateTimeHelper.MomentsBetweenDateTime(d1, d2, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 10, 10, 23, 50);
        d2 = new DateTime(2023, 4, 11, 10, 23, 51);

        moments = DateTimeHelper.MomentsBetweenDateTime(d1, d2, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 10, 10, 23, 52);
        d2 = new DateTime(2023, 4, 11, 10, 23, 51);

        moments = DateTimeHelper.MomentsBetweenDateTime(d1, d2, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(0));

        d1 = new DateTime(2023, 4, 10, 10, 23, 52);
        d2 = new DateTime(2023, 4, 11, 10, 23, 52);

        moments = DateTimeHelper.MomentsBetweenDateTime(d1, d2, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 10, 10, 23, 51);
        d2 = new DateTime(2023, 4, 11, 10, 23, 52);

        moments = DateTimeHelper.MomentsBetweenDateTime(d1, d2, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(2));


        d1 = new DateTime(2023, 4, 10, 0, 0, 0);
        d2 = new DateTime(2023, 4, 11, 0, 0, 0);

        moments = DateTimeHelper.MomentsBetweenDateTime(d1, d2, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 9, 23, 59, 59);
        d2 = new DateTime(2023, 4, 11, 0, 0, 0);

        moments = DateTimeHelper.MomentsBetweenDateTime(d1, d2, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(1));
    }

    [Test]
    public void WeekdayMomentsBetweenTest()
    {
        DateTime d1 = new(2023, 4, 10, 0, 0, 0); // DayOfWeek.Monday;
        DateTime d2 = new(2023, 4, 11, 0, 0, 0);
        DayOfWeek day = DayOfWeek.Monday;
        int moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 0, 0, 0);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 10, 0, 0, 0); // DayOfWeek.Monday;
        d2 = new DateTime(2023, 4, 17, 0, 0, 0);
        day = DayOfWeek.Monday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 0, 0, 0);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 10, 0, 0, 0); // DayOfWeek.Monday;
        d2 = new DateTime(2023, 4, 18, 0, 0, 0);
        day = DayOfWeek.Monday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 0, 0, 0);
        Assert.That(moments, Is.EqualTo(2));

        d1 = new DateTime(2023, 4, 10, 0, 0, 0); // DayOfWeek.Monday;
        d2 = new DateTime(2023, 4, 18, 0, 0, 0);
        day = DayOfWeek.Tuesday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 0, 0, 0);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 10, 0, 0, 0); // DayOfWeek.Monday;
        d2 = new DateTime(2023, 4, 19, 0, 0, 0);
        day = DayOfWeek.Tuesday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 0, 0, 0);
        Assert.That(moments, Is.EqualTo(2));

        d1 = new DateTime(2023, 4, 10, 0, 0, 0); // DayOfWeek.Monday;
        d2 = new DateTime(2023, 4, 26, 0, 0, 0);
        day = DayOfWeek.Tuesday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 0, 0, 0);
        Assert.That(moments, Is.EqualTo(3));

        d1 = new DateTime(2023, 4, 15, 0, 0, 0); // DayOfWeek.Saturday;
        d2 = new DateTime(2023, 4, 16, 0, 0, 0);
        day = DayOfWeek.Saturday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 0, 0, 0);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 15, 0, 0, 0); // DayOfWeek.Saturday;
        d2 = new DateTime(2023, 4, 16, 0, 0, 0);
        day = DayOfWeek.Sunday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 0, 0, 0);
        Assert.That(moments, Is.EqualTo(0));

        d1 = new DateTime(2023, 4, 15, 0, 0, 0); // DayOfWeek.Saturday;
        d2 = new DateTime(2023, 4, 17, 0, 0, 0);
        day = DayOfWeek.Sunday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 0, 0, 0);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 10, 0, 0, 0); // DayOfWeek.Monday;
        d2 = new DateTime(2023, 4, 11, 0, 0, 0);
        day = DayOfWeek.Monday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 10, 10, 23, 51); // DayOfWeek.Monday;
        d2 = new DateTime(2023, 4, 11, 0, 0, 0);
        day = DayOfWeek.Monday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 10, 10, 23, 52); // DayOfWeek.Monday;
        d2 = new DateTime(2023, 4, 11, 0, 0, 0);
        day = DayOfWeek.Monday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(0));

        d1 = new DateTime(2023, 4, 10, 10, 23, 51); // DayOfWeek.Monday;
        d2 = new DateTime(2023, 4, 17, 10, 23, 51);
        day = DayOfWeek.Monday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 10, 10, 23, 51); // DayOfWeek.Monday;
        d2 = new DateTime(2023, 4, 17, 10, 23, 52);
        day = DayOfWeek.Monday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(2));

        d1 = new DateTime(2023, 4, 10, 10, 23, 52); // DayOfWeek.Monday;
        d2 = new DateTime(2023, 4, 17, 10, 23, 51);
        day = DayOfWeek.Monday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(0));

        d1 = new DateTime(2023, 4, 3, 10, 23, 52); // DayOfWeek.Monday;
        d2 = new DateTime(2023, 4, 17, 10, 23, 51);
        day = DayOfWeek.Monday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 3, 10, 23, 52); // DayOfWeek.Monday;
        d2 = new DateTime(2023, 4, 17, 10, 23, 52);
        day = DayOfWeek.Monday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(2));

        d1 = new DateTime(2023, 4, 3, 10, 23, 51); // DayOfWeek.Monday;
        d2 = new DateTime(2023, 4, 17, 10, 23, 52);
        day = DayOfWeek.Monday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(3));


        d1 = new DateTime(2023, 4, 15, 10, 23, 52); // DayOfWeek.Saturday;
        d2 = new DateTime(2023, 4, 23, 10, 23, 51);
        day = DayOfWeek.Sunday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(1));

        d1 = new DateTime(2023, 4, 8, 10, 23, 52); // DayOfWeek.Saturday;
        d2 = new DateTime(2023, 4, 23, 10, 23, 51);
        day = DayOfWeek.Sunday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(2));

        d1 = new DateTime(2023, 4, 8, 10, 23, 52); // DayOfWeek.Saturday;
        d2 = new DateTime(2023, 4, 23, 10, 23, 52);
        day = DayOfWeek.Sunday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(3));

        d1 = new DateTime(2023, 4, 8, 10, 23, 51); // DayOfWeek.Saturday;
        d2 = new DateTime(2023, 4, 23, 10, 23, 52);
        day = DayOfWeek.Sunday;
        moments = DateTimeHelper.WeekdayMomentsBetweenDateTime(d1, d2, day, 10, 23, 51);
        Assert.That(moments, Is.EqualTo(3));
    }
}
*/
