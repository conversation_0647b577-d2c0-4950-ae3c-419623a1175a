MsgPackDesc_Bag=EProtoCode.BAG_BAGINFO_REQ=7001
MsgPackDesc_Bag=EProtoCode.BAG_BAGINFO_ACK=7002
MsgPackDesc_Battle=EProtoCode.BATTLE_BATTLEVERIFY_REQ=4001
MsgPackDesc_Battle=EProtoCode.BATTLE_BATTLEVERIFY_ACK=4002
MsgPackDesc_BattlePVP=EProtoCode.BATTLEPVP_BATTLECOMMAND_REQ=6001
MsgPackDesc_BattlePVP=EProtoCode.BATTLEPVP_BATTLECOMMAND_ACK=6002
MsgPackDesc_BattlePVP=EProtoCode.BATTLEPVP_BATTLECOMMAND_NTF=6003
MsgPackDesc_Core=EProtoCode.CORE_S_CHECKCONSISTENTDIGEST_PROTO=201
MsgPackDesc_Core=EProtoCode.CORE_S_ENGINERPC_PROTO=202
MsgPackDesc_Core=EProtoCode.CORE_S_RPCCONNECT_PROTO=203
MsgPackDesc_Core=EProtoCode.CORE_S_RPCESTABLISH_PROTO=204
MsgPackDesc_Core=EProtoCode.CORE_S_GETCONSISTENTDIGEST_PROTO=205
MsgPackDesc_Core=EProtoCode.CORE_S_NODESREADY_PROTO=206
MsgPackDesc_Core=EProtoCode.CORE_S_NODEHEARTBEAT_REQ=207
MsgPackDesc_Core=EProtoCode.CORE_S_NODEHEARTBEAT_ACK=208
MsgPackDesc_Core=EProtoCode.CORE_S_NODEINFOLIST_PROTO=209
MsgPackDesc_Core=EProtoCode.CORE_S_NODEJOIN_NTF=210
MsgPackDesc_Core=EProtoCode.CORE_S_NODELEAVE_NTF=211
MsgPackDesc_Core=EProtoCode.CORE_S_NODEREADY_PROTO=212
MsgPackDesc_Core=EProtoCode.CORE_S_REGISTERMAILBOX_REQ=213
MsgPackDesc_Core=EProtoCode.CORE_S_REGISTERMAILBOX_ACK=214
MsgPackDesc_Core=EProtoCode.CORE_S_RPCBYMAILBOX_PROTO=215
MsgPackDesc_Core=EProtoCode.CORE_S_RPC_REQ=216
MsgPackDesc_Core=EProtoCode.CORE_S_RPC_ACK=217
MsgPackDesc_Core=EProtoCode.CORE_S_RPCTONODE_PROTO=218
MsgPackDesc_Core=EProtoCode.CORE_S_RPCTOPLAYER_PROTO=219
MsgPackDesc_Core=EProtoCode.CORE_S_RPCTOPLAYERROUTER_PROTO=220
MsgPackDesc_Core=EProtoCode.CORE_S_SERVERNODEINFO_PROTO=221
MsgPackDesc_Core=EProtoCode.CORE_S_TRANSUPDATECONSISTENTHASH_REQ=222
MsgPackDesc_Core=EProtoCode.CORE_S_TRANSUPDATECONSISTENTHASH_ACK=223
MsgPackDesc_Core=EProtoCode.CORE_S_UNREGISTERMAILBOX_REQ=224
MsgPackDesc_Core=EProtoCode.CORE_S_UNREGISTERMAILBOX_ACK=225
MsgPackDesc_Core=EProtoCode.CORE_S_UPDATECONSISTENTHASH_PROTO=226
MsgPackDesc_Core=EProtoCode.CORE_S_BROADCASTDATA_PROTO=227
MsgPackDesc_Duel=EProtoCode.DUEL_DUEL_REQ=5001
MsgPackDesc_Duel=EProtoCode.DUEL_DUEL_ACK=5002
MsgPackDesc_Duel=EProtoCode.DUEL_DUEL_NTF=5003
MsgPackDesc_Duel=EProtoCode.DUEL_DUELREPLY_REQ=5004
MsgPackDesc_Duel=EProtoCode.DUEL_DUELREPLY_ACK=5005
MsgPackDesc_Duel=EProtoCode.DUEL_DUELREPLY_NTF=5006
MsgPackDesc_Duel=EProtoCode.DUEL_DUELBATTLESESSIONINIT_NTF=5007
MsgPackDesc_Duel=EProtoCode.DUEL_DUELBATTLESESSIONEND_NTF=5008
MsgPackDesc_Duel=EProtoCode.DUEL_S_DUELREPLY_NTF=5009
MsgPackDesc_Duel=EProtoCode.DUEL_S_DUEL_NTF=5010
MsgPackDesc_Example=EProtoCode.EXAMPLE_EXAMPLE_REQ=1
MsgPackDesc_Example=EProtoCode.EXAMPLE_EXAMPLE_ACK=2
MsgPackDesc_Example=EProtoCode.EXAMPLE_EXAMPLEDATA_NTF=3
MsgPackDesc_Example=EProtoCode.EXAMPLE_EXAMPLEEXTERNAL_REQ=4
MsgPackDesc_Example=EProtoCode.EXAMPLE_EXAMPLEEXTERNAL_ACK=5
MsgPackDesc_GlobalSync=EProtoCode.GLOBALSYNC_HEARTBEAT_PROTO=65001
MsgPackDesc_GlobalSync=EProtoCode.GLOBALSYNC_SERVERTIME_REQ=65002
MsgPackDesc_GlobalSync=EProtoCode.GLOBALSYNC_SERVERTIME_ACK=65003
MsgPackDesc_GlobalSync=EProtoCode.GLOBALSYNC_HEARTBEAT_REQ=65004
MsgPackDesc_GlobalSync=EProtoCode.GLOBALSYNC_HEARTBEAT_ACK=65005
MsgPackDesc_Login=EProtoCode.LOGIN_LOGINBYAUTHTOKEN_REQ=801
MsgPackDesc_Login=EProtoCode.LOGIN_LOGINBYAUTHTOKEN_ACK=802
MsgPackDesc_Login=EProtoCode.LOGIN_LOGINBYSESSIONTOKEN_REQ=803
MsgPackDesc_Login=EProtoCode.LOGIN_LOGINBYSESSIONTOKEN_ACK=804
MsgPackDesc_Login=EProtoCode.LOGIN_SERVERDISCONNECT_NTF=805
MsgPackDesc_Login=EProtoCode.LOGIN_LOGOUT_REQ=806
MsgPackDesc_Login=EProtoCode.LOGIN_LOGINBYAUTHTOKENREQ=807
MsgPackDesc_Login=EProtoCode.LOGIN_LOGINBYAUTHTOKENACK=808
MsgPackDesc_Login=EProtoCode.LOGIN_LOGINBYSESSIONTOKENREQ=809
MsgPackDesc_Login=EProtoCode.LOGIN_LOGINBYSESSIONTOKENACK=810
MsgPackDesc_Login=EProtoCode.LOGIN_SERVERDISCONNECTNTF=811
MsgPackDesc_PlayerInit=EProtoCode.PLAYERINIT_PLAYERINFOINIT_REQ=1001
MsgPackDesc_PlayerInit=EProtoCode.PLAYERINIT_PLAYERINFOINIT_ACK=1002
MsgPackDesc_PlayerInit=EProtoCode.PLAYERINIT_CHARACTERCREATE_REQ=1003
MsgPackDesc_PlayerInit=EProtoCode.PLAYERINIT_CHARACTERCREATE_ACK=1004
MsgPackDesc_PlayerInit=EProtoCode.PLAYERINIT_PLAYERBASICINFODATASECTION_NTF=1005
MsgPackDesc_PlayerInit=EProtoCode.PLAYERINIT_PLAYERDATASECTIONSYNCEND_NTF=1006
MsgPackDesc_PlayerInit=EProtoCode.PLAYERINIT_PLAYERBASICCOMPDATANTF=1007
MsgPackDesc_PlayerInit=EProtoCode.PLAYERINIT_PLAYERDATASECTIONSYNCENDNTF=1008
MsgPackDesc_PlayerInit=EProtoCode.PLAYERINIT_PLAYERINFOINITREQ=1009
MsgPackDesc_PlayerInit=EProtoCode.PLAYERINIT_PLAYERINFOINITACK=1010
MsgPackDesc_PlayerInit=EProtoCode.PLAYERINIT_CHARACTERCREATEREQ=1011
MsgPackDesc_PlayerInit=EProtoCode.PLAYERINIT_CHARACTERCREATEACK=1012
MsgPackDesc_RPC=EProtoCode.RPC_S_FORWARDCLIENTMSGBROADCAST_PROTO=603
MsgPackDesc_RPC=EProtoCode.RPC_FORWARDCLIENTMESSAGETOSERVERNTF=604
MsgPackDesc_RPC=EProtoCode.RPC_FORWARDMESSAGETOCLIENTNTF=605
MsgPackDesc_SYS=EProtoCode.SYS_C_CALLBASEPLAYERMETHOD_PROTO=401
MsgPackDesc_SYS=EProtoCode.SYS_C_CALLENTITYMETHOD_PROTO=402
MsgPackDesc_SYS=EProtoCode.SYS_S_BASICENTITYCREATE_PROTO=403
MsgPackDesc_SYS=EProtoCode.SYS_S_CALLCLIENTMETHOD_PROTO=404
MsgPackDesc_SYS=EProtoCode.SYS_S_ENTITYCREAWTE_PROTO=405
MsgPackDesc_SYS=EProtoCode.SYS_S_ENTITYDESTROY_PROTO=406
MsgPackDesc_SYS=EProtoCode.SYS_S_ENTITYMIGRATE_PROTO=407
MsgPackDesc_SYS=EProtoCode.SYS_S_ENTITYMIGRATEREPLY_PROTO=408
MsgPackDesc_SYS=EProtoCode.SYS_S_FORWARDENTITYRPC_PROTO=409
MsgPackDesc_Test=EProtoCode.TEST_TEST_REQ=51
MsgPackDesc_Test=EProtoCode.TEST_TEST_ACK=52
MsgPackDesc_Test=EProtoCode.TEST_ECHO_REQ=53
MsgPackDesc_Test=EProtoCode.TEST_ECHO_ACK=54
MsgPackDesc_Word=EProtoCode.WORD_WORLDINFODATASECTION_NTF=2001
MsgPackDesc_Word=EProtoCode.WORD_PLAYERWORLDCOMPDATANTF=2002
MsgPackDesc_Hakoniwa=EProtoCode.HAKONIWA_PLAYERHAKONIWACOMPDATANTF=3001
MsgPackDesc_Hakoniwa=EProtoCode.HAKONIWA_HAKONIWAENTERREQ=3002
MsgPackDesc_Hakoniwa=EProtoCode.HAKONIWA_HAKONIWAENTERACK=3003
MsgPackDesc_Hakoniwa=EProtoCode.HAKONIWA_HAKONIWAQUITREQ=3004
MsgPackDesc_Hakoniwa=EProtoCode.HAKONIWA_HAKONIWAQUITACK=3005
MsgPackDesc_Hakoniwa=EProtoCode.HAKONIWA_HAKONIWAGIVEUPREQ=3006
MsgPackDesc_Hakoniwa=EProtoCode.HAKONIWA_HAKONIWAGIVEUPACK=3007
MsgPackDesc_Hakoniwa=EProtoCode.HAKONIWA_HAKONIWACREATEREQ=3008
MsgPackDesc_Hakoniwa=EProtoCode.HAKONIWA_HAKONIWACREATEACK=3009
