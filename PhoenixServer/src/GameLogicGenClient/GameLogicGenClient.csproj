<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>Mos.$(MSBuildProjectName)</AssemblyName>
    <RootNamespace>Mos.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
    <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
    <GeneratedFolder>Generated</GeneratedFolder>
    <!--<CompilerGeneratedFilesOutputPath>..\PropAutoGeneratedClient</CompilerGeneratedFilesOutputPath>-->
    <CompilerGeneratedFilesOutputPath>..\SourceGeneratorFiles</CompilerGeneratedFilesOutputPath>
    <!--<CompilerGeneratedFilesOutputPath>..\..\..\..\common</CompilerGeneratedFilesOutputPath>-->
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.Analyzers">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" PrivateAssets="all" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AutoScriptDesc\AutoScriptDesc.csproj" />
    <ProjectReference Include="..\PropSourceGeneratorClient\PropSourceGeneratorClient.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
  </ItemGroup>

  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <!-- <Exec Command="cd $(SolutionDir)&#xD;&#xA;call $(SolutionDir)\src\PostProcessAutoGen.bat" /> -->
  </Target>

  <!--<Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="cd $(SolutionDir)src&#xD;&#xA;call $(SolutionDir)src/PostProcessAutoGen.bat" />
  </Target>-->
</Project>
