{"Serilog": {"MinimumLevel": "Verbose", "Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File", "Serilog.Sinks.Async", "Serilog.Enrichers.Thread"], "WriteTo": [{"Name": "Async", "Args": {"configure": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss}] [{Level:u3}] [{ThreadId} {CorrelationId}] {SourceContext}: {Message:lj}{NewLine}{Exception}"}}]}}, {"Name": "Async", "Args": {"configure": [{"Name": "File", "Args": {"path": "./logs/commander_server-.log", "rollingInterval": "Hour", "retainedFileCountLimit": null, "rollOnFileSizeLimit": true, "fileSizeLimitBytes": 100000000, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{ThreadId} {CorrelationId}] {SourceContext}: {Message}{NewLine}{Exception}"}}]}}], "Enrich": ["FromLogContext", "WithThreadId"]}}