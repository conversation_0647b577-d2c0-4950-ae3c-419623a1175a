<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>Phoenix.Server.Commander</RootNamespace>
    <Configurations>Debug;Release</Configurations>
    <Platforms>x64</Platforms>
  </PropertyGroup>

  <PropertyGroup>
    <ServerGarbageCollection>true</ServerGarbageCollection>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <!-- Code Analyzers -->
  <ItemGroup Condition=" '$(Configuration)' == 'Debug' ">
    <PackageReference Include="SerilogAnalyzer" PrivateAssets="All" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Elastic.Serilog.Sinks" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="MongoDB.Driver" />
    <PackageReference Include="Serilog" />
    <PackageReference Include="Serilog.Enrichers.Thread" />
    <PackageReference Include="Serilog.Extensions.Hosting" />
    <PackageReference Include="Serilog.Settings.Configuration" />
    <PackageReference Include="Serilog.Sinks.Async" />
    <PackageReference Include="Serilog.Sinks.Console" />
    <PackageReference Include="Serilog.Sinks.File" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Phoenix.Server.CommonDataStructure\Phoenix.Server.CommonDataStructure.csproj" />
    <ProjectReference Include="..\Phoenix.Server.Common\Phoenix.Server.Common.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="commander.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="commander_logging.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Content Include="..\..\shared\Config\cluster.json">
      <Link>cluster.json</Link>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="@echo off&#xA;
    del /F /Q &quot;..\..\Bin\Commander\*&quot;&#xA;
    xcopy /E /Y &quot;$(TargetDir)&quot; &quot;..\..\Bin\Commander\&quot;" />
  </Target>
</Project>
