// <auto-generated/>
#nullable enable
using System;
using System.Linq.Expressions;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.GameModel.Server
{
    /// <summary>
    /// 提供 GamePlayerData 的扩展方法
    /// </summary>
    public static class GamePlayerDataExtensions
    {
        /// <summary>
        /// 根据组件名称获取对应的数据
        /// </summary>
        /// <param name="data">玩家数据</param>
        /// <param name="componentName">组件名称</param>
        /// <returns>组件对应的数据，如果找不到则返回 null</returns>
        public static object? GetCompPersistData(this GamePlayerData data, string componentName)
        {
            switch (componentName)
            {
                {{~ for prop in x.properties ~}}
                case "{{ prop.component_name }}":
                    return data.{{ prop.property_name }};
                {{~ end ~}}
                default:
                    return null;
            }
        }
    }
}
