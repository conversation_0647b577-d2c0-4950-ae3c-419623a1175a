using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Text;
using Scriban;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using Utils.SourceGenerated.Build;

namespace Utils.SourceGenerator.PersistComp
{
    [Generator]
    public class PersistCompGenerator : ISourceGenerator
    {
        private static readonly string persistCompAttributeFullName = "Phoenix.GameModel.Server.PersistCompAttribute";

        private static readonly string persistCompAttributeText = @"
// <auto-generated/>
using System;
namespace Phoenix.GameModel.Server
{
    /// <summary>
    /// 初始化的组件数据属性
    /// </summary>
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field)]
    public partial class PersistCompAttribute : Attribute
    {
        public PersistCompAttribute(string name)
        {
            CompName = name;
        }

        /// <summary>
        /// 持久化的组件名
        /// </summary>
        public string CompName
        {
            get;
            set;
        }
    }
}
";

        public void Initialize(GeneratorInitializationContext context)
        {
            context.RegisterForPostInitialization(i=>i.AddSource("PersistCompAttribute.g.cs",
                SourceText.From(persistCompAttributeText, Encoding.UTF8)));
            // 注册语法接收器
            context.RegisterForSyntaxNotifications(() => new SyntaxReceiver());
        }

        public void Execute(GeneratorExecutionContext context)
        {
            // 获取语法接收器
            if (!(context.SyntaxContextReceiver is SyntaxReceiver receiver))
                return;

            // 获取 GamePlayerData 类型
            INamedTypeSymbol? gamePlayerDataSymbol = receiver.GamePlayerDataClass;
            if (gamePlayerDataSymbol == null)
                return;

            // 获取 PersistCompAttribute 类型
            INamedTypeSymbol? persistCompAttributeSymbol = context.Compilation.GetTypeByMetadataName(persistCompAttributeFullName);
            if (persistCompAttributeSymbol == null)
                return;

            // 收集所有带有 PersistCompAttribute 的属性
            var properties = new List<PropertyModel>();

            foreach (var member in gamePlayerDataSymbol.GetMembers().OfType<IPropertySymbol>())
            {
                foreach (var attribute in member.GetAttributes())
                {
                    if (attribute.AttributeClass?.Equals(persistCompAttributeSymbol, SymbolEqualityComparer.Default) == true)
                    {
                        string componentName = "";

                        // 检查是否有构造函数参数
                        if (attribute.ConstructorArguments.Length > 0)
                        {
                            componentName = attribute.ConstructorArguments[0].Value?.ToString() ?? "";
                        }

                        // 检查是否有命名参数 CompName
                        foreach (var namedArg in attribute.NamedArguments)
                        {
                            if (namedArg.Key == "CompName")
                            {
                                componentName = namedArg.Value.Value?.ToString() ?? "";
                                break;
                            }
                        }

                        properties.Add(new PropertyModel
                        {
                            property_name = member.Name,
                            component_name = componentName,
                            property_type = member.Type.ToDisplayString(SymbolDisplayFormat.MinimallyQualifiedFormat)
                        });
                        break;
                    }
                }
            }



            // 直接从嵌入资源中读取模板
            string templateContent = GetEmbeddedTemplate("GamePlayerDataExtensions.sbntxt");
            if (string.IsNullOrEmpty(templateContent))
            {
                // 如果嵌入资源读取失败，尝试从AdditionalFiles中读取
                templateContent = GetTemplateFromAdditionalFiles(context, "GamePlayerDataExtensions.sbntxt");
            }

            if (string.IsNullOrEmpty(templateContent))
            {
                // 如果仍然找不到模板，使用硬编码的模板内容
                templateContent = GetHardcodedTemplate();
            }

            var template = Template.Parse(templateContent);
            var source = template.RenderCode(new
            {
                // namespacename = gamePlayerDataSymbol.ContainingNamespace.ToDisplayString(),
                properties
            });
            // 添加生成的源代码
            context.AddSource("GamePlayerDataExtensions.g.cs", SourceText.From(source, Encoding.UTF8));
        }

        private string GetEmbeddedTemplate(string templateName)
        {
            try
            {
                var assembly = Assembly.GetExecutingAssembly();
                string resourceName = $"Phoenix.SourceGenerator.PersistComp.templates.{templateName}";

                // 列出所有嵌入资源，用于调试
                var resourceNames = assembly.GetManifestResourceNames();

                using (Stream? stream = assembly.GetManifestResourceStream(resourceName))
                {
                    if (stream == null)
                        return string.Empty;

                    using (StreamReader reader = new StreamReader(stream))
                    {
                        return reader.ReadToEnd();
                    }
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        private string GetTemplateFromAdditionalFiles(GeneratorExecutionContext context, string templateName)
        {
            try
            {
                // 查找项目中的附加文件
                foreach (var file in context.AdditionalFiles)
                {
                    if (Path.GetFileName(file.Path) == templateName)
                    {
                        return file.GetText()?.ToString() ?? string.Empty;
                    }
                }

                // 尝试在项目目录下查找
                string projectDir = Path.GetDirectoryName(context.Compilation.SyntaxTrees.First().FilePath) ?? string.Empty;
                string templatePath = Path.Combine(projectDir, "templates", templateName);

                if (File.Exists(templatePath))
                {
                    return File.ReadAllText(templatePath);
                }

                return string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        private string GetHardcodedTemplate()
        {
            // 硬编码的模板内容，作为最后的备选方案
            return @"// <auto-generated/>'
#nullable enable
using System;
using System.Linq.Expressions;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.GameModel.Server
{
    /// <summary>
    /// 提供 GamePlayerData 的扩展方法
    /// </summary>
    public static class GamePlayerDataExtensions
    {
        /// <summary>
        /// 根据组件名称获取对应的数据
        /// </summary>
        /// <param name=""data"">玩家数据</param>
        /// <param name=""componentName"">组件名称</param>
        /// <returns>组件对应的数据，如果找不到则返回 null</returns>
        public static object? GetCompPersistData(this GamePlayerData data, string componentName)
        {
            switch (componentName)
            {
                {{~ for prop in x.properties ~}}
                case ""{{ prop.component_name }}"":
                    return data.{{ prop.property_name }};
                {{~ end ~}}
                default:
                    return null;
            }
        }
    }
}";
        }

        private string RenderTemplate(Template template, object model)
        {
            var context = new Scriban.TemplateContext();
            var scriptObject = new Scriban.Runtime.ScriptObject();

            // 将模型属性添加到脚本对象
            foreach (var prop in model.GetType().GetProperties())
            {
                scriptObject.Add(prop.Name.ToLower(), prop.GetValue(model));
            }

            context.PushGlobal(scriptObject);
            return template.Render(context);
        }

        /// <summary>
        /// 属性模型，用于模板渲染
        /// </summary>
        private class PropertyModel
        {
            public string property_name { get; set; } = "";
            public string component_name { get; set; } = "";
            public string property_type { get; set; } = "";
        }

        /// <summary>
        /// 语法接收器，用于收集 GamePlayerData 类
        /// </summary>
        private class SyntaxReceiver : ISyntaxContextReceiver
        {
            public INamedTypeSymbol? GamePlayerDataClass { get; private set; }

            public void OnVisitSyntaxNode(GeneratorSyntaxContext context)
            {
                // 查找 GamePlayerData 类
                if (context.Node is ClassDeclarationSyntax classDeclaration &&
                    classDeclaration.Identifier.ValueText == "GamePlayerData")
                {
                    var symbol = context.SemanticModel.GetDeclaredSymbol(classDeclaration);
                    if (symbol != null && symbol.ContainingNamespace.ToDisplayString() == "Phoenix.GameModel.Server")
                    {
                        GamePlayerDataClass = symbol;
                    }
                }
            }
        }
    }
}
