{"Server": {"ZoneId": 6666, "NodeId": 1001, "Name": "GameServer_1", "Role": "game", "Ip": "127.0.0.1", "Port": 5556, "Tags": "game", "ServerHeartbeatInterval": 10, "ServerHeartbeatTimeout": 60}, "Elasticsearch": {"Uri": "http://elastic:changeme@*********:9200", "DataStream": {"Type": "logs", "DataSet": "game"}}, "DatabaseSettings": {"Game": {"ConnectionString": "***************************************", "DatabaseName": "GameDB"}}, "GamePlayerContext": {"Save2DBPeriod": 30, "KeepAliveTimeoutSeconds": 30}, "ActorStat": {"Enable": true, "IntervalSeconds": 60}}