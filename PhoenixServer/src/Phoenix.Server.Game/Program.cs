// Copyright (c) Phoenix.  All Rights Reserved.

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Phoenix.Server.Common;
using ILogger = Microsoft.Extensions.Logging.ILogger;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Common.MessageHandlerRegistry;
using Phoenix.Server.CommonDataStructure;
using Phoenix.Server.Model;

// Login server namespace
namespace Phoenix.Server.Game;

public static class Program
{
    /// <summary>
    ///     Main entry point of the application.
    /// </summary>
    /// <param name="args">Command line arguments passed to the application.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains the exit code of the application:
    ///     0 for success, 1 for failure.
    /// </returns>
    public static async Task<int> Main(string[] args)
    {
        // Phoenix.Server.Log unhandled exceptions
        AppDomain.CurrentDomain.UnhandledException += (_, e)
            =>
        {
            s_logger.LogCritical((Exception)e.ExceptionObject, "Unhandled Exception");
        };

        // Phoenix.Server.Log unobserved task exceptions
        TaskScheduler.UnobservedTaskException += (_, e) =>
        {
            s_logger.LogCritical(e.Exception, "Unobserved Task Exception");
            e.SetObserved();
        };

        try
        {
            // Start the login service
            await CreateHostBuilder(args).Build().RunAsync();
            return 0;
        }
        catch (Exception ex)
        {
            // Phoenix.Server.Log any exceptions that cause the server to terminate unexpectedly
            s_logger.LogError(ex, "server terminated unexpectedly");
            return 1;
        }
        finally
        {
            // Ensure all logs are flushed before the application exits
            await Serilog.Log.CloseAndFlushAsync();
        }
    }

    /// <summary>
    ///     Creates a host builder for the application.
    /// </summary>
    /// <param name="args">Command line arguments passed to the application.</param>
    /// <returns>A HostApplicationBuilder configured with the necessary services and configuration for the application.</returns>
    private static HostApplicationBuilder CreateHostBuilder(string[] args)
    {
        // MsgPackProtoHelper.Instance.Init();
        var settings = new HostApplicationBuilderSettings
        {
            DisableDefaults = true,
            Configuration = new ConfigurationManager()
        };

        foreach (string arg in args)
        {
            settings.Configuration.AddJsonFile(Path.Combine(AppContext.BaseDirectory, arg), optional: false);
        }

        var builder = Host.CreateApplicationBuilder(settings);
        builder.InitSerilog();

        // Configure options from the configuration
        builder.Services.Configure<DatabaseSettingsOptions>(builder.Configuration.GetSection(DatabaseSettingsOptions.StoreKeyGame));
        builder.Services.Configure<ServerOptions>(builder.Configuration.GetSection(ServerOptions.StoreKey));
        builder.Services.Configure<ClusterOptions>(builder.Configuration.GetSection(ClusterOptions.StoreKey));
        builder.Services.Configure<ElasticsearchOptions>(builder.Configuration.GetSection(ElasticsearchOptions.StoreKey));
        builder.Services.Configure<GamePlayerContextOptions>(builder.Configuration.GetSection(GamePlayerContextOptions.StoreKey));
        builder.Services.Configure<ActorStatOptions>(builder.Configuration.GetSection(ActorStatOptions.StoreKey));

        // builder.Configuration.GetRequiredSection(GamePlayerContextOptions.StoreKey).Bind(new GamePlayerContextOptions());

        //配置相关资源
        builder.Services.AddSingleton<ResourceInitializer>();
        builder.Services.BuildServiceProvider().GetRequiredService<ResourceInitializer>();


        // var serverOptions = builder.Services.BuildServiceProvider().GetRequiredService<IOptions<GamePlayerContextOptions>>();
        builder.Services.AddSingleton<GameServerConfigureService>();
        builder.Services.BuildServiceProvider().GetRequiredService<GameServerConfigureService>();

        builder.Services.AddPhoenixCommonServices();
        builder.Services.AddSingleton<IUniqueIdentifierGenerator, UniqueIdentifierGenerator>();
        builder.Services.AddSingleton<IGameDatabaseService, GameDatabaseService>();
        builder.Services.BuildServiceProvider().GetRequiredService<IGameDatabaseService>();
        builder.Services.AddSingleton<IMessageHandlerRegistry>(new GameMessageHandlerRegistry());
        builder.Services.AddHostedService<LocalPlayerManager>();
        if (RpcService.UseConsul())
        {
            builder.Services.AddConsulLeaderDiscovery();
        }
        builder.Services.AddHostedService<RpcService>();
        builder.Services.AddHostedService<GameBootstrap>();

        return builder;
    }

    // Logger for this class
    private static ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger("Main");
}
