// Copyright (c) Phoenix.All Rights Reserved.

using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Common;
using Phoenix.Server.Common.Actor;

namespace Phoenix.Server.Game;

public class GameBootstrap : IHostedService
{
    private void BindDelegates()
    {
        RpcService.Instance.DelegateOnRpcMessageRequest += OnRpcMessageRequest;
        RpcService.Instance.DelegateOnRpcRequest += OnRpcRequest;
        RpcService.Instance.DelegateOnForwardClientMsg += OnForwardClient;
        // RpcService.Instance.DelegateOnRpcRequestToPlayer += OnRpcRequestToPlayer;
        RpcService.Instance.DelegateOnNodeJoin += OnNodeJoin;
        RpcService.Instance.DelegateOnNodeLeave += OnNodeLeave;
        RpcService.Instance.DelegateOnWorldHashChanged += OnWordResharding;
    }

    private void OnWordResharding(string hashName, ConsistentHash.ConsistentHashImmutable chashImmutable)
    {
        s_logger.LogInformation("[RPC]OnWordResharding, HashName={HashName}", hashName);
        LocalPlayerManager.Instance.OnWorldResharding(hashName, chashImmutable);
    }

    private void OnNodeLeave(uint nodeId, string role)
    {
        LocalPlayerManager.Instance.OnNodeLeave(nodeId, role);
    }

    private void OnNodeJoin(MsgPack_S_ServerNodeInfo_Proto nodeInfo)
    {
        LocalPlayerManager.Instance.OnNodeJoin(nodeInfo.NodeId, nodeInfo.Role);
    }

    private void OnRpcRequestToPlayer(long playerId, bool isSpacePlayer, string funcName, object[]? paramList, MsgPackStructBase? message, RpcRequestInfo reqInfo)
    {
        s_logger.LogInformation("OnRpcRequestToPlayer funcName={funcName}", funcName);
    }

    private void OnForwardClient(long playerId, MsgPackStructBase msg)
    {
        var player = LocalPlayerManager.Instance.GetPlayer(playerId);
        if (player != null)
        {
            player.PostMessage(new ClientMessage("", msg));
        }
        else
        {
            s_logger.LogError("OnForwardClient player not found, PlayerId={PlayerId} Message={Message}", playerId, msg);
        }
    }

    private void OnRpcRequest(string uri, string funcName, object[] paramList, RpcRequestInfo reqInfo)
    {
        object[] args = new object[paramList.Length + 1];
        args[0] = reqInfo;
        for (int i = 0; i < paramList.Length; ++i)
        {
            args[i + 1] = paramList[i];
        }
        if (uri == RpcUriConstants.BASE_PLAYER_MGR_URI)
        {
            var rpcReqMessage = new RpcReqMessage(reqInfo.CorrelationId, reqInfo, "LocalPlayerManager", funcName, args, null);
            LocalPlayerManager.Instance.PostMessage(rpcReqMessage);
        }
    }

    private void OnRpcMessageRequest(string uri, string funcName, MsgPackStructBase message, RpcRequestInfo reqInfo)
    {
        if (uri == RpcUriConstants.BASE_PLAYER_MGR_URI)
        {
            var rpcMessage = new RpcReqMessage(reqInfo.CorrelationId, reqInfo, "LocalPlayerManager", funcName, null, message);
            var actor = ActorRegistry.Instance.GetActor(uri.Split()[1]);
            if (actor != null)
            {
                actor.PostMessage(rpcMessage);
            }
            else
            {
                reqInfo.SendResponse(new Dictionary<object, object>(), RpcErrCode.RPC_ERROR_ENTITY_NOTFOUND );
            }
        }
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        BindDelegates();
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    private static readonly ILogger s_logger = Common.InternalLoggerFactory.LoggerFactory.CreateLogger<GameBootstrap>();
}
