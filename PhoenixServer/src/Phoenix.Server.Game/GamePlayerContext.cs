// Copyright (c) Phoenix.All Rights Reserved.

using Microsoft.Extensions.Logging;
using Phoenix.GameModel.Server;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Common;
using Phoenix.Server.Common.Actor;
using Phoenix.Server.Common.MessageHandlerRegistry;
using Phoenix.Server.CommonDataStructure;
using Phoenix.Server.Model;

namespace Phoenix.Server.Game;

public class GamePlayerContext : Actor<long>
{
    public GamePlayerContext(long playerId)
    {
        m_playerId = playerId;
        TickInternal = 5000;
    }

    public void LoginDone(GamePlayerData playerData)
    {
        Active();
        m_gamePlayer = new(playerData);
        m_gamePlayer.CompTransport.BindDelegates(SendMessageToClient);
        m_gamePlayer.CompPersistent.Init(m_playerId);
        m_gamePlayer.Init();
        s_logger.LogInformation("LoginDone, PlayerId={PlayerId}", m_playerId);
    }

    public override Task Active()
    {
        base.Active();
        StartKeepAliveWaitReconnectOrGateBind();
        return Task.CompletedTask;
    }

    private async Task Logout()
    {
        s_logger.LogInformation("Logout, PlayerId={PlayerId}", m_playerId);
        m_state = PlayerState.OFFLINE;
        await Inactive();
        LocalPlayerManager.Instance.RemovePlayer(m_playerId);
    }

    public void BindWorldRouter(uint worldRouterNodeId)
    {
        m_playerProxyInWorldRouter = worldRouterNodeId;
    }

    private async Task Flush2DB()
    {
        await m_gamePlayer.Flush2DB();
    }

    internal Task ReqBindGate(RpcRequestInfo reqInfo, string ip, string sessionId, string loginKey, bool relogin)
    {
        s_logger.LogInformation(
            "BindGate, PlayerId={PlayerId}, SessionId={SessionId} Ip={Ip}, LoginKey={LoginKey}, Relogin={Relogin}, ReqNodeId={ReqNodeId}",
            m_playerId, sessionId, ip, loginKey, relogin, reqInfo.NodeId);
        m_gateNodeId = reqInfo.NodeId;
        m_loginKey = loginKey;
        m_ip = ip;
        if (m_state == PlayerState.BEING_DESTROYED)
        {
            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_PLAYER_IS_DESTROYING }
            });
        }
        else
        {
            StopKeepAliveWaitReconnectOrGateBind();
            reqInfo.SendResponse(new Dictionary<object, object> { { RpcConstants.ERR_CODE, LoginErrorCode.OK } });
        }

        return Task.CompletedTask;
    }

    internal Task ReqClientDisconnect(RpcRequestInfo reqInfo, string sessionId)
    {
        s_logger.LogInformation(
            "ReqClientDisconnect, ReqId={ReqId} PlayerId={PlayerId}, SessionId={SessionId}, ReqNodeId={reqNodeId}",
            reqInfo.ReqId, m_playerId, sessionId, reqInfo.NodeId);
        if (reqInfo.NodeId != m_gateNodeId)
        {
            s_logger.LogError(
                "ReqClientDisconnect, ReqId={ReqId} PlayerId={PlayerId}, SessionId={SessionId}, ReqNodeId={reqNodeId}, GateNodeId={GateNodeId}",
                reqInfo.ReqId, m_playerId, sessionId, reqInfo.NodeId, m_gateNodeId);
            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_NODE_NOT_MATCH }
            });
            return Task.CompletedTask;
        }

        // begin to keepalive
        StartKeepAliveWaitReconnectOrGateBind();
        reqInfo.SendResponse(new Dictionary<object, object> { { RpcConstants.ERR_CODE, LoginErrorCode.OK } });
        return Task.CompletedTask;
    }

    private void StartKeepAliveWaitReconnectOrGateBind()
    {
        if (!m_isKeepAliveWaitReconnect)
        {
            s_logger.LogInformation("StartKeepAliveWaitReconnect, PlayerId={PlayerId}", m_playerId);
            m_isKeepAliveWaitReconnect = true;
            m_keepAliveBeginTime = DateTime.Now;
        }
    }

    private void StopKeepAliveWaitReconnectOrGateBind()
    {
        if (m_isKeepAliveWaitReconnect)
        {
            s_logger.LogInformation("StopKeepAliveWaitReconnect, PlayerId={PlayerId}", m_playerId);
            m_isKeepAliveWaitReconnect = false;
        }
    }

    protected override async Task Tick()
    {
        // s_logger.LogTrace("Tick, PlayerId={PlayerId}", m_playerId);
        // HeartbeatCheck();
        if (m_isKeepAliveWaitReconnect && m_keepAliveBeginTime.AddSeconds(KeepAliveTimeoutSeconds) < DateTime.Now)
        {
            // destroy player
            StopKeepAliveWaitReconnectOrGateBind();
            if (m_state != PlayerState.BEING_DESTROYED)
            {
                m_state = PlayerState.BEING_DESTROYED;
                await ApplyLogout(0);
                return;
            }
        }

        await m_gamePlayer.Tick();
    }

    private async Task ApplyLogout(int reasonCode)
    {
        s_logger.LogInformation("ApplyLogout, PlayerId={PlayerId}, ReasonCode={ReasonCode}", m_playerId, reasonCode);
        var rpcTarget =
            RpcManager.CreateNodeTarget(RpcUriConstants.WORLD_PLAYER_REGISTRY_URI, m_playerProxyInWorldRouter);
        var resp = await RpcManager.Instance.AsyncCall(rpcTarget, "ReqPlayerLogout",
            [m_playerId, reasonCode]);
        if (resp.RetCode == RpcErrCode.RPC_NO_ERROR)
        {
            //落盘
            await Flush2DB();
            await ApplyLogoutDone();
            await Logout();
        }
    }

    private async Task ApplyLogoutDone()
    {
        s_logger.LogInformation("ApplyLogoutDone, PlayerIdId={PlayerId}", m_playerId);
        var rpcTarget =
            RpcManager.CreateNodeTarget(RpcUriConstants.WORLD_PLAYER_REGISTRY_URI, m_playerProxyInWorldRouter);
        var resp = await RpcManager.Instance.AsyncCall(rpcTarget, "ReqPlayerLogoutDone",
            [m_playerId]);
        if (resp.RetCode != RpcErrCode.RPC_NO_ERROR)
        {
            s_logger.LogError("ApplyLogoutDone fail, PlayerId={PlayerId} RpcErrCode={RpcErrCode}", m_playerId,
                resp.RetCode);
        }
    }

    protected override Task HandleClientMessage(ClientMessage clientMessage)
    {
        var message = clientMessage.Message;
        var handlerInfo = MessageHandlerRegistry.Instance.TryGetMessageHandler(message);
        if (handlerInfo != null)
        {
            object? instance = GetMessageExecInstance(handlerInfo.ExecType);
            if (instance != null)
            {
                return handlerInfo.Handler(instance, message);
            }
        }

        s_logger.LogError($"Unhandled message: {message.ProtoCode},{message.GetType().Name}");
        return Task.CompletedTask;
    }

    /// <summary>
    /// 获取消息的执行实例，支持定义在GamePlayerContext、GamePlayer或者直接定义在组件上，简化协议定义流程
    /// </summary>
    /// <param name="execType"></param>
    /// <returns></returns>
    private object? GetMessageExecInstance(Type execType)
    {
        if (execType == typeof(GamePlayerContext))
        {
            return this;
        }

        if (execType == typeof(GamePlayer))
        {
            return m_gamePlayer;
        }

        var comp = m_gamePlayer?.GetComp(execType);
        return comp;
    }

    [MessageHandler(typeof(MsgPack_BattleCommand_Req))]
    private Task OnClientMessage(MsgPack_BattleCommand_Req? req)
    {
        s_logger.LogInformation("HandleMessageBattleCommandReq, PlayerId={PlayerId}, Req={Req}", m_playerId, req);
        return Task.CompletedTask;
    }

    [MessageHandler(typeof(MsgPack_Duel_Req))]
    private Task OnClientMessage(MsgPack_Duel_Req? req)
    {
        s_logger.LogInformation("HandleMessageDuelReq, PlayerId={PlayerId}, Req={Req}", m_playerId, req);
        MsgPack_Duel_Ack ack = new();
        ack.SetToken(req!.Token);
        MsgPack_S_Duel_Ntf ntf = new();
        RpcManager.Instance.RpcToPlayer(req.UidTarget, ntf);
        ack.SetToken(req.GetToken()!.Value);
        SendMessageToClient(ack);
        return Task.CompletedTask;
    }

    [MessageHandler(typeof(PlayerInfoInitReq))]
    private Task OnPlayerInfoInitReq(PlayerInfoInitReq? req)
    {
        s_logger.LogInformation("OnPlayerInfoInitReq, PlayerId={PlayerId}, Req={Req}", m_playerId, req);
        var ack = new PlayerInfoInitAck();
        if (req?.Token != null)
        {
            ack.Token = req.Token;
        }

        ack.ErrCode = m_gamePlayer.CompBasic.IsNewbie()
            ? (int)ErrCode.ErrCodeCharacterNotExist
            : (int)ErrCode.ErrCodeOk;
        SendMessageToClient(ack);

        if (!m_gamePlayer.CompBasic.IsNewbie())
        {
            m_gamePlayer.SyncToClient();
        }

        return Task.CompletedTask;
    }

    [MessageHandler(typeof(CharacterCreateReq))]
    private async Task OnCharacterCreateReq(CharacterCreateReq? req)
    {
        s_logger.LogInformation("OnCharacterCreateReq, PlayerId={PlayerId}, Req={Req}", m_playerId, req);
        CharacterCreateAck ack = new();
        try
        {
            if (req?.Token != null)
            {
                ack.Token = req.Token;
            }

            await m_gamePlayer.TrySetName(req!.NickName);
        }
        catch (Exception e)
        {
            ack.ErrCode = (int)ErrCode.ErrCodePlayerNameDuplicate;
            s_logger.LogError("Save2DB. PlayerId={PlayerId} Error={Error}", m_playerId, e);
        }

        SendMessageToClient(ack);
    }

    [MessageHandler(typeof(MsgPack_Logout_Req))]
    private Task OnClientMessage(MsgPack_Logout_Req? req)
    {
        s_logger.LogInformation("HandleMessage Example Req, PlayerId={PlayerId}, Req={Req}", m_playerId, req);

        _ = ApplyLogout(0);

        return Task.CompletedTask;
    }

    #region MsgPack_Example From Client

    [MessageHandler(typeof(MsgPack_Example_Req))]
    private Task OnClientMessage(MsgPack_Example_Req? req)
    {
        s_logger.LogInformation("HandleMessage Example Req, PlayerId={PlayerId}, TargetId={TargetId}, Req={Req}",
            m_playerId, req.UidTarget, req);
        var ack = new MsgPack_Example_Ack();
        if (req?.Token != null)
        {
            ack.Token = req.Token;
        }

        ack.ErrCode = (int)ErrCode.ErrCodeOk;

        SendMessageToClient(ack);

        m_gamePlayer.GetComp<GamePlayerCompTest>().SendToClientExampleData();

        return Task.CompletedTask;
    }

    #endregion

    #region Test echo

    [MessageHandler(typeof(MsgPack_Echo_Req))]
    private Task OnClientMessage(MsgPack_Echo_Req? req)
    {
        s_logger.LogTrace("HandleMessage Echo Req, PlayerId={PlayerId}, Req={Req}", m_playerId, req);
        var ack = new MsgPack_Echo_Ack();
        if (req?.Token != null)
        {
            ack.Token = req.Token;
        }

        ack.Content = req!.Content;

        SendMessageToClient(ack);

        return Task.CompletedTask;
    }

    #endregion

    private void SendMessageToClient(MsgPackStructBase msg)
    {
        RpcService.Instance.SendMessageToClient(m_playerId, m_gateNodeId, msg, false);
    }

    protected override Task HandleRpcReqMessage(RpcReqMessage rpcMessage)
    {
        if (rpcMessage!.ReqMessage!.ProtoCode == EProtoCode.DUEL_S_DUEL_NTF)
        {
            var duelNtf = rpcMessage.ReqMessage as MsgPack_S_Duel_Ntf;
            return OnRpcMessage(rpcMessage.ReqCtx, duelNtf);
        }

        if (rpcMessage.ReqMessage.ProtoCode == EProtoCode.DUEL_S_DUELREPLY_NTF)
        {
            var duelReplyNtf = rpcMessage.ReqMessage as MsgPack_S_DuelReply_Ntf;
        }

        return base.HandleRpcReqMessage(rpcMessage);
    }

    private Task OnRpcMessage(RpcRequestInfo rpcMessageReqCtx, MsgPack_S_Duel_Ntf? duelNtf)
    {
        s_logger.LogInformation("OnRecv DuelNtf PlayerId={PlayerId} SrcPlayerId={SrcPlayerId}", m_playerId,
            duelNtf!.SrcTarget!);
        MsgPack_S_Duel_Ntf ntf = new();
        ntf.SrcTarget = duelNtf.SrcTarget;
        SendMessageToClient(ntf);
        return Task.CompletedTask;
    }

    public override long GetActorId() => m_playerId;

    internal void OnWorldResharding(string hashName, ConsistentHash.ConsistentHashImmutable chashImmutable)
    {
        PostAction(async () =>
        {
            nodeid_t newRouterNodeId = chashImmutable.PickNode(m_playerId);
            s_logger.LogInformation("OnWorldResharding, HashName={HashName}", hashName);
            if (m_playerProxyInWorldRouter != newRouterNodeId)
            {
                nodeid_t oldRouterNodeId = m_playerProxyInWorldRouter;
                var rpcTarget = RpcManager.CreateNodeTarget(RpcUriConstants.WORLD_PLAYER_REGISTRY_URI, oldRouterNodeId);
                s_logger.LogInformation(
                    "OnWorldResharding, ReqDeletePlayerProxy PlayerId={PlayerId}, OldNodeId={OldNodeId}, NewNodeId={NewNodeId}",
                    m_playerId, oldRouterNodeId, newRouterNodeId);
                var resp = await RpcManager.Instance.AsyncCall(rpcTarget, "ReqDeletePlayerProxy",
                    [m_playerId]);
                if (resp.RetCode != RpcErrCode.RPC_NO_ERROR)
                {
                    s_logger.LogError(
                        "OnWorldResharding, ReqDeletePlayerProxy fail PlayerId={PlayerId}, OldNodeId={OldNodeId}, NewNodeId={NewNodeId} RetCode={RetCode}",
                        m_playerId, oldRouterNodeId, newRouterNodeId, resp.RetCode);
                    int retryCount = 3;
                    for (int i = 0; i < retryCount; i++)
                    {
                        int retryTime = i;
                        resp = await RpcManager.Instance.AsyncCall(rpcTarget, "ReqDeletePlayerProxy",
                            [m_playerId]);
                        s_logger.LogError(
                            "OnWorldResharding, ReqDeletePlayerProxy RetryTime={RetryTime}, PlayerId={PlayerId}, OldNodeId={OldNodeId}, NewNodeId={NewNodeId} RetCode={RetCode}",
                            retryTime, m_playerId, oldRouterNodeId, newRouterNodeId, resp.RetCode);
                        if (resp.RetCode == RpcErrCode.RPC_NO_ERROR)
                        {
                            break;
                        }
                    }
                }

                s_logger.LogInformation(
                    "OnWorldResharding, ReqRestorePlayerProxy PlayerId={PlayerId}, OldNodeId={OldNodeId}, NewNodeId={NewNodeId} success",
                    m_playerId, m_playerProxyInWorldRouter, newRouterNodeId);
                rpcTarget = RpcManager.CreateNodeTarget(RpcUriConstants.WORLD_PLAYER_REGISTRY_URI, newRouterNodeId);
                resp = await RpcManager.Instance.AsyncCall(rpcTarget, "ReqRestorePlayerProxy", [
                    false, new Dictionary<string, object>()
                    {
                        { "player_id", m_playerId },
                        { "game_node_id", RpcService.Instance.MyNode.NodeInfo.NodeId },
                        { "gate_node_id", m_gateNodeId },
                        // todo sync state
                        { "state", (int)PlayerState.ONLINE }
                    }
                ]);
                if (resp.RetCode == RpcErrCode.RPC_NO_ERROR)
                {
                    m_playerProxyInWorldRouter = chashImmutable.PickNode(m_playerId);
                }
                else
                {
                    s_logger.LogError(
                        "OnWorldResharding, ReqRestorePlayerProxy PlayerId={PlayerId}, OldNodeId={OldNodeId}, NewNodeId={NewNodeId} RetCode={RetCode}",
                        m_playerId, m_playerProxyInWorldRouter, chashImmutable.PickNode(m_playerId), resp.RetCode);
                }
            }
        });
    }

    private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<GamePlayerContext>();

    private PlayerState m_state = PlayerState.ONLINE;
    private readonly long m_playerId;
    private uint m_playerProxyInWorldRouter;
    private string m_loginKey = string.Empty;
    private string m_ip = string.Empty;
    private DateTime m_lastHeartbeatTime = DateTime.Now;
    private DateTime m_keepAliveBeginTime;
    private bool m_isKeepAliveWaitReconnect;
    private uint m_gateNodeId;
    private GamePlayer m_gamePlayer;

    private int KeepAliveTimeoutSeconds
    {
        get => GameServerConfigureService.Instance.GamePlayerContextOptions.KeepAliveTimeoutSeconds;
    }
}
