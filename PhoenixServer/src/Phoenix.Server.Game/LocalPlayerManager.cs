// Copyright (c) Phoenix.All Rights Reserved.

using System.Collections.Concurrent;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Phoenix.Server.Common;
using Phoenix.Server.Common.Actor;
using Phoenix.Server.Model;
using InternalLoggerFactory = Phoenix.Server.Common.InternalLoggerFactory;

namespace Phoenix.Server.Game;

[RpcModule(Name = "LocalPlayerManager")]
public sealed class LocalPlayerManager : Actor<string>, IHostedService
{
    public static LocalPlayerManager Instance { get; private set; } = null!;

    public LocalPlayerManager(IGameDatabaseService gameDatabaseService)
    {
        Instance = this;
        m_gameDatabaseService = gameDatabaseService;
        RpcModuleMethodFinder.Instance.RegModule(this);
        // RpcService.Instance.RegisterMailbox("LocalPlayerMgr");
    }

    public override string GetActorId() => "LocalPlayerManager";

    public Task StartAsync(CancellationToken cancellationToken)
    {
        Active();
        return Task.CompletedTask;
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        await Inactive();
    }

    [RpcMethod(Name = "ReqCreatePlayer", Role = RoleName.ROLE_ROUTER)]
    private Task ReqCreatePlayer(RpcRequestInfo reqInfo, long playerId, uint gameNodeId)
    {
        Task.Run(async() =>
        {
            s_logger.LogInformation("CreatePlayer, playerId={playerId}, from={reqNodeId}", playerId, reqInfo.NodeId);
            var player = new GamePlayerContext(playerId);
            if (m_playerId2Player.TryAdd(playerId, player))
            {
                try
                {
                    var playerModel = await m_gameDatabaseService.GetPlayerAsync(playerId);
                    player.LoginDone(playerModel!);
                    player.BindWorldRouter(reqInfo.NodeId);
                    // bind world router
                    reqInfo.SendResponse(
                        new Dictionary<object, object> { { RpcConstants.ERR_CODE, LoginErrorCode.OK } });
                }
                catch (Exception e)
                {
                    RemovePlayer(playerId);
                    s_logger.LogError(e, "CreatePlayer failed. playerId={playerId}, from={reqNodeId}", playerId,
                        reqInfo.NodeId);
                    reqInfo.SendResponse(new Dictionary<object, object>
                        { { RpcConstants.ERR_CODE, LoginErrorCode.ERR_PLAYER_INIT_FAILED } });
                    return;
                }
            }
            else
            {
                s_logger.LogError("CreatePlayer failed. playerId={playerId} already exists, from={reqNodeId}", playerId, reqInfo.NodeId);
                reqInfo.SendResponse(new Dictionary<object, object> { { RpcConstants.ERR_CODE, LoginErrorCode.ERR_PLAYER_ALREADY_EXISTS } });
            }
        });
        return Task.CompletedTask;
    }

    [RpcMethod(Name = "ReqBindGate", Role = RoleName.ROLE_GATE)]
    private Task ReqBindGate(RpcRequestInfo reqInfo, long playerId, string sessionId, string ip, string loginKey, bool relogin)
    {
        s_logger.LogInformation("BindGate, playerId={playerId}, sessionId={sessionId} ip={ip}, loginKey={loginKey}, relogin={relogin}, from={reqNodeId}",
            playerId, sessionId, ip, loginKey, relogin, reqInfo.NodeId);
        var player = GetPlayer(playerId);
        if (player != null)
        {
            player.PostAction(() => player.ReqBindGate(reqInfo, sessionId, ip, loginKey, relogin));
        }
        return Task.CompletedTask;
    }

    [RpcMethod(Name = "ReqClientDisconnect", Role = RoleName.ROLE_GATE)]
    private Task ReqClientDisconnect(RpcRequestInfo reqInfo, long playerId, string sessionId)
    {
        s_logger.LogInformation("ReqClientDisconnect, ReqId={ReqId} playerId={playerId}, sessionId={sessionId}, from={reqNodeId}", reqInfo.ReqId, playerId, sessionId, reqInfo.NodeId);
        var player = GetPlayer(playerId);
        if (player != null)
        {
            player.PostAction(() => player.ReqClientDisconnect(reqInfo, sessionId));
        }
        else
        {
            reqInfo.SendResponse(new Dictionary<object, object>(), RpcErrCode.RPC_ERROR_ENTITY_NOTFOUND);
            s_logger.LogError("ReqClientDisconnect failed. player not found, ReqId={ReqId} playerId={playerId}, sessionId={sessionId}, from={reqNodeId}", reqInfo.ReqId, playerId, sessionId, reqInfo.NodeId);
        }
        return Task.CompletedTask;
    }

    public GamePlayerContext? GetPlayer(long playerId)
    {
        m_playerId2Player.TryGetValue(playerId, out var player);
        return player;
    }

    public void RemovePlayer(long playerId)
    {
        m_playerId2Player.TryRemove(playerId, out _);
    }

    internal void OnNodeLeave(uint nodeId, string role)
    {
        s_logger.LogInformation("OnNodeLeave NodeId={NodeId} Role={Role}", nodeId, role);
        PostAction(() =>
        {
            return Task.CompletedTask;
        });
    }

    internal void OnNodeJoin(uint nodeId, string role)
    {
        s_logger.LogInformation("OnNodeJoin NodeId={NodeId} Role={Role}", nodeId, role);
    }

    internal void OnWorldResharding(string hashName, ConsistentHash.ConsistentHashImmutable chashImmutable)
    {
        s_logger.LogInformation("[RPC]OnWordResharding, HashName={HashName}", hashName);
        PostAction(() =>
        {
            foreach (var pair in m_playerId2Player)
            {
                GamePlayerContext player = pair.Value;
                player.OnWorldResharding(hashName, chashImmutable);
            }

            return Task.CompletedTask;
        });
    }

    private readonly ConcurrentDictionary<long, GamePlayerContext> m_playerId2Player = new();
    private readonly IGameDatabaseService m_gameDatabaseService;
    private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<LocalPlayerManager>();
}
