using Microsoft.Extensions.Logging;
using Phoenix.GameModel.Base;
using Phoenix.GameModel.Server;
using Phoenix.Server.Common.MessageHandlerRegistry;
using Phoenix.Server.Utils;

namespace Phoenix.Server.Game;
public class GameMessageHandlerRegistry : MessageHandlerRegistry
{
    public GameMessageHandlerRegistry()
    {
    }

    protected override void InitRegisterMessageHandlers()
    {
        // 查找所有组件类
        var componentTypes = TypeSpider.FindTypesInSolution(
            type => type.IsClass && !type.IsAbstract
                                 && IsSubclassOf(type, typeof(GamePlayerCompBase))
                                 && type.Namespace != null
                                 && type.Namespace.StartsWith("Phoenix.GameModel.Server"),
            "Phoenix.Server.Model"
        );

        foreach (var componentType in componentTypes)
        {
            RegisterMessageHandlers(componentType);
        }

        // GamePlayerContext
        RegisterMessageHandlers(typeof(GamePlayerContext));
        // GamePlayer
        RegisterMessageHandlers(typeof(GamePlayer));
    }
}
