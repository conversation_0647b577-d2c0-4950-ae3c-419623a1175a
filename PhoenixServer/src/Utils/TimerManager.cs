using System.Collections.Concurrent;

namespace Phoenix.Server.Utils
{
    public class TimerManager
    {
        private readonly HashedWheelTimer timer;
        private readonly ConcurrentDictionary<string, IRepeatableTimeout> namedTimeouts;

        public TimerManager(TimeSpan tickDuration, int ticksPerWheel, long maxPendingTimeouts = -1)
        {
            timer = new HashedWheelTimer(tickDuration, ticksPerWheel, maxPendingTimeouts);
            namedTimeouts = new ConcurrentDictionary<string, IRepeatableTimeout>();
        }

        public ITimeout Schedule(string name, Action task, TimeSpan delay, TimeSpan? repeatInterval = null)
        {
            var repeatableTimeout = new RepeatableTimeout(name, task, delay, repeatInterval, this);
            namedTimeouts[name] = repeatableTimeout;
            repeatableTimeout.Schedule();
            return repeatableTimeout;
        }

        public ITimeout Schedule<T>(string name, Action<T> task, T parameter, TimeSpan delay, TimeSpan? repeatInterval = null)
        {
            var repeatableTimeout = new RepeatableTimeout<T>(name, task, parameter, delay, repeatInterval, this);
            namedTimeouts[name] = repeatableTimeout;
            repeatableTimeout.Schedule();
            return repeatableTimeout;
        }

        public bool Cancel(string name)
        {
            if (namedTimeouts.TryRemove(name, out var timeout))
            {
                return timeout.Cancel();
            }
            return false;
        }

        public bool HasTimerByName(string name)
        {
            return namedTimeouts.ContainsKey(name);
        }

        public void Stop()
        {
            timer.StopAsync().Wait();
        }

        private interface IRepeatableTimeout : ITimeout
        {
            void Schedule();
        }

        private class RepeatableTimeout : IRepeatableTimeout
        {
            private readonly string name;
            private readonly Action task;
            private readonly TimeSpan delay;
            private readonly TimeSpan? repeatInterval;
            private readonly TimerManager parent;
            private ITimeout currentTimeout;

            public RepeatableTimeout(string name, Action task, TimeSpan delay, TimeSpan? repeatInterval, TimerManager parent)
            {
                this.name = name;
                this.task = task;
                this.delay = delay;
                this.repeatInterval = repeatInterval;
                this.parent = parent;
                Task = new TimerTask(this);
            }

            public void Schedule()
            {
                currentTimeout = parent.timer.NewTimeout(Task, delay);
            }

            public bool Cancel()
            {
                return currentTimeout?.Cancel() ?? false;
            }

            public bool Canceled => currentTimeout?.Canceled ?? false;

            public bool Expired => currentTimeout?.Expired ?? false;

            public ITimer Timer => currentTimeout?.Timer;

            public ITimerTask Task { get; }

            private class TimerTask : ITimerTask
            {
                private readonly RepeatableTimeout parent;

                public TimerTask(RepeatableTimeout parent)
                {
                    this.parent = parent;
                }

                public void Run(ITimeout timeout)
                {
                    try
                    {
                        parent.task();
                    }
                    finally
                    {
                        if (parent.repeatInterval.HasValue && !timeout.Canceled)
                        {
                            parent.currentTimeout = parent.parent.timer.NewTimeout(this, parent.repeatInterval.Value);
                        }
                        else
                        {
                            parent.parent.namedTimeouts.TryRemove(parent.name, out _);
                        }
                    }
                }
            }
        }

        private class RepeatableTimeout<T> : IRepeatableTimeout
        {
            private readonly string name;
            private readonly Action<T> task;
            private readonly T parameter;
            private readonly TimeSpan delay;
            private readonly TimeSpan? repeatInterval;
            private readonly TimerManager parent;
            private ITimeout currentTimeout;

            public RepeatableTimeout(string name, Action<T> task, T parameter, TimeSpan delay, TimeSpan? repeatInterval, TimerManager parent)
            {
                this.name = name;
                this.task = task;
                this.parameter = parameter;
                this.delay = delay;
                this.repeatInterval = repeatInterval;
                this.parent = parent;
                Task = new TimerTask(this);
            }

            public void Schedule()
            {
                currentTimeout = parent.timer.NewTimeout(Task, delay);
            }

            public bool Cancel()
            {
                return currentTimeout?.Cancel() ?? false;
            }

            public bool Canceled => currentTimeout?.Canceled ?? false;

            public bool Expired => currentTimeout?.Expired ?? false;

            public ITimer Timer => currentTimeout?.Timer;

            public ITimerTask Task { get; }

            private class TimerTask : ITimerTask
            {
                private readonly RepeatableTimeout<T> parent;

                public TimerTask(RepeatableTimeout<T> parent)
                {
                    this.parent = parent;
                }

                public void Run(ITimeout timeout)
                {
                    try
                    {
                        parent.task(parent.parameter);
                    }
                    finally
                    {
                        if (parent.repeatInterval.HasValue && !timeout.Canceled)
                        {
                            parent.currentTimeout = parent.parent.timer.NewTimeout(this, parent.repeatInterval.Value);
                        }
                        else
                        {
                            parent.parent.namedTimeouts.TryRemove(parent.name, out _);
                        }
                    }
                }
            }
        }
    }
}
