namespace Phoenix.Server.Utils;

public static class Encrypt
{
    public static string EncryptClientGameSessionToken(string secret, string playerId, int expiryTime) => playerId;

    public static bool DecryptClientGameSessionToken(string token, out string playerId, out int expireTime)
    {
        playerId = token;
        expireTime = int.MaxValue;
        return true;
    }

    public static string EncryptClientLoginAuthToken(string secret, string account, int expiryTime) => account;

    public static bool DecryptClientLoginAuthToken(string token, out string account, out int expireTime)
    {
        account = token;
        expireTime = int.MaxValue;
        return true;
    }
}
