using System.Security.Cryptography;
using System.Text;

namespace Phoenix.Server.Utils.Security;

public static class RSA
{
    [ThreadStatic] private static RSACryptoServiceProvider? RSAProvider;

    [ThreadStatic] private static SHA1? SHA1;

    private static RSAParameters? RSAParameters;

    public static string SignData(string data)
    {
        RSAProvider ??= CreateNewRSACryptoServiceProvider();
#pragma warning disable CA5350
        SHA1 ??= SHA1.Create();
#pragma warning restore CA5350
        byte[] signedData = Encoding.UTF8.GetBytes(data);
        byte[] signature = RSAProvider.SignData(signedData, SHA1);
        return Convert.ToBase64String(signature);
    }

    public static bool VerifyData(string signedData, string signature)
    {
        RSAProvider ??= CreateNewRSACryptoServiceProvider();
#pragma warning disable CA5350
        SHA1 ??= SHA1.Create();
#pragma warning restore CA5350
        return RSAProvider.VerifyData(Encoding.UTF8.GetBytes(signedData), SHA1, Convert.FromBase64String(signature));
    }

    public static void InitRSAParametersFromKeyFile(string keyFile)
    {
        if (RSAParameters != null)
        {
            throw new InvalidOperationException("RSAParameters already init");
        }

        using RSACryptoServiceProvider rsa = new();
        using StreamReader sr = new(keyFile);
        string keyInfo = sr.ReadToEnd();
        rsa.FromXmlString(keyInfo);
        RSAParameters = rsa.ExportParameters(true);
    }

    private static RSACryptoServiceProvider CreateNewRSACryptoServiceProvider()
    {
        if (RSAParameters == null)
        {
            throw new InvalidOperationException("RSAParameters not init");
        }

        RSACryptoServiceProvider provider = new RSACryptoServiceProvider();
        provider.ImportParameters(RSAParameters.Value);
        return provider;
    }
}
