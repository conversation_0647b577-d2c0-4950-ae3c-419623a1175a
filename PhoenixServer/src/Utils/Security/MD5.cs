using System.Security.Cryptography;
using System.Text;

namespace Phoenix.Server.Utils.Security;

public static class MD5Generator
{
    /// <summary>
    ///     generate md5 signature
    /// </summary>
    /// <param name="plainString"></param>
    /// <returns></returns>
    public static string GenerateMD5Signature(string plainString)
    {
#pragma warning disable CA5351
        using MD5 md5 = MD5.Create();
#pragma warning restore CA5351
        StringBuilder sb = new();
        byte[] b = md5.ComputeHash(Encoding.UTF8.GetBytes(plainString));
        for (int i = 0; i < b.Length; ++i)
        {
            sb.Append(b[i].ToString("X2"));
        }

        return sb.ToString();
    }
}
