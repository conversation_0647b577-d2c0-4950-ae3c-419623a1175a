/*using System.Diagnostics;

namespace Phoenix.ProjectSS.Utilites;

public class ExecutionTimeMonitorAttribute : Attribute
{
    // private static readonly ILog m_logger = LogManager.GetLogger("ConcurrentBasicTimeWheel");
    private readonly string m_MethodName;
    private readonly int m_thresholdMilliseconds = 2;
    private readonly Stopwatch m_stopwatch;

    public ExecutionTimeMonitorAttribute(string methodName, int thresholdMilliseconds = 2)
    {
        m_MethodName = methodName;
        m_thresholdMilliseconds = thresholdMilliseconds;
        m_stopwatch = new Stopwatch();
    }

    public void OnEntry() => m_stopwatch.Start();

    public void OnExit()
    {
        m_stopwatch.Stop();
        if (m_stopwatch.ElapsedMilliseconds > m_thresholdMilliseconds)
        {
            m_logger.Warn($"Slow. Execution Time of {m_MethodName}: {m_stopwatch.ElapsedMilliseconds} ms.");
        }
    }
}
*/
