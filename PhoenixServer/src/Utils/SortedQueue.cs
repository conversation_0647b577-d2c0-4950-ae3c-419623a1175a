namespace Phoenix.Server.Utils.Concurrent;

public class SortedQueue<T>
{
    private readonly SortedSet<T> m_items = new(new SortComparer());
    public int Count => m_items.Count;

    public bool TryAdd(T item) => m_items.Add(item);

    /// <summary>
    ///     Removes a specified item
    /// </summary>
    /// <param name="item"></param>
    /// <returns>removed item</returns>
    public bool Remove(T item) => m_items.Remove(item);

    public T? FirstOrDefault() => m_items.FirstOrDefault();

    /// <summary>
    ///     清理
    /// </summary>
    public void Clear() => m_items.Clear();

    private sealed class SortComparer : Comparer<T>
    {
        /// <summary>
        ///     When overridden in a derived class, performs a comparison of two objects of the same type and returns a value
        ///     indicating whether one object is less than, equal to, or greater than the other.
        /// </summary>
        /// <param name="x">The first object to compare.</param>
        /// <param name="y">The second object to compare.</param>
        /// <exception cref="T:System.ArgumentException">
        ///     Type <paramref name="T" /> does not implement either the
        ///     <see cref="T:System.IComparable`1" /> generic interface or the <see cref="T:System.IComparable" /> interface.
        /// </exception>
        /// <returns>
        ///     A signed integer that indicates the relative values of <paramref name="x" /> and <paramref name="y" />, as shown in
        ///     the following table.
        ///     <list type="table">
        ///         <listheader>
        ///             <term> Value</term><description> Meaning</description>
        ///         </listheader>
        ///         <item>
        ///             <term> Less than zero</term>
        ///             <description><paramref name="x" /> is less than <paramref name="y" />.</description>
        ///         </item>
        ///         <item>
        ///             <term> Zero</term><description><paramref name="x" /> equals <paramref name="y" />.</description>
        ///         </item>
        ///         <item>
        ///             <term> Greater than zero</term>
        ///             <description><paramref name="x" /> is greater than <paramref name="y" />.</description>
        ///         </item>
        ///     </list>
        /// </returns>
        public override int Compare(T? x, T? y)
        {
            if (x == null && y == null)
            {
                return 0;
            }

            if (x == null)
            {
                return -1;
            }

            if (y == null)
            {
                return 1;
            }

            int result = Default.Compare(x, y);
            if (result == 0)
            {
                result = x.GetHashCode().CompareTo(y.GetHashCode());
            }

            return result;
        }
    }
}
