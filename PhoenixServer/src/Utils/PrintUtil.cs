using System.Text;
using Microsoft.Extensions.Logging;

namespace Phoenix.Server.Utils
{
    public class PrintUtil
    {
        private static readonly ILogger Logger = InternalLoggerFactory.LoggerFactory.CreateLogger<PrintUtil>();
        public static void PrintBytes(byte[] arrByte, string strDesc = "")
        {
            StringBuilder strBuilder = new StringBuilder();
            for (int i = 0; i < arrByte.Length; i++)
            {
                strBuilder.Append(arrByte[i]);
                if (i < arrByte.Length - 1)
                {
                    strBuilder.Append("|");
                }
            }
            Logger.LogWarning($"{strDesc} =====1-1===== strData={strBuilder.ToString()}, Length = {arrByte.Length}");

            string strData = string.Concat(arrByte.Select(temp => temp.ToString("x2")));
            Logger.LogWarning($"{strDesc} =====1-2===== strData={strData}");
        }
    }
}
