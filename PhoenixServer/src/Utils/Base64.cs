using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Phoenix.Server.Utils
{
    public static class Base64
    {
        /// <summary>
        /// 将字符串编码为 Base64
        /// </summary>
        /// <param name="plainText"></param>
        /// <returns></returns>
        public static string Encode(string plainText)
        {
            // 将字符串转换为字节数组
            byte[] plainTextBytes = Encoding.UTF8.GetBytes(plainText);
            // 将字节数组编码为 Base64 字符串
            return Convert.ToBase64String(plainTextBytes);
        }

        /// <summary>
        /// 将 Base64 编码的字符串解码为原始字符串
        /// </summary>
        /// <param name="base64EncodedData"></param>
        /// <returns></returns>
        public static string Decode(string base64EncodedData)
        {
            // 将 Base64 字符串解码为字节数组
            byte[] base64EncodedBytes = Convert.FromBase64String(base64EncodedData);
            // 将字节数组转换为原始字符串
            return Encoding.UTF8.GetString(base64EncodedBytes);
        }

    }
}
