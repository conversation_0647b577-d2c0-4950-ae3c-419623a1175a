using System.Diagnostics.CodeAnalysis;
using Phoenix.ProjectSS.TimingWheel;

namespace Phoenix.Server.Utils.Concurrent;

public class DelayQueue<T> where T : class, IDelayItem
{
    private readonly object m_lock = new();

    private readonly SortedQueue<T> m_sortedItems = new();

    /// <summary>
    ///     cur thread wait 4 peek item
    /// </summary>
    private Thread? m_waitThread;

    public int Count
    {
        get
        {
            lock (m_lock)
            {
                return m_sortedItems.Count;
            }
        }
    }

    public bool IsEmpty => Count == 0;

    public bool TryAdd(T item) => TryAdd(item, Timeout.InfiniteTimeSpan, CancellationToken.None);

    public bool TryAdd(T item, CancellationToken cancelToken) => TryAdd(item, Timeout.InfiniteTimeSpan, cancelToken);

    public bool TryAdd(T item, TimeSpan timeout) => TryAdd(item, timeout, CancellationToken.None);

    private bool TryAdd(T item, TimeSpan timeout, CancellationToken cancelToken)
    {
        if (item == null)
        {
            throw new ArgumentNullException(nameof(item));
        }

        if (IsTimeout(timeout, cancelToken))
        {
            throw new ArgumentException("Method execute timeout or cancelled");
        }

        if (!Monitor.TryEnter(m_lock, timeout))
        {
            return false;
        }

        if (cancelToken.IsCancellationRequested)
        {
            Monitor.Exit(m_lock);
            return false;
        }

        try
        {
            if (!m_sortedItems.TryAdd(item))
            {
                return false;
            }

            if (Peek() == item)
            {
                m_waitThread = null;
                Monitor.Pulse(m_lock);
            }

            return true;
        }
        finally
        {
            Monitor.Exit(m_lock);
        }
    }

    public void Clear()
    {
        lock (m_lock)
        {
            m_sortedItems.Clear();
        }
    }

    /// <summary>is timeout</summary>
    /// <param name="timeout"></param>
    /// <param name="cancelToken"></param>
    /// <returns></returns>
    private bool IsTimeout(TimeSpan timeout, CancellationToken cancelToken) =>
        (timeout <= TimeSpan.Zero && timeout != Timeout.InfiniteTimeSpan) || cancelToken.IsCancellationRequested;

    #region peek

    private T? Peek()
    {
        lock (m_lock)
        {
            return m_sortedItems.FirstOrDefault();
        }
    }

    private bool TryPeek([MaybeNullWhen(false)] out T item)
    {
        item = Peek();
        return item != null;
    }

    #endregion

    #region take

    public bool TryTake([MaybeNullWhen(false)] out T item) => TryTake(out item, CancellationToken.None);

    public bool TryTake([MaybeNullWhen(false)] out T item, TimeSpan timeout) =>
        TryTake(out item, timeout, CancellationToken.None);

    public bool TryTake([MaybeNullWhen(false)] out T item, CancellationToken cancelToken)
    {
        item = default;
        if (!Monitor.TryEnter(m_lock))
        {
            return false;
        }

        if (cancelToken.IsCancellationRequested)
        {
            Monitor.Exit(m_lock);
            return false;
        }

        try
        {
            while (!cancelToken.IsCancellationRequested)
            {
                if (!TryPeek(out item))
                {
                    Monitor.Wait(m_lock);
                }
                else
                {
                    TimeSpan delaySpan = item.GetDelaySpan();
                    if (delaySpan <= TimeSpan.Zero)
                    {
                        return m_sortedItems.Remove(item);
                    }

                    item = default;
                    if (m_waitThread != null)
                    {
                        Monitor.Wait(m_lock);
                    }
                    else
                    {
                        Thread currentThread = Thread.CurrentThread;
                        m_waitThread = currentThread;
                        try
                        {
                            Monitor.Wait(m_lock, delaySpan);
                        }
                        finally
                        {
                            if (m_waitThread == currentThread)
                            {
                                m_waitThread = null;
                            }
                        }
                    }
                }
            }

            return false;
        }
        finally
        {
            if (m_waitThread == null && m_sortedItems.Count > 0)
            {
                Monitor.Pulse(m_lock);
            }

            Monitor.Exit(m_lock);
        }
    }

    private bool TryTake([MaybeNullWhen(false)] out T item, TimeSpan timeout, CancellationToken cancelToken)
    {
        item = default;
        if (IsTimeout(timeout, cancelToken))
        {
            throw new ArgumentException("Method execute timeout or cancelled");
        }

        if (!Monitor.TryEnter(m_lock, timeout))
        {
            return false;
        }

        if (IsTimeout(timeout, cancelToken))
        {
            Monitor.Exit(m_lock);
            return false;
        }

        try
        {
            while (!IsTimeout(timeout, cancelToken))
            {
                if (!TryPeek(out item))
                {
                    timeout = MonitorExt.Wait(m_lock, timeout);
                }
                else
                {
                    TimeSpan delaySpan = item.GetDelaySpan();
                    if (delaySpan <= TimeSpan.Zero)
                    {
                        return m_sortedItems.Remove(item);
                    }

                    item = default;
                    if (timeout < delaySpan || m_waitThread != null)
                    {
                        timeout = MonitorExt.Wait(m_lock, timeout);
                    }
                    else
                    {
                        Thread currentThread = Thread.CurrentThread;
                        m_waitThread = currentThread;
                        try
                        {
                            TimeSpan timeSpan = MonitorExt.Wait(m_lock, delaySpan);
                            timeout -= delaySpan - timeSpan;
                        }
                        finally
                        {
                            if (m_waitThread == currentThread)
                            {
                                m_waitThread = null;
                            }
                        }
                    }
                }
            }

            return false;
        }
        finally
        {
            if (m_waitThread == null && m_sortedItems.Count > 0)
            {
                Monitor.Pulse(m_lock);
            }

            Monitor.Exit(m_lock);
        }
    }

    public bool TryTakeNoBlocking([MaybeNullWhen(false)] out T item)
    {
        lock (m_lock)
        {
            item = Peek();
            if (item != null && !(item.GetDelaySpan() > TimeSpan.Zero))
            {
                return m_sortedItems.Remove(item);
            }

            item = default;
            return false;
        }
    }

    #endregion
}
