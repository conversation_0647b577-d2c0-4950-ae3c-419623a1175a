namespace Phoenix.Server.Utils
{
    public class SequenceIntGenerator
    {
        private int SeqId;
        private readonly int? exceptSeq;

        public SequenceIntGenerator(int? exceptSeq)
        {
            SeqId = 0;
            this.exceptSeq = exceptSeq;
        }

        public int GetNextSequenceId()
        {
            int newSeq;
            int currentSeq;
            do
            {
                currentSeq = SeqId;

                newSeq = (currentSeq + 1) % int.MaxValue;

                if (exceptSeq != null && exceptSeq == newSeq)
                {
                    newSeq++;
                }

                // 试图原子性地修改 _seq 变量，如果成功，则返回 newSeq
            } while (Interlocked.CompareExchange(ref SeqId, newSeq, currentSeq) != currentSeq);

            return newSeq;
        }
    }
}
