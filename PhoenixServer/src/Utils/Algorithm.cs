using System.Diagnostics;
using System.Numerics;
using Phoenix.Server.Utils.Concurrent;

namespace Phoenix.Server.Utils;

public static class Algorithm
{
    public static T RandomItemFromList<T>(this List<T> list) where T : notnull
    {
        int idx = ConcurrentRandom.NextInt(0, list.Count);
        return list[idx];
    }

    public static T? RandomWeightedItemFromSequence<T>(this IEnumerable<Tuple<T,int>> itemWithWeight) where T : notnull
    {
        var weightedItems = new List<WeightedItem<T>>();
        foreach (Tuple<T,int> tuple in itemWithWeight)
        {
            var weightedItem = new WeightedItem<T>(tuple.Item1, tuple.Item2);
            weightedItems.Add(weightedItem);
        }

        // Calculate total weight
        int totalWeight = 0;
        foreach (var weightedItem in weightedItems)
        {
            totalWeight += weightedItem.Weight;
        }

        // Generate a random value within the total weight range
        int randomValue = ConcurrentRandom.NextInt(totalWeight);

        // Select an item based on the random value and weights
        foreach (var weightedItem in weightedItems)
        {
            if (randomValue < weightedItem.Weight)
            {
                return weightedItem.Item;
            }
            randomValue -= weightedItem.Weight;
        }

        return default;
    }

    public static T RandomItemFromArray<T>(this T[] array) where T : notnull
    {
        int idx = ConcurrentRandom.NextInt(0, array.Length);
        return array[idx];
    }

    public static T? RandomItemFromSequenceConditional<T>(this IEnumerable<T> sequence, Func<T, bool> condition)
        where T : notnull
    {
        int satisfied = 0;
        T? picked = default;
        foreach (T item in sequence)
        {
            if (!condition(item))
            {
                continue;
            }

            ++satisfied;
            if (ConcurrentRandom.NextInt(0, satisfied) == 0)
            {
                picked = item;
            }
        }

        return picked;
    }

    public static List<T> RandomNItemsFromSequenceConditional<T>(this IEnumerable<T> sequence, int n,
        Func<T, bool> condition)
        where T : notnull
    {
        int satisfied = 0;
        List<T> picked = new List<T>(n);
        foreach (T item in sequence)
        {
            if (!condition(item))
            {
                continue;
            }

            ++satisfied;
            int rn = ConcurrentRandom.NextInt(0, satisfied);
            if (rn >= n)
            {
                continue;
            }

            if (satisfied <= n)
            {
                picked.Add(item);
            }
            else
            {
                picked[rn] = item;
            }
        }

        return picked;
    }

    public static IEnumerable<uint> RandomUIntSequence(uint exclusiveMax)
    {
        if (exclusiveMax == 0)
        {
            throw new ArgumentException("exclusiveMax is not allowed to equal 0");
        }

        if (exclusiveMax == 1)
        {
            yield return 0;
            yield break;
        }

        if (exclusiveMax == 2)
        {
            uint n = ConcurrentRandom.NextUInt(2);
            yield return n;
            yield return 1 - n;
            yield break;
        }

        int leadingZeros = BitOperations.LeadingZeroCount(exclusiveMax - 1);
        int bits = leadingZeros > 26 ? 6 : 32 - leadingZeros;

        uint upperBound = (uint)1 << bits;
        uint mask = upperBound - 1;
        int shift = bits - 1;

        uint a = ConcurrentRandom.NextUInt(upperBound);
        uint b = ConcurrentRandom.NextUInt(upperBound) | 1;
        uint c = ConcurrentRandom.NextUInt(upperBound) | 1;
        uint d = ConcurrentRandom.NextUInt(upperBound) | 1;
        foreach (uint n in RandomUIntSequence(exclusiveMax, mask, shift, a, b, c, d))
        {
            yield return n;
        }
    }

    private static IEnumerable<uint> RandomUIntSequence(uint exclusiveMax, uint mask, int shift, uint a, uint b, uint c,
        uint d)
    {
        Debug.Assert(
            BitOperations.IsPow2(mask + 1) &&
            shift + 1 == BitOperations.Log2(mask + 1) &&
            (b & 1) == 1 &&
            (c & 1) == 1 &&
            (d & 1) == 1);

        uint i = 0;
        uint count = 0;
        while (count < exclusiveMax)
        {
            uint x = i;
            x = (x + a) & mask;
            x = (x ^ (x >> shift)) & mask;

            x = (x * b) & mask;
            x = (x ^ (x >> shift)) & mask;

            x = (x * c) & mask;
            x = (x ^ (x >> shift)) & mask;

            x = (x * d) & mask;
            x = (x ^ (x >> shift)) & mask;
            if (x < exclusiveMax)
            {
                ++count;
                yield return x;
            }

            ++i;
        }
    }

    class WeightedItem<T>
    {
        /// <summary>
        /// Gets the item.
        /// </summary>
        public T Item { get; }

        /// <summary>
        /// Gets the weight assigned to the item.
        /// </summary>
        public int Weight { get; }

        /// <summary>
        /// Initializes a new instance of the WeightedItem class with the specified item and weight.
        /// </summary>
        /// <param name="item">The item.</param>
        /// <param name="weight">The weight assigned to the item.</param>
        public WeightedItem(T item, int weight)
        {
            Item = item;
            Weight = weight;
        }
    }
}
