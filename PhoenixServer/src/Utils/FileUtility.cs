using System.Text;

namespace Phoenix.Server.Utils;

public class FileUtility
{
    /// <summary>
    ///     read text
    /// </summary>
    /// <param name="fileName"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public static string ReadText(string fileName)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            throw new ArgumentNullException(nameof(fileName));
        }

        string txt = File.ReadAllText(fileName, Encoding.UTF8);
        if (!string.IsNullOrEmpty(txt))
        {
            return txt;
        }

        return string.Empty;
    }

    public static bool WriteText(string fileName, string txt)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            throw new ArgumentNullException(nameof(fileName));
        }

        File.WriteAllText(fileName, txt, Encoding.UTF8);
        return true;
    }

    public static bool AppendText(string fileName, string txt)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            return false;
        }

        try
        {
            File.AppendAllText(fileName, txt, Encoding.UTF8);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public static byte[]? ReadBytes(string fileName)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            return null;
        }

        try
        {
            return File.ReadAllBytes(fileName);
        }
        catch
        {
            return null;
        }
    }

    public static bool WriteBytes(string fileName, byte[] bytes)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            return false;
        }

        if (bytes == null || bytes.Length <= 0)
        {
            return false;
        }

        try
        {
            File.WriteAllBytes(fileName, bytes);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public static bool WriteLines(string fileName, string[] strList)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            return false;
        }

        if (strList == null || strList.Length <= 0)
        {
            return false;
        }

        try
        {
            File.WriteAllLines(fileName, strList, Encoding.UTF8);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public static bool DeleteFile(string fileName)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            return false;
        }

        try
        {
            File.Delete(fileName);
            return true;
        }
        catch
        {
            return false;
        }
    }

    public static bool IsFileExist(string fileName)
    {
        if (string.IsNullOrEmpty(fileName))
        {
            return false;
        }

        try
        {
            FileInfo fileInfo = new FileInfo(fileName);
            return fileInfo.Exists;
        }
        catch
        {
            return false;
        }
    }

    public static void CreatePath(string path)
    {
        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
        }
    }
}
