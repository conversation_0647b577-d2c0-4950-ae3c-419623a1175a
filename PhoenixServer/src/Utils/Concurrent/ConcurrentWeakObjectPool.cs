using System.Collections.Concurrent;

namespace Phoenix.Server.Utils.Concurrent;

public class ConcurrentWeakObjectPool<T> where T : class
{
    private readonly Action<T> m_disposer;
    private readonly Func<T> m_factory;
    private readonly ConcurrentQueue<WeakReference> m_queue = new();

    public ConcurrentWeakObjectPool(Func<T> factory, Action<T> disposer)
    {
        m_factory = factory;
        m_disposer = disposer;
    }

    public T GetOrCreate()
    {
        while (m_queue.TryDequeue(out WeakReference? obj))
        {
            if (obj.Target != null)
            {
                return (T)obj.Target;
            }
        }

        return m_factory();
    }

    public void Return(T obj)
    {
        m_disposer(obj);
        m_queue.Enqueue(new WeakReference(obj, false));
    }
}
