using System.Collections.Concurrent;

namespace Phoenix.Server.Utils.Concurrent;

/// <summary>
///     可以await的队列
/// </summary>
/// <typeparam name="T"></typeparam>
public class AwaitableQueue<T>
{
    private readonly ConcurrentQueue<TaskCompletionSource<T>> m_pendingTaskQueue = new();

    private readonly ConcurrentQueue<T> m_queue = new();
    private bool m_isShutdown;

    /// <summary>
    ///     队列的长度
    /// </summary>
    public int Count => m_queue.Count;

    /// <summary>
    ///     Enqueue the specified item. Blocking operation.
    /// </summary>
    /// <param name="item">Item.</param>
    public void Enqueue(T item)
    {
        if (m_isShutdown)
        {
            return;
        }

        // 加锁的原因：当 两个线程导致 执行顺序是 1C 2B 2A 1D 会导致 A后投入的消息先于B执行
        lock (this)
        {
            // A
            TaskCompletionSource<T> result;
            if (m_pendingTaskQueue.TryDequeue(out result!))
            {
                result.SetResultAsync(item);
            }
            else
            {
                // B
                m_queue.Enqueue(item);
            }
        }
    }

    public async Task<T> DequeueAsync()
    {
        T result = default!;

        if (m_isShutdown)
        {
            return result;
        }

        TaskCompletionSource<T> tcs;
        // 加锁的原因：当 两个线程导致 执行顺序是 1C 2B 2A 1D 会导致 A后投入的消息先于B执行
        lock (this)
        {
            // C
            if (m_queue.TryDequeue(out result!))
            {
                return result;
            }

            // D
            tcs = new TaskCompletionSource<T>();
            m_pendingTaskQueue.Enqueue(tcs);
        }

        return tcs.Task.IsCompleted ? tcs.Task.Result : await tcs.Task;
    }

    /// <summary>
    ///     同步Dequeue
    /// </summary>
    /// <param name="t"></param>
    /// <returns></returns>
    public bool TryDequeue(out T? t) => m_queue.TryDequeue(out t);

    /// <summary>
    ///     取消所有正在进行的等待
    /// </summary>
    public void Shutdown()
    {
        m_isShutdown = true;

        CancelWaitingDequeue();
    }

    /// <summary>
    ///     清理等待task
    /// </summary>
    public void CancelWaitingDequeue()
    {
        lock (this)
        {
            while (true)
            {
                if (m_pendingTaskQueue.TryDequeue(out TaskCompletionSource<T>? tcs))
                {
                    tcs.SetCanceled();
                }
                else
                {
                    break;
                }
            }
        }
    }

    /// <summary>
    ///     插入到队列的最前面
    /// </summary>
    /// <param name="item"></param>
    public void PushItem2Top(T item)
    {
        lock (this)
        {
            List<T> tempList = m_queue.ToList();
            m_queue.Clear();
            // 插入头部
            tempList.Insert(0, item);
            foreach (T it in tempList)
            {
                m_queue.Enqueue(it);
            }
        }
    }
}
