namespace Phoenix.Framework.Utilities.Concurrent;

public class AtomicInt : IEquatable<int>
{
    private int m_value;

    public AtomicInt(int value = default) => m_value = value;

    public bool Equals(int other) => Get().Equals(other);

    public int Get() => m_value;

    public int Increment() => Interlocked.Increment(ref m_value);

    public int Decrement() => Interlocked.Decrement(ref m_value);
}
