namespace Phoenix.Server.Utils.Concurrent;

public class AtomicLong : IComparable, IComparable<long>, IEquatable<long>
{
    private long m_value;

    public AtomicLong(long value = default) => m_value = value;

    public int CompareTo(object? obj)
    {
        if (obj == null)
        {
            return 1;
        }

        if (obj is AtomicLong value)
        {
            return Get().CompareTo(value.Get());
        }

        throw new ArgumentException($"Object is not a {nameof(AtomicLong)}");
    }

    public int CompareTo(long other) => Get().CompareTo(other);

    public bool Equals(long other) => Get().Equals(other);

    /// <summary>
    ///     get
    /// </summary>
    /// <returns></returns>
    public long Get() => Interlocked.Read(ref m_value);

    /// <summary>
    ///     set
    /// </summary>
    /// <param name="value"></param>
    public void Set(long value) => Interlocked.Exchange(ref m_value, value);

    /// <summary>
    ///     set and return
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    public long GetAndSet(long value) => Interlocked.Exchange(ref m_value, value);
}
