using System.Collections.Concurrent;
using System.Diagnostics;

namespace Phoenix.Server.Utils.Concurrent;

public static class ConcurrentExecuteQueueManager
{
    private static readonly ConcurrentDictionary<string, QueuesInfo> m_queuesInfoByCategory = new();

    public static void InitQueueCategory<TKey, TItem>(string category, Func<TItem, Task> executeHandler,
        Func<TItem, Exception, Task> exceptionHandler, int? expireSeconds = null)
        where TKey : notnull
        where TItem : notnull
    {
        ConcurrentDictionary<TKey, ConcurrentExecuteQueue<TItem>> queues = new();
        Func<TKey, ConcurrentExecuteQueue<TItem>[], ConcurrentExecuteQueue<TItem>> factory;
        Type queuesType = typeof(ConcurrentDictionary<TKey, ConcurrentExecuteQueue<TItem>>);
        Type factoryType = typeof(Func<TKey, ConcurrentExecuteQueue<TItem>[], ConcurrentExecuteQueue<TItem>>);

        if (expireSeconds.HasValue)
        {
            factory = (key, value) => new ConcurrentExecuteQueue<TItem>(execute<PERSON><PERSON><PERSON>, exceptionHandler,
                expireSeconds.Value,
                () => { queues.TryRemove(new KeyValuePair<TKey, ConcurrentExecuteQueue<TItem>>(key, value[0])); });
        }
        else
        {
            factory = (key, value) => new ConcurrentExecuteQueue<TItem>(executeHandler, exceptionHandler);
        }

        if (!m_queuesInfoByCategory.TryAdd(category, new QueuesInfo(queues, factory, queuesType, factoryType)))
        {
            throw new InvalidOperationException($"ConcurrentExecuteQueue category {category} already exists");
        }
    }

    public static void Enqueue<TKey, TItem>(string category, TKey key, TItem item)
        where TKey : notnull
        where TItem : notnull
    {
        if (!m_queuesInfoByCategory.TryGetValue(category, out QueuesInfo? queuesInfo))
        {
            throw new ArgumentOutOfRangeException($"ConcurrentExecuteQueue category {category} not exists");
        }

        Debug.Assert(queuesInfo.QueuesType ==
                     typeof(ConcurrentDictionary<TKey, ConcurrentExecuteQueue<TItem>>));
        Debug.Assert(queuesInfo.FactoryType ==
                     typeof(Func<TKey, ConcurrentExecuteQueue<TItem>[],
                         ConcurrentExecuteQueue<TItem>>));

        ConcurrentDictionary<TKey, ConcurrentExecuteQueue<TItem>> queues =
            (ConcurrentDictionary<TKey, ConcurrentExecuteQueue<TItem>>)queuesInfo.Queues;
        if (queues.TryGetValue(key, out ConcurrentExecuteQueue<TItem>? queue))
        {
            if (queue.Enqueue(item))
            {
                return;
            }
        }

        Func<TKey, ConcurrentExecuteQueue<TItem>[], ConcurrentExecuteQueue<TItem>> factory =
            (Func<TKey, ConcurrentExecuteQueue<TItem>[], ConcurrentExecuteQueue<TItem>>)queuesInfo.Factory;
        while (true)
        {
            ConcurrentExecuteQueue<TItem>[] newQueueWrapper = new ConcurrentExecuteQueue<TItem>[1];
            newQueueWrapper[0] = factory(key, newQueueWrapper);
            ConcurrentExecuteQueue<TItem> newQueue = queues.GetOrAdd(key, newQueueWrapper[0]);
            if (newQueue.Enqueue(item))
            {
                return;
            }
        }
    }

    internal class QueuesInfo
    {
        internal readonly object Factory;
        internal readonly Type FactoryType;

        internal readonly object Queues;
        internal readonly Type QueuesType;

        internal QueuesInfo(object queues, object factory, Type queuesType, Type factoryType)
        {
            Queues = queues;
            Factory = factory;
            QueuesType = queuesType;
            FactoryType = factoryType;
        }
    }
}

public class ConcurrentExecuteQueue<T> where T : notnull
{
    private readonly IConcurrentExecuteQueueInterface<T> m_queue;

    public ConcurrentExecuteQueue(Func<T, Task> executeHandler, Func<T, Exception, Task> exceptionHandler) =>
        m_queue = new ConcurrentExecuteQueueImplementPermanent<T>(executeHandler, exceptionHandler);

    public ConcurrentExecuteQueue(Func<T, Task> executeHandler, Func<T, Exception, Task> exceptionHandler,
        int expireSeconds, Action disposeHandler) =>
        m_queue = new ConcurrentExecuteQueueImplementLimitedLifetime<T>(executeHandler, exceptionHandler,
            expireSeconds, disposeHandler);

    public bool Disposed() => m_queue.Disposed();

    public bool Enqueue(T item) => m_queue.Enqueue(item);
}

internal class ConcurrentExecuteQueueImplementPermanent<T> : IConcurrentExecuteQueueInterface<T>
    where T : notnull
{
    private readonly Func<T, Exception, Task> m_exceptionHandler;
    private readonly Func<T, Task> m_executeHandler;
    private readonly AwaitableQueue<T> m_queue = new();

    private long m_count;
    private Task m_worker = default!;

    public ConcurrentExecuteQueueImplementPermanent(Func<T, Task> executeHandler,
        Func<T, Exception, Task> exceptionHandler)
    {
        m_executeHandler = executeHandler;
        m_exceptionHandler = exceptionHandler;
    }

    public bool Disposed() => false;

    public bool Enqueue(T item)
    {
        long incremented = Interlocked.Increment(ref m_count);
        m_queue.Enqueue(item);

        if (incremented != 1)
        {
            return true;
        }

        m_worker = ExecuteAll();
        return true;
    }

    private async Task ExecuteAll()
    {
        while (true)
        {
            T item = await m_queue.DequeueAsync();
            bool quit = false;
            try
            {
                await m_executeHandler(item);
            }
            catch (Exception e)
            {
                await m_exceptionHandler(item, e);
            }
            finally
            {
                quit = Interlocked.Decrement(ref m_count) == 0;
            }

            if (quit)
            {
                return;
            }
        }
    }
}

internal class ConcurrentExecuteQueueImplementLimitedLifetime<T> : IConcurrentExecuteQueueInterface<T>
    where T : notnull
{
    private const long DISPOSE_FLAG = 0x4000000000000000;
    private readonly Action m_disposeHandler;

    private readonly Func<T, Exception, Task> m_exceptionHandler;
    private readonly Func<T, Task> m_executeHandler;
    private readonly long m_expireInterval;
    private readonly AwaitableQueue<T> m_queue = new();

    // in theory, count must smaller then DISPOSE_FLAG, but it is impossible in debug/release/production environment.
    // so we don't have any check or protection on count.
    private long m_disposeBorCount; // == destroy_flag | count
    private int m_disposer;
    private long m_lastProcessTicks = DateTime.UtcNow.Ticks;
    private Task m_worker = default!;

    public ConcurrentExecuteQueueImplementLimitedLifetime(Func<T, Task> executeHandler,
        Func<T, Exception, Task> exceptionHandler, int expireSeconds, Action disposeHandler)
    {
        m_executeHandler = executeHandler;
        m_exceptionHandler = exceptionHandler;
        m_expireInterval = expireSeconds * TimeSpan.TicksPerSecond;
        m_disposeHandler = disposeHandler;
    }

    public bool Disposed() => (m_disposeBorCount & DISPOSE_FLAG) != 0;

    public bool Enqueue(T item)
    {
        long incremented = Interlocked.Increment(ref m_disposeBorCount);
        if ((incremented & DISPOSE_FLAG) !=
            0) // queue destroyed. caller should create a new queue to Enqueue or abort.
        {
            return false;
        }

        m_queue.Enqueue(item);

        if (incremented != 1)
        {
            return true;
        }

        m_worker = ExecuteAll();
        return true;
    }

    private async Task ExecuteAll()
    {
        while (true)
        {
            T item = await m_queue.DequeueAsync();
            bool quit = false;
            try
            {
                await m_executeHandler(item);
            }
            catch (Exception e)
            {
                await m_exceptionHandler(item, e);
            }
            finally
            {
                m_lastProcessTicks = DateTime.UtcNow.Ticks;
                quit = Interlocked.Decrement(ref m_disposeBorCount) == 0;
            }

            if (quit)
            {
                goto ExitLoop;
            }
        }

        ExitLoop:
        if (Interlocked.Exchange(ref m_disposer, 1) == 1)
        {
            return;
        }

        long delay = m_expireInterval;
        while (delay > 0)
        {
            await Task.Delay(TimeSpan.FromTicks(delay));
            delay = m_expireInterval + m_lastProcessTicks - DateTime.UtcNow.Ticks;
        }

        Interlocked.Exchange(ref m_disposer, 0);

        if (Interlocked.CompareExchange(ref m_disposeBorCount, DISPOSE_FLAG, 0) == 0)
        {
            m_disposeHandler();
        }
    }
}

internal interface IConcurrentExecuteQueueInterface<in T> where T : notnull
{
    bool Disposed();
    bool Enqueue(T item);
}
