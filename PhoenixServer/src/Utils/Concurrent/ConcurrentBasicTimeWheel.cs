/*namespace Phoenix.Server.Utils.Concurrent;

/// <summary>
///     A class that represents a concurrent timer wheel.
/// </summary>
public class ConcurrentBasicTimeWheel
{
    private static readonly ILog m_logger = LogManager.GetLogger("ConcurrentBasicTimeWheel");
    private readonly Task m_loopTask;
    private readonly ConcurrentExecuteQueue<TimeWheelTimer> m_timerCallbackQueue;
    private readonly ConcurrentExecuteQueue<Func<Task>> m_timerQueue;
    private readonly SortedSet<TimeWheelTimer>[] m_wheel;
    private long m_idSeq;
    private long m_nextTriggeredTimeStamp;

    public ConcurrentBasicTimeWheel(int sizeInSeconds)
    {
        m_wheel = new SortedSet<TimeWheelTimer>[sizeInSeconds];
        for (int i = 0; i < sizeInSeconds; i++)
        {
            m_wheel[i] = new SortedSet<TimeWheelTimer>(Comparer<TimeWheelTimer>.Create((x, y) =>
            {
                if (x.m_id == y.m_id)
                {
                    return 0;
                }

                return x.m_id < y.m_id ? -1 : 1;
            }));
        }

        m_nextTriggeredTimeStamp = DateTimeHelper.GetTimestampSeconds();
        m_timerQueue = new ConcurrentExecuteQueue<Func<Task>>(
            async f => { await f(); },
            async (f, e) => { await OnAddTimerException(e); }
        );

        m_timerCallbackQueue = new ConcurrentExecuteQueue<TimeWheelTimer>(
            async timer => { await timer.m_callback(); },
            async (timer, e) => { await timer.OnCallbackException(e); }
        );

        m_loopTask = Loop();
    }

    private async Task Loop()
    {
        while (true)
        {
            long now = DateTimeHelper.GetTimestampSeconds();
            await AdvanceToTimestamp(now);
            await Task.Delay(1000);
        }
    }

    /// <summary>
    ///     Tries to add a timer to the time wheel.
    /// </summary>
    /// <param name="dueTime">The due time of the timer.</param>
    /// <param name="Callback">The callback function to execute when the timer is due.</param>
    /// <returns>
    ///     A tuple containing a boolean indicating whether the operation was successful, and the timer that was added (or
    ///     null if the operation failed).
    /// </returns>
    public async Task<(bool, TimeWheelTimer)> TryAddTimer(DateTime dueTime, Func<Task> Callback)
    {
        long dueTimestamp = DateTimeHelper.GetTimestampSeconds(dueTime);
        return await TryAddTimer(dueTimestamp, Callback);
    }

    /// <summary>
    ///     Tries to add a timer to the time wheel.
    /// </summary>
    /// <param name="dueTimestamp">The due timestamp of the timer.</param>
    /// <param name="Callback">The callback function to execute when the timer is due.</param>
    /// <returns>
    ///     A tuple containing a boolean indicating whether the operation was successful, and the timer that was added (or
    ///     null if the operation failed).
    /// </returns>
    public async Task<(bool, TimeWheelTimer)> TryAddTimer(long dueTimestamp, Func<Task> Callback)
    {
        long nowTimestamp = DateTimeHelper.GetTimestampSeconds();
        if (dueTimestamp <= nowTimestamp)
        {
            throw new ArgumentOutOfRangeException(nameof(dueTimestamp),
                "Attempting to add an expired/expiring timer is not allowed.");
        }

        if (dueTimestamp > m_wheel.Length + nowTimestamp)
        {
            throw new ArgumentOutOfRangeException(
                $"Attempting to add a timer that is set for {m_wheel.Length} seconds in the future is not allowed.");
        }

        TaskCompletionSource<(bool, TimeWheelTimer)> tcs = new();
        m_timerQueue.Enqueue(() =>
        {
            bool valid = m_nextTriggeredTimeStamp <= dueTimestamp &&
                         dueTimestamp < m_nextTriggeredTimeStamp + m_wheel.Length;
            if (!valid)
            {
                tcs.SetResult((false, default!));
                m_logger.Warn(
                    $"ConcurrentBasicTimeWheel::TryAddTimer dueTimestamp={dueTimestamp} is not in a valid range [{m_nextTriggeredTimeStamp}, {m_nextTriggeredTimeStamp + m_wheel.Length}]. This may hint a performance drawback.");
                return Task.CompletedTask;
            }

            ++m_idSeq;
            TimeWheelTimer timer = new(m_idSeq, dueTimestamp, Callback);
            long index = timer.m_dueTimestamp % m_wheel.Length;
            bool succeed = m_wheel[index].Add(timer);
            tcs.SetResult((succeed, timer));
            return Task.CompletedTask;
        });

        return await tcs.Task;
    }

    private Task OnAddTimerException(Exception e)
    {
        m_logger.Error(e);
        return Task.CompletedTask;
    }

    /// <summary>
    ///     Tries to remove a timer from the time wheel.
    /// </summary>
    /// <param name="timer">The timer to remove.</param>
    /// <returns>A boolean indicating whether the operation was successful.</returns>
    public async Task<bool> TryRemoveTimer(TimeWheelTimer timer)
    {
        TaskCompletionSource<bool> tcs = new();
        m_timerQueue.Enqueue(() =>
        {
            long index = timer.m_dueTimestamp % m_wheel.Length;
            SortedSet<TimeWheelTimer> slot = m_wheel[index];
            bool succeed = slot.Remove(timer);
            if (!succeed)
            {
                m_logger.Info($"ConcurrentBasicTimeWheel::TryRemoveTimer failed. timer={timer.m_id} not exist.");
            }

            tcs.SetResult(succeed);
            return Task.CompletedTask;
        });

        return await tcs.Task;
    }

    /// <summary>
    ///     Advances the time wheel to a specified target time.
    /// </summary>
    /// <param name="targetTimestamp">The target time to advance to.</param>
    public async Task AdvanceToTimestamp(long targetTimestamp)
    {
        if (targetTimestamp < m_nextTriggeredTimeStamp)
        {
            return;
        }

        TaskCompletionSource tcs = new();

        m_timerQueue.Enqueue(() =>
        {
            while (m_nextTriggeredTimeStamp <= targetTimestamp)
            {
                long index = m_nextTriggeredTimeStamp % m_wheel.Length;
                SortedSet<TimeWheelTimer> slot = m_wheel[index];
                foreach (TimeWheelTimer dueTimer in slot)
                {
                    m_timerCallbackQueue.Enqueue(dueTimer);
                }

                slot.Clear();
                ++m_nextTriggeredTimeStamp;
            }

            tcs.SetResult();
            return Task.CompletedTask;
        });

        await tcs.Task;
    }

    /// <summary>
    ///     Advances the time wheel to a specified target time.
    /// </summary>
    /// <param name="targetMoment">The target time to advance to.</param>
    public async Task AdvanceTo(DateTime targetMoment)
    {
        long targetTimestamp = DateTimeHelper.GetTimestampSeconds(targetMoment);
        await AdvanceToTimestamp(targetTimestamp);
    }
}

public class TimeWheelTimer
{
    private static readonly ILog m_logger = LogManager.GetLogger("TimeWheelTimer");
    public readonly Func<Task> m_callback;
    public long m_dueTimestamp;
    public long m_id;

    public TimeWheelTimer(long id, long dueTimestamp, Func<Task> Callback)
    {
        m_id = id;
        m_dueTimestamp = dueTimestamp;
        m_callback = Callback;
    }

    public Task OnCallbackException(Exception e)
    {
        m_logger.Error(e);
        return Task.CompletedTask;
    }
}
*/
