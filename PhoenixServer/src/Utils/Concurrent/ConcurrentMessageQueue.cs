using System.Collections.Concurrent;
using System.Diagnostics;

namespace Phoenix.Server.Utils.Concurrent;

public static class ConcurrentMessageQueueManager
{
    private static readonly ConcurrentDictionary<string, QueuesInfo> m_queuesInfoByCategory = new();

    public static void InitQueueCategory<TKey, TRequest, TResponse>(string category, int? expireSeconds = null)
        where TKey : notnull
        where TRequest : notnull
        where TResponse : notnull
    {
        ConcurrentDictionary<TKey, ConcurrentMessageQueue<TRequest, TResponse>> queues = new();
        Func<TKey, ConcurrentMessageQueue<TRequest, TResponse>[], ConcurrentMessageQueue<TRequest, TResponse>> factory;
        Type queuesType = typeof(ConcurrentDictionary<TKey, ConcurrentMessageQueue<TRequest, TResponse>>);
        Type factoryType =
            typeof(Func<TKey, ConcurrentMessageQueue<TRequest, TResponse>[],
                ConcurrentMessageQueue<TRequest, TResponse>>);

        if (expireSeconds.HasValue)
        {
            factory = (key, value) => new ConcurrentMessageQueue<TRequest, TResponse>(expireSeconds.Value,
                () =>
                {
                    queues.TryRemove(
                        new KeyValuePair<TKey, ConcurrentMessageQueue<TRequest, TResponse>>(key, value[0]));
                });
        }
        else
        {
            factory = (key, value) => new ConcurrentMessageQueue<TRequest, TResponse>();
        }

        if (!m_queuesInfoByCategory.TryAdd(category, new QueuesInfo(queues, factory, queuesType, factoryType)))
        {
            throw new InvalidOperationException($"ConcurrentMessageQueue category {category} already exists");
        }
    }

    public static TaskCompletionSource<TResponse> Enqueue<TKey, TRequest, TResponse>(string category, TKey key,
        IConcurrentMessageQueueMessage<TRequest, TResponse> request)
        where TKey : notnull
        where TRequest : notnull
        where TResponse : notnull
    {
        if (!m_queuesInfoByCategory.TryGetValue(category, out QueuesInfo? queuesInfo))
        {
            throw new ArgumentOutOfRangeException($"ConcurrentMessageQueue category {category} not exists");
        }

        Debug.Assert(queuesInfo.QueuesType ==
                     typeof(ConcurrentDictionary<TKey, ConcurrentMessageQueue<TRequest, TResponse>>));
        Debug.Assert(queuesInfo.FactoryType ==
                     typeof(Func<TKey, ConcurrentMessageQueue<TRequest, TResponse>[],
                         ConcurrentMessageQueue<TRequest, TResponse>>));

        ConcurrentDictionary<TKey, ConcurrentMessageQueue<TRequest, TResponse>> queues =
            (ConcurrentDictionary<TKey, ConcurrentMessageQueue<TRequest, TResponse>>)queuesInfo.Queues;
        if (queues.TryGetValue(key, out ConcurrentMessageQueue<TRequest, TResponse>? queue))
        {
            if (queue.TryEnqueue(request, out TaskCompletionSource<TResponse> tcs))
            {
                return tcs;
            }
        }

        Func<TKey, ConcurrentMessageQueue<TRequest, TResponse>[], ConcurrentMessageQueue<TRequest, TResponse>> factory =
            (Func<TKey, ConcurrentMessageQueue<TRequest, TResponse>[], ConcurrentMessageQueue<TRequest, TResponse>>)
            queuesInfo.Factory;
        while (true)
        {
            ConcurrentMessageQueue<TRequest, TResponse>[] newQueueWrapper =
                new ConcurrentMessageQueue<TRequest, TResponse>[1];
            newQueueWrapper[0] = factory(key, newQueueWrapper);
            ConcurrentMessageQueue<TRequest, TResponse> newQueue = queues.GetOrAdd(key, newQueueWrapper[0]);
            if (newQueue.TryEnqueue(request, out TaskCompletionSource<TResponse> tcs))
            {
                return tcs;
            }
        }
    }

    internal class QueuesInfo
    {
        internal readonly object Factory;
        internal readonly Type FactoryType;

        internal readonly object Queues;
        internal readonly Type QueuesType;

        internal QueuesInfo(object queues, object factory, Type queuesType, Type factoryType)
        {
            Queues = queues;
            Factory = factory;
            QueuesType = queuesType;
            FactoryType = factoryType;
        }
    }
}

public interface IConcurrentMessageQueueMessage<TRequest, TResponse>
{
    Task Execute(TaskCompletionSource<TResponse> tcs);
    Task OnException(Exception e);
}

internal class MessageAndTaskCompletionSource<TRequest, TResponse>
{
    internal readonly IConcurrentMessageQueueMessage<TRequest, TResponse> Message;
    internal readonly TaskCompletionSource<TResponse> Tcs;

    internal MessageAndTaskCompletionSource(IConcurrentMessageQueueMessage<TRequest, TResponse> message,
        TaskCompletionSource<TResponse> tcs)
    {
        Message = message;
        Tcs = tcs;
    }
}

public class ConcurrentMessageQueue<TRequest, TResponse>
    where TRequest : notnull
{
    private readonly ConcurrentExecuteQueue<MessageAndTaskCompletionSource<TRequest, TResponse>> m_queue;

    public ConcurrentMessageQueue() =>
        m_queue = new ConcurrentExecuteQueue<MessageAndTaskCompletionSource<TRequest, TResponse>>(
            async item => { await item.Message.Execute(item.Tcs); },
            async (item, e) =>
            {
                await item.Message.OnException(e);
                item.Tcs.SetException(e);
            }
        );

    public ConcurrentMessageQueue(int expireSeconds, Action disposeHandler) =>
        m_queue = new ConcurrentExecuteQueue<MessageAndTaskCompletionSource<TRequest, TResponse>>(
            async item => { await item.Message.Execute(item.Tcs); },
            async (item, e) =>
            {
                await item.Message.OnException(e);
                item.Tcs.SetException(e);
            },
            expireSeconds,
            disposeHandler
        );

    public bool TryEnqueue(IConcurrentMessageQueueMessage<TRequest, TResponse> message,
        out TaskCompletionSource<TResponse> tcs)
    {
        tcs = new TaskCompletionSource<TResponse>();
        return m_queue.Enqueue(new MessageAndTaskCompletionSource<TRequest, TResponse>(message, tcs));
    }
}
