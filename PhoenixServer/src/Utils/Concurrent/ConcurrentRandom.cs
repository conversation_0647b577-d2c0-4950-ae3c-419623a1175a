namespace Phoenix.Server.Utils.Concurrent;

public static class ConcurrentRandom
{
    private static readonly Random s_random = Random.Shared;
    public static int NextInt() => s_random.Next();

    public static int NextInt(int maxValue) => s_random.Next(maxValue);

    public static int NextInt(int minValue, int maxValue) => s_random.Next(minValue, maxValue);

    public static uint NextUInt() => (uint)s_random.Next();

    public static uint NextUInt(uint maxValue) => (uint)s_random.Next((int)maxValue);

    public static uint NextUInt(uint minValue, uint maxValue) => (uint)s_random.Next((int)minValue, (int)maxValue);
}
