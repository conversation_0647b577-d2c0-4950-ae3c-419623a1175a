namespace Phoenix.Server.Utils.Concurrent;
// All comments are generated by chatgpt. Don't blame author for trash comments.
// Regarding the correctness of the implemenation, take a look at specs in /ProjectSS/TLA+/ConcurrentSingleConsumerQueue

/// <summary>
///     A thread-safe, single-consumer queue class that provides basic functionality for enqueueing and dequeueing items.
/// </summary>
/// <typeparam name="T">The type of items to be stored in the queue.</typeparam>
public class ConcurrentSingleConsumerQueue<T>
{
    // Head and tail nodes of the queue.
    private QueueNode<T> m_head;
    private QueueNode<T> m_tail;

    /// <summary>
    ///     Initializes a new instance of the <see cref="ConcurrentSingleConsumerQueue{T}" /> class.
    /// </summary>
    public ConcurrentSingleConsumerQueue() =>
        // Initialize the head and tail pointers with a dummy node.
        m_tail = m_head = new QueueNode<T>(default!);

    /// <summary>
    ///     Enqueues the specified item into the queue.
    /// </summary>
    /// <param name="item">The item to enqueue.</param>
    public void Enqueue(T item)
    {
        // Create a new node for the item.
        QueueNode<T> newHead = new(item);
        // Atomically replace the head with the new node.
        QueueNode<T> prevHead = Interlocked.Exchange(ref m_head, newHead);
        // Link the new node to the previous head.
        Interlocked.Exchange(ref prevHead.Next, newHead);
    }

    /// <summary>
    ///     Tries to dequeue an item from the queue.
    /// </summary>
    /// <param name="item">The dequeued item if available, otherwise the default value of T.</param>
    /// <returns><c>true</c> if an item was dequeued; otherwise, <c>false</c>.</returns>
    public bool TryDequeue(out T item)
    {
        // Get the next node to dequeue.
        QueueNode<T>? dequeuedNode = m_tail.Next;
        // Check if there is no item to dequeue.
        if (dequeuedNode is null)
        {
            item = default!;
            return false;
        }

        // Set the item from the dequeued node.
        item = dequeuedNode.Item;
        // Move the tail pointer to the dequeued node.
        m_tail = dequeuedNode;
        // Clear the item in the dequeued node to release the reference.
        m_tail.Item = default!;
        return true;
    }
}

/// <summary>
///     Internal class to represent a node in the queue.
/// </summary>
/// <typeparam name="T">The type of the item stored in the node.</typeparam>
internal class QueueNode<T>
{
    // Item stored in the node.
    internal T Item;

    // Reference to the next node in the queue.
    internal QueueNode<T>? Next;

    /// <summary>
    ///     Initializes a new instance of the <see cref="QueueNode{T}" /> class.
    /// </summary>
    /// <param name="item">The item to be stored in the node.</param>
    internal QueueNode(T item) => Item = item;
}
