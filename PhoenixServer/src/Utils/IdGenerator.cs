using System.Text;

namespace Phoenix.Server.Utils
{
    public class IdGenerator
    {
        // 32 chars, omit ambiguous 'o', '0', 'l', '1'
        private static readonly char[] g_idEncodeChars = "ABCDEFGHIJKMNPQRSTUVWXYZ23456789".ToCharArray();
        private const int TIMESTEP_BITS = 4;    // every 16ms as a time step
        private const int SEQUENCE_BITS = 11;
        private const long TIMESTAMP_MASK = 0xFFFFFFFFF;
        private const int TIMESTAMP_BITS = 36;
        private const long PROC_ID_MASK = 0xFFFFL;
        private const int PROC_ID_SHIFT = TIMESTAMP_BITS + SEQUENCE_BITS;

        private static readonly Lazy<IdGenerator> instance = new Lazy<IdGenerator>(() => new IdGenerator());

        private readonly object lockObj = new object();
        private long seq = 0;
        private long lastTimeStart = 0;
        private long procID = 0;
        private long timestamp = 0;
        private long segmentNum = 0;
        private EGenMode mode = EGenMode.BY_TIMESTAMP;

        public enum EGenMode
        {
            BY_TIMESTAMP,
            BY_PROC_RUNTIME_ONLY,
            BY_EXTERNAL_SEGMENT,
        }

        public static IdGenerator Instance => instance.Value;

        private IdGenerator()
        {
            // SetLocalProcID(Engine.Instance.GetNodeId());
        }

        public int SetLocalProcID(long procID)
        {
            // make sure in a valid range
            this.procID = (procID & PROC_ID_MASK);
            return procID <= PROC_ID_MASK ? 0 : -1;
        }

        public void SetSegmentNum(long segmentNum)
        {
            this.segmentNum = segmentNum;
        }

        public string GenGUID_String()
        {
            long iGuid = GenGUID_Int64();
            // 16 bytes string (2 + 13 + 1)
            // add random chars at prefix and suffix to make it feel randomly
            return RandomString(2, true, true) + IntIDToString(iGuid) + RandomString(1, true, true);
        }

        public string RandomNameCard()
        {
            // 2 uppercase alphabets + 6 digits
            int num = Random.Next(0, 1000000);
            return RandomString(2, true, true) + num.ToString("D6");
        }

        // check time rewind for caller
        public bool CheckTimeRewind()
        {
            return GetTimestamp() < timestamp;
        }

        public long GenGUID_Int64()
        {
            lock (lockObj)
            {
                long tmStamp = GetTimestamp();

                if (tmStamp == timestamp)
                {
                    if (((seq + 1) % (1L << (int)SEQUENCE_BITS)) == lastTimeStart)
                    {
                        // sequence full in current timestamp
                        tmStamp = WaitAndGetNewTime();
                        if (tmStamp == timestamp)
                        {
                            // WARNING: duplicated ID maybe generated. !!!!!
                        }
                    }
                }
                else if (tmStamp < timestamp)
                {
                    // timestamp rewind occurred, sleep a while and retry.
                    // make sure timestamp keep increasing
                    for (int i = 0; i < 8 && tmStamp < timestamp; i++)
                    {
                        Thread.Sleep(8);
                        tmStamp = GetTimestamp();
                    }
                    if (tmStamp < timestamp)
                    {
                        tmStamp = timestamp;
                        if (((seq + 1) % (1L << (int)SEQUENCE_BITS)) == lastTimeStart)
                        {
                            // WARNING: duplicated ID maybe generated. !!!!!
                        }
                    }
                }

                seq++;
                if (seq >= (1L << (int)SEQUENCE_BITS))
                {
                    // loop back
                    seq = 0;
                }
                if (tmStamp != timestamp)
                {
                    lastTimeStart = seq;
                    timestamp = tmStamp;
                }

                return (procID << (int)PROC_ID_SHIFT) | (tmStamp << (int)SEQUENCE_BITS) | seq;
            }
        }

        public static string IntIDToString(long inNum)
        {
            int idCharLen = g_idEncodeChars.Length;
            const int shiftCnt = 64 / 5 + 1;
            char[] buf = new char[shiftCnt];
            for (int i = 0; i < shiftCnt; i++)
            {
                int n = (int)(inNum & 0x1F);
                buf[shiftCnt - i - 1] = g_idEncodeChars[n % idCharLen];
                inNum >>= 5;
            }
            return new string(buf);
        }

        private long GetTimestamp()
        {
            long epochMS = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            return (epochMS >> TIMESTEP_BITS) & TIMESTAMP_MASK;
        }

        private long WaitAndGetNewTime()
        {
            long tm = timestamp;
            // max retry 2 time steps
            for (int i = 0; i < 4 && tm == timestamp; i++)
            {
                Thread.Sleep(8);
                tm = GetTimestamp();
            }
            return tm;
        }

        private static string RandomString(int length, bool upperCase, bool digits)
        {
            const string upperChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string digitChars = "0123456789";
            string chars = (upperCase ? upperChars : "") + (digits ? digitChars : "");
            StringBuilder result = new StringBuilder(length);
            for (int i = 0; i < length; i++)
            {
                result.Append(chars[Random.Next(chars.Length)]);
            }
            return result.ToString();
        }

        private static readonly Random Random = new Random();
    }
}
