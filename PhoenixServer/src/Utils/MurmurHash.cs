using System.Text;

namespace Phoenix.Server.Utils
{
    public static class MurmurHash
    {
        public static uint MurmurHash3_32(string str, uint seed = 1111111111)
        {
            var key = Encoding.UTF8.GetBytes(str);
            return (uint)MurmurHashExtensions.GetMurmur32BitsX86(key, seed);
        }

        public static uint MurmurHash3_32(byte[] key, uint seed = 1111111111)
        {
            return (uint)MurmurHashExtensions.GetMurmur32BitsX86(key, seed);
        }

        public static unsafe ulong MurmurHash2_64A(byte[] key, ulong seed = 1111111111)
        {
            return HashUtils.MurmurHash2x64A(key, (uint)seed);
        }

        public static ulong MurmurHash2_hash64(ulong x)
        {
            byte[] bytes = BitConverter.GetBytes(x);
            return MurmurHash2_64A(bytes, 10573);
        }

        public static long Hash64(ulong x)
        {
            x = (x ^ (x >> 30)) * 0xbf58476d1ce4e5b9;
            x = (x ^ (x >> 27)) * 0x94d049bb133111eb;
            return (long)((x ^ (x >> 31)) & 0x7FFFFFFF);
        }

        public static uint Hash(long val)
        {
            return (uint)(Hash64((ulong)val) & 0xFFFFFFFF);
        }

        public static uint Hash(string val)
        {
            return MurmurHash3_32(val);
        }

        private static uint Murmur32Scramble(uint k)
        {
            k *= 0xcc9e2d51;
            k = (k << 15) | (k >> 17);
            k *= 0x1b873593;
            return k;
        }
    }
}
