namespace Phoenix.Server.Utils
{
    public class SequenceInt64Generator
    {
        private Int64 SeqId;
        private Int64? exceptSeq;

        public SequenceInt64Generator(Int64? exceptSeq)
        {
            SeqId = 0;
            this.exceptSeq = exceptSeq;
        }

        public Int64 GetNextSequenceId()
        {
            Int64 newSeq;
            Int64 currentSeq;
            do
            {
                currentSeq = SeqId;
                newSeq = (currentSeq + 1) % int.MaxValue;

                if (exceptSeq != null && exceptSeq == newSeq)
                {
                    newSeq++;
                }

            } while (Interlocked.CompareExchange(ref SeqId, newSeq, currentSeq) != currentSeq);

            return newSeq;
        }
    }
}
