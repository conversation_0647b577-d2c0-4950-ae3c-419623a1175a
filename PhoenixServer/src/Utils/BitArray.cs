namespace Phoenix.ProjectSS.Utilites;

/// <summary>
///     The  `BitArray`  class represents a collection of bits and provides methods for manipulating and querying the bits.
/// </summary>
public class BitArray
{
    private static readonly int IntBitNums = 32;

    private readonly List<int> m_bits = new();

    /// <summary>
    ///     Initializes a new instance of the  `BitArray`  class with an empty list of bits.
    /// </summary>
    public BitArray()
    {
    }

    /// <summary>
    ///     Initializes a new instance of the  `BitArray`  class with the specified collection of bits.
    /// </summary>
    /// <param name="bits">The collection of bits to be added to the  `BitArray` .</param>
    public BitArray(IEnumerable<int> bits) => m_bits.AddRange(bits);

    /// <summary>
    ///     Sets the bits specified in the provided list.
    /// </summary>
    /// <param name="bits">The list of bits to be set.</param>
    public void SetBits(List<int> bits)
    {
        foreach (int bit in bits)
        {
            SetABit(bit);
        }
    }

    /// <summary>
    ///     Sets an individual bit at the specified index.
    /// </summary>
    /// <param name="bit">The index of the bit to be set.</param>
    public void SetABit(int bit)
    {
        int index, bitShift, bitMask, intDelta;
        index = bit / IntBitNums;
        intDelta = index - m_bits.Count + 1;

        if (intDelta > 0)
        {
            for (int i = 0; i < intDelta; ++i)
            {
                m_bits.Add(0);
            }
        }

        bitShift = bit % IntBitNums;
        bitMask = 1 << bitShift;
        m_bits[index] |= bitMask;
    }

    /// <summary>
    ///     Clears the bits specified in the provided list.
    /// </summary>
    /// <param name="bits">The list of bits to be cleared.</param>
    public void CleanBits(List<int> bits)
    {
        foreach (int bit in bits)
        {
            CleanABit(bit);
        }
    }

    /// <summary>
    ///     Clears an individual bit at the specified index.
    /// </summary>
    /// <param name="bit">The index of the bit to be cleared.</param>
    public void CleanABit(int bit)
    {
        int index, bitShift, bitMask;
        index = bit / IntBitNums;
        bitShift = bit % IntBitNums;
        bitMask = -1 - (1 << bitShift);
        m_bits[index] &= bitMask;
    }

    /// <summary>
    ///     Checks if the bit at the specified index is set.
    /// </summary>
    /// <param name="bit">The index of the bit to be checked.</param>
    /// <returns>True if the bit is set; otherwise, false.</returns>
    public bool IsBitSet(int bit)
    {
        int index = bit / IntBitNums;

        if (index >= m_bits.Count)
        {
            return false;
        }

        int bitShift = bit % IntBitNums;
        int bitMask = 1 << bitShift;

        return (m_bits[index] & bitMask) != 0;
    }

    /// <summary>
    ///     Retrieves an enumerable collection of all the set bits in the  `BitArray` .
    /// </summary>
    /// <returns>The enumerable collection of set bits.</returns>
    public IEnumerable<int> GetAllBitSets()
    {
        List<int> bitSets = new List<int>(AllBits());

        for (int bit = 0; bit < AllBits(); bit++)
        {
            if (IsBitSet(bit))
            {
                bitSets.Add(bit);
            }
        }

        return bitSets;
    }

    /// <summary>
    ///     Calculates the total number of bits in the  `BitArray` .
    /// </summary>
    /// <returns>The total number of bits in the  `BitArray` .</returns>
    private int AllBits() => m_bits.Count * IntBitNums;

    /// <summary>
    ///     Clears the  `BitArray` , removing all the bits.
    /// </summary>
    public void Clear() => m_bits.Clear();
}
