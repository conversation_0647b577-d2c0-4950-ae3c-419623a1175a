using System.Diagnostics;

namespace Phoenix.ProjectSS.TimingWheel;

public static class MonitorExt
{
    /// <summary>lock wait 4 timeout </summary>
    /// <param name="obj"></param>
    /// <param name="timeout"></param>
    /// <returns></returns>
    public static TimeSpan Wait(object obj, TimeSpan timeout)
    {
        Stopwatch stopwatch = Stopwatch.StartNew();
        Monitor.Wait(obj, timeout);
        stopwatch.Stop();
        return timeout - stopwatch.Elapsed;
    }
}
