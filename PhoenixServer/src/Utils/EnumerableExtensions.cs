// Copyright (c) Phoenix All Rights Reserved.

namespace Phoenix.Server.Utils;

public static class EnumerableExtensions
{
    public static string JoinWithSeparator<T>(this IEnumerable<T> source, char separator = '-')
    {
        if (source == null)
            throw new ArgumentNullException(nameof(source));

        return string.Join(separator, source);
    }

    public static string JoinTopElementsWithSeparator<T>(this IEnumerable<T> source, int count, char separator = '-')
    {
        if (source == null)
            throw new ArgumentNullException(nameof(source));

        return string.Join(separator, source.Take(count));
    }
}
