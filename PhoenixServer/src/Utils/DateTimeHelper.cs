namespace Phoenix.Server.Utils;

public static class DateTimeHelper
{
    // /// <summary>
    // /// 获取当前时间戳，默认毫秒级
    // /// </summary>
    // /// <param name="isSecond">是否秒级时间戳</param>
    // /// <returns></returns>
    // public static long GetTimestamp(bool isSecond = false)
    // {
    //     return GetTimestamp(DateTime.Now, isSecond);
    // }
    //
    // /// <summary>
    // /// 获取指定时间戳，默认毫秒级
    // /// </summary>
    // /// <param name="date">时间</param>
    // /// <param name="isSecond">是否秒级时间戳</param>
    // /// <returns></returns>
    // public static long GetTimestamp(DateTime date, bool isSecond = false)
    // {
    //     var dateTimeOffset = new DateTimeOffset(date);
    //     return isSecond
    //         ? dateTimeOffset.ToUnixTimeSeconds()
    //         : dateTimeOffset.ToUnixTimeMilliseconds();
    // }
    //
    // /// <summary>
    // /// 获取当前时间
    // /// </summary>
    // /// <param name="timestamp">时间戳，默认毫秒级</param>
    // /// <param name="isSecond">是否秒级时间戳，默认毫秒级</param>
    // /// <returns></returns>
    // public static DateTime FromTimestamp(long timestamp, bool isSecond = false)
    // {
    //     return isSecond
    //         ? DateTimeOffset.FromUnixTimeSeconds(timestamp).LocalDateTime
    //         : DateTimeOffset.FromUnixTimeMilliseconds(timestamp).LocalDateTime;
    // }

    private const int SECONDS_PER_MINUTE = 60;
    private const int SECONDS_PER_HOUR = 60 * SECONDS_PER_MINUTE;
    private const int SECONDS_PER_DAY = 24 * SECONDS_PER_HOUR;
    private const int SECONDS_PER_WEEK = 7 * SECONDS_PER_DAY;
    private static readonly DateTime UnixEpoch = new(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);

    public static long GetTimestampMilliseconds(DateTime? date = null) => date == null
        ? new DateTimeOffset(DateTime.UtcNow).ToUnixTimeMilliseconds()
        : new DateTimeOffset(date.Value).ToUnixTimeMilliseconds();

    public static long GetTimestampSeconds(DateTime? date = null) => date == null
        ? new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds()
        : new DateTimeOffset(date.Value).ToUnixTimeSeconds();

    public static DateTime GetUtcDateTimeFromTimestampMilliseconds(long timestampMilliseconds) =>
        DateTimeOffset.FromUnixTimeMilliseconds(timestampMilliseconds).UtcDateTime;

    public static DateTime GetUtcDateTimeFromTimestampSeconds(long timestampSeconds) =>
        DateTimeOffset.FromUnixTimeSeconds(timestampSeconds).UtcDateTime;

    public static int MomentsBetweenDateTime(DateTime inclusive, DateTime exclusive, int hour, int minute, int second)
    {
        if (inclusive >= exclusive)
        {
            throw new ArgumentException("inclusive must less than exclusive");
        }

        int compensation = (hour - exclusive.Hour) * SECONDS_PER_HOUR +
                           (minute - exclusive.Minute) * SECONDS_PER_MINUTE + (second - exclusive.Second);
        compensation = (compensation + SECONDS_PER_DAY) % SECONDS_PER_DAY;
        return (int)(exclusive.AddSeconds(compensation) - inclusive).TotalSeconds / SECONDS_PER_DAY;
    }

    public static int DaysBetweenDateTime(DateTime inclusive, DateTime exclusive) =>
        MomentsBetweenDateTime(inclusive, exclusive, 0, 0, 0);

    public static int WeekdayMomentsBetweenDateTime(DateTime inclusive, DateTime exclusive, DayOfWeek day, int hour,
        int minute, int second)
    {
        if (inclusive >= exclusive)
        {
            throw new ArgumentException("inclusive must less than exclusive");
        }

        int compensation = (day - exclusive.DayOfWeek) * SECONDS_PER_DAY + (hour - exclusive.Hour) * SECONDS_PER_HOUR +
                           (minute - exclusive.Minute) * SECONDS_PER_MINUTE + (second - exclusive.Second);
        compensation = (compensation + SECONDS_PER_WEEK) % SECONDS_PER_WEEK;
        return (int)(exclusive.AddSeconds(compensation) - inclusive).TotalSeconds / SECONDS_PER_WEEK;
    }

    public static int WeekdaysBetweenDateTime(DateTime inclusive, DateTime exclusive, DayOfWeek day) =>
        WeekdayMomentsBetweenDateTime(inclusive, exclusive, day, 0, 0, 0);

    /// <summary>
    ///     get current unix timestamp
    ///     now should be utc time
    /// </summary>
    /// <returns></returns>
    public static long GetUnixTimestamp(DateTime now) => GetTimestampSeconds(now);

    public static DateTime GetInitialSecondTime(this DateTime now) =>
        new(now.Year, now.Month, now.Day, now.Hour, now.Minute, now.Second, now.Kind);

    public static DateTime GetStartOfDay(this DateTime now) => new(now.Year, now.Month, now.Day, 0, 0, 0, now.Kind);
}
