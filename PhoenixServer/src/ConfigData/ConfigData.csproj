<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <LangVersion>9</LangVersion>
  </PropertyGroup>

  <PropertyGroup>
    <NoWarn>CS8600</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="..\ConfigDataLink\**\*.cs" />
    <Compile Update="..\ConfigDataLink\Scripts\Manager\Extension\PhoenixHakoniwaEntityConfigData.cs">
      <Link>Scripts\Manager\Extension\PhoenixHakoniwaEntityConfigData.cs</Link>
    </Compile>
    <Compile Update="..\ConfigDataLink\Scripts\Manager\Extension\PhoenixWorldPointStoryConfigData.cs">
      <Link>Scripts\Manager\Extension\PhoenixWorldPointStoryConfigData.cs</Link>
    </Compile>
    <Compile Update="..\ConfigDataLink\Scripts\Manager\Extension\PhoenixHakoniwaQuestGraphConfigData.cs">
      <Link>Scripts\Manager\Extension\PhoenixHakoniwaQuestGraphConfigData.cs</Link>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Google.Protobuf" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="MessagePack" />
    <PackageReference Include="MessagePack.Annotations" />
  </ItemGroup>
</Project>
