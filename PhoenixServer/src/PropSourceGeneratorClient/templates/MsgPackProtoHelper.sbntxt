{{~
    m_listProtocolDesc = x.m_listProtocolDesc
~}}
// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
using System.IO;

namespace Phoenix.MsgPackLogic.Protocol
{
    public static class MsgPackProtoHelper
    {
        static MsgPackProtoHelper()
        {
            # if PHOENIX_SERVER || PHOENIX_MINICLIENT

            var resolver = MessagePack.Resolvers.CompositeResolver.Create(
                MessagePack.Resolvers.NativeDateTimeResolver.Instance,
                MessagePack.Resolvers.StandardResolver.Instance
            );
            MessagePackSerializer.DefaultOptions = MessagePackSerializerOptions.Standard.WithResolver(resolver);
            #endif

           {{~ for desc in m_listProtocolDesc ~}}
           Add({{ desc.ProtoCode }}, typeof({{ desc.ProtoName }}), {{ desc.IsClient }}, {{desc.IsFromClient}});
           {{~ end ~}}
        }

        private static void Add(int protoId, Type type, bool isClient, bool isFromClient)
        {
           s_type2ProtoId.Add(type, protoId);
           s_protoId2Type.Add(protoId, type);
           if (isClient)
           {
                s_clientProtoId2Type.Add(protoId, type);
           }
           if (isFromClient)
           {
                s_protoIdsFromClient.Add(protoId);
           }
        }

        public static MsgPackStructBase? Deserialize(int protoId, ReadOnlyMemory<byte> memory)
        {
            switch (protoId)
            {
            {{~ for desc in m_listProtocolDesc ~}}
                case {{ desc.ProtoCode }}:
                {
                    {{~ if desc.IsShared ~}}
                    return {{ desc.ProtoName }}.Shared;
                    {{~ else ~}}
                    return MessagePackSerializer.Deserialize<{{ desc.ProtoName }}>(memory);
                    {{~ end ~}}
                }
            {{~ end ~}}
                default:
                {
                    return null;
                }
            }
        }

        public static MsgPackStructBase? Deserialize(int protoId, byte[] data)
        {
            switch (protoId)
            {
        {{~ for desc in m_listProtocolDesc ~}}
                case {{ desc.ProtoCode }}:
                {
                    return MessagePackSerializer.Deserialize<{{ desc.ProtoName }}>(data);
                }
        {{~ end ~}}
                default:
                {
                    return null;
                }
            }
        }

        public static void Serialize(Stream stream, MsgPackStructBase data)
        {
            switch ((int)data.ProtoCode)
            {
           {{~ for desc in m_listProtocolDesc ~}}
                case {{ desc.ProtoCode }}:
                {
                    MessagePackSerializer.Serialize(stream, data as {{ desc.ProtoName }});
                }
                break;
           {{~ end ~}}
                default:
                {
                    return;
                }
            }
        }

        public static byte[]? Serialize(MsgPackStructBase data)
        {
            switch ((int)data.ProtoCode)
            {
        {{~ for desc in m_listProtocolDesc ~}}
                case {{ desc.ProtoCode }}:
                {
                    return MessagePackSerializer.Serialize(data as {{ desc.ProtoName }});
                }
        {{~ end ~}}
                default:
                {
                    return null;
                }
            }
        }

		public static Type? GetTypeByProtoId(int protoId)
		{
			return s_protoId2Type.TryGetValue(protoId, out Type? value) ? value : null;
		}

		public static int GetProtoIdByType(Type type)
		{
			return s_type2ProtoId.TryGetValue(type, out int value) ? value : 0;
		}

        public static bool IsClientProtocol(int protoId)
        {
            return s_clientProtoId2Type.ContainsKey(protoId);
        }

        public static IEnumerable<KeyValuePair<int, Type>> GetClientProtoId2Types()
        {
            return s_clientProtoId2Type;
        }

        public static IEnumerable<KeyValuePair<int, Type>> GetProtoId2Types()
        {
            return s_protoId2Type;
        }

        public static bool IsProtocolFromClient(int protoId)
        {
            return s_protoIdsFromClient.Contains(protoId);
        }

        private static readonly Dictionary<Type, int> s_type2ProtoId = new();
        private static readonly Dictionary<int, Type> s_protoId2Type = new();
        private static readonly Dictionary<int, Type> s_clientProtoId2Type = new();
        private static readonly HashSet<int> s_protoIdsFromClient = new();
     }
}
