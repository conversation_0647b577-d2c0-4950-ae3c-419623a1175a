using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Text;
using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using Scriban;
using Utils.SourceGenerated.Build;
using static PropGenerator;

public class ProtocolDesc
{
    public int ProtoCode { get; set; }
    public string ProtoName { get; set; }
    public bool IsClient { get; set; }
    public bool IsNtf { get; set; }

    public bool IsFromClient
    {
        get;
        set;
    }

    public bool IsShared
    {
        get;
        set;
    }
}

public enum ProtocolSource
{
    /// <summary>
    /// 客户端发起的协议
    /// </summary>
    Client = 0,

    /// <summary>
    /// 服务器发起的协议
    /// </summary>
    Server = 1,

    /// <summary>
    /// 双向协议（客户端和服务器都可以发起）
    /// </summary>
    Bidirectional = 2,

    /// <summary>
    /// 内部协议（仅在服务器内部使用）
    /// </summary>
    Internal = 3
}

[Generator]
#pragma warning disable RS1036 // 指定分析器禁止的 API 强制设置
public class PropGenerator : ISourceGenerator
#pragma warning restore RS1036 // 指定分析器禁止的 API 强制设置
{
    // 指定要查找的自定义属性的完全限定名
    public static readonly string dbDataClassAttributeFullName = "Phoenix.AutoScriptDesc.DataClassAttribute";
    public static readonly string dbDataPropertyAttributeFullName = "Phoenix.AutoScriptDesc.DataPropertyAttribute";

    public static readonly string msgPackModuleAttributeFullName = "Phoenix.AutoScriptDesc.MsgPackModuleAttribute";
    public static readonly string msgPackClassAttributeFullName = "Phoenix.AutoScriptDesc.MsgPackClassAttribute";
    public static readonly string msgPackPropertyAttributeFullName = "Phoenix.AutoScriptDesc.MsgPackPropertyAttribute";


    public static readonly string serializeClassDBPlayerData = "Phoenix.DBData.DBPlayerData";

    public class MsgPackModuleInfo
    {
        public String strModuleNameDesc;
        public String strModulename;
        public int iProtoStart;
        public int iProtoEnd;
        public int iProtoIncrease;
        public Dictionary<string, int> dictProtoCode;
        public bool isClientProtocol;//是否是客户端
    }

    public void Initialize(GeneratorInitializationContext context)
    {
    }


    public void Execute(GeneratorExecutionContext context)
    {
        //System.Diagnostics.Debugger.Launch();
        // 获取当前编译单元
        Compilation compilation = context.Compilation;

        // 创建一个用于存储找到的类型的集合DB
        var typesWithAttributeDB = ImmutableArray.CreateBuilder<INamedTypeSymbol>();

        m_dictMsgPackModuleByName.Clear();
        m_dictMsgPackModuleByProtoCode.Clear();
        m_dictModuleName2ProtoIncrease.Clear();
        m_dictProtoCode.Clear();
        // m_dictClassName2ProtoCode.Clear();
        m_listProtocolDesc.Clear();
        m_dictModuleTypeSymbols.Clear();

        string protoNotesFile = context.Compilation.Options.SourceReferenceResolver.NormalizePath("ProtoInfo.txt", "");
        ReadProtoNotes(protoNotesFile);

        // 遍历所有引用的 MetadataReference
        foreach (var reference in compilation.References)
        {
            // 获取引用的程序集符号
            var assemblySymbol = compilation.GetAssemblyOrModuleSymbol(reference) as IAssemblySymbol;
            if (assemblySymbol != null)
            {
                // 遍历程序集中的所有类型
                foreach (var type in GetAllTypes(assemblySymbol.GlobalNamespace))
                {
                    // 查找具有指定自定义属性的类型
                    if (type.TypeKind == TypeKind.Class)
                    {
                        // if (HasCustomAttribute(type, dbDataClassAttributeFullName))
                        // {
                        //     // 将找到的类型加入集合DB
                        //     typesWithAttributeDB.Add(type);
                        // }
                        // else

                        if (HasCustomAttribute(type, msgPackModuleAttributeFullName))
                        {
                            if (string.IsNullOrEmpty(m_strModuleDescName)
                                || !m_strModuleDescName.Equals(type.MetadataName))
                            {
                                // 获取模块名称
                                m_strModuleDescName = type.MetadataName;
                                var classAttri = type.GetAttributes().First(e => e.AttributeClass.ToDisplayString() == msgPackModuleAttributeFullName);

                                Int32 category = GetAttributeArgValue<Int32>(classAttri, "CategoryType");
                                Int32 iProtoStart = GetAttributeArgValue<Int32>(classAttri, "ProtoStart");
                                Int32 iProtoEnd = GetAttributeArgValue<Int32>(classAttri, "ProtoEnd");

                                if(category != 1 && category != 2)
                                {
                                    // 只处理CategoryType为1或2的模块
                                    continue;
                                }
                                bool isClientProtocol = category == 1;
                                // foreach (var pair in classAttri.NamedArguments)
                                // {
                                //     if (pair.Key == "ProtoStart")
                                //     {
                                //         iProtoStart = (Int32)pair.Value.Value;
                                //     }
                                //
                                //     if (pair.Key == "ProtoEnd")
                                //     {
                                //         iProtoEnd = (Int32)pair.Value.Value;
                                //     }
                                // }



                                String strModuleName = String.Empty;

                                String[] arrModuleName = m_strModuleDescName.Split('_');
                                if (arrModuleName.Length > 1)
                                {
                                    strModuleName = arrModuleName[1];
                                }

                                MsgPackModuleInfo msgPackModuleInfo;
                                if (m_dictMsgPackModuleByName.TryGetValue(m_strModuleDescName, out msgPackModuleInfo))
                                {
                                    msgPackModuleInfo.strModulename = strModuleName;
                                    msgPackModuleInfo.iProtoStart = iProtoStart;
                                    msgPackModuleInfo.iProtoEnd = iProtoEnd;
                                    if (msgPackModuleInfo.iProtoIncrease < iProtoStart || msgPackModuleInfo.iProtoIncrease > iProtoEnd)
                                    {
                                        msgPackModuleInfo.iProtoIncrease = iProtoStart + 1;
                                        msgPackModuleInfo.dictProtoCode.Clear();
                                    }
                                    else
                                    {
                                        msgPackModuleInfo.iProtoIncrease = msgPackModuleInfo.iProtoIncrease + 1;
                                    }
                                    msgPackModuleInfo.isClientProtocol = isClientProtocol;
                                }
                                else
                                {
                                    msgPackModuleInfo = new MsgPackModuleInfo();
                                    msgPackModuleInfo.strModuleNameDesc = m_strModuleDescName;
                                    msgPackModuleInfo.strModulename = strModuleName;
                                    msgPackModuleInfo.iProtoStart = iProtoStart;
                                    msgPackModuleInfo.iProtoEnd = iProtoEnd;
                                    msgPackModuleInfo.dictProtoCode = new Dictionary<string, int>();
                                    msgPackModuleInfo.iProtoIncrease = iProtoStart + 1;
                                    msgPackModuleInfo.isClientProtocol = isClientProtocol;

                                    m_dictMsgPackModuleByName.Add(m_strModuleDescName, msgPackModuleInfo);
                                }

                            }
                        }

                        if (HasCustomAttribute(type, msgPackClassAttributeFullName))
                        {
                            // 将找到的类型加入集合MsgPack
                            AddINamedTypeSymbolByFullName(type);
                        }
                    }
                }
            }
        }

        // // 处理找到的类型，生成代码或做其他操作
        // foreach (var typeSymbol in typesWithAttributeDB)
        // {
        //     GenerateDBDataClassForType(context, typeSymbol);
        // }

        // 处理找到的类型，生成代码或做其他操作
        foreach (var moduleSimbols in m_dictModuleTypeSymbols)
        {
            GenerateMsgPackClassForType(context, moduleSimbols);
        }
        GenerateMsgPackProtoDeclare(context);

        GenerateProtoHelper(context);

        //GenerateBasePlayerComponent(context);

        WriteProtoNotes(protoNotesFile);
    }


    /// <summary>
    /// 获取类型符号对应的源代码文件路径
    /// </summary>
    /// <param name="typeSymbol">类型符号</param>
    /// <returns>源代码文件路径，如果找不到则返回null</returns>
    private string GetSourceFilePath(INamedTypeSymbol typeSymbol)
    {
        // 获取类型的声明语法引用
        var syntaxRef = typeSymbol.DeclaringSyntaxReferences.FirstOrDefault();
        if (syntaxRef == null)
        {
            return null;
        }

        // 获取语法树
        SyntaxTree syntaxTree = syntaxRef.SyntaxTree;

        // 获取文件路径
        string filePath = syntaxTree.FilePath;

        return filePath;
    }

    public T GetAttributeArgValue<T>(AttributeData attr, string argName, T defaultValue = default)
    {
        foreach (var pair in attr.NamedArguments)
        {
            if (pair.Key == argName)
            {
                if (pair.Value.Value is T value)
                {
                    return value;
                }
                else
                {
                    throw new InvalidOperationException($"Attribute argument '{argName}' is not of type {typeof(T).Name}.");
                }
            }
        }

        return defaultValue;
    }

    private void ReadProtoNotes(string protoNotesFile)
    {
        String line;
        try
        {

            using (StreamReader sr = new StreamReader(protoNotesFile))
            {
                Int32 iProtoCodeTemp = 0;
                //MsgPackModuleInfo msgPackModuleInfo;
                line = sr.ReadLine();
                while (line != null)
                {
                    String[] arrStrModuleProtoIncrease = line.Split('=');
                    if (arrStrModuleProtoIncrease.Length > 2)
                    {
                        String strModuleName = arrStrModuleProtoIncrease[0];
                        String strProtoCode = arrStrModuleProtoIncrease[1];
                        Int32 iProtoCode = Int32.Parse(arrStrModuleProtoIncrease[2]);


                        if (!m_dictMsgPackModuleByName.TryGetValue(strModuleName, out MsgPackModuleInfo msgPackModuleInfo))
                        {
                            iProtoCodeTemp = 0;
                            msgPackModuleInfo = new MsgPackModuleInfo();
                            msgPackModuleInfo.strModuleNameDesc = strModuleName;
                            if (msgPackModuleInfo.dictProtoCode == null)
                            {
                                msgPackModuleInfo.dictProtoCode = new Dictionary<string, int>();
                            }



                            m_dictMsgPackModuleByName.Add(strModuleName, msgPackModuleInfo);
                        }

                        msgPackModuleInfo.dictProtoCode[strProtoCode] = iProtoCode;
                        if (iProtoCode > iProtoCodeTemp)
                        {
                            iProtoCodeTemp = iProtoCode;
                            msgPackModuleInfo.iProtoIncrease = iProtoCodeTemp;
                        }
                    }
                    line = sr.ReadLine();
                }


            }
        }
        catch (Exception e)
        {
            Console.WriteLine("Exception: " + e.Message);
        }
        finally
        {
            Console.WriteLine("Executing finally block.");
        }
    }

    private void WriteProtoNotes(string protoNotesFile)
    {
        try
        {
            using (StreamWriter sw = new StreamWriter(protoNotesFile, false, Encoding.UTF8))
            {
                StringBuilder strBuilder = new StringBuilder();
                foreach (var pairInfo in m_dictMsgPackModuleByName)
                {
                    MsgPackModuleInfo moduleInfo = pairInfo.Value;
                    foreach (var pairProto in moduleInfo.dictProtoCode)
                    {
                        strBuilder.AppendLine($"{pairInfo.Key}={pairProto.Key}={pairProto.Value}");
                    }

                }
                foreach (var pair in m_dictModuleName2ProtoIncrease)
                {
                    strBuilder.AppendLine($"{pair.Key}={pair.Value}");
                }
                sw.Write(strBuilder.ToString());
            }
        }
        catch (Exception e)
        {
            Console.WriteLine("Exception: " + e.Message);
        }
        finally
        {
            Console.WriteLine("Executing finally block.");
        }
    }

    private void GenerateProtoHelper(GeneratorExecutionContext context)
    {
        m_listProtocolDesc.Sort((desc1, desc2) =>
        {
            if (desc1.ProtoCode < desc2.ProtoCode)
            {
                return -1;
            }
            else if (desc1.ProtoCode > desc2.ProtoCode)
            {
                return 1;
            }
            return 0;
        });

        // 直接从嵌入资源中读取模板
        string templateContent = GetEmbeddedTemplate("MsgPackProtoHelper.sbntxt");
        var template = Template.Parse(templateContent);
        var source = template.RenderCode(new
        {
            // namespacename = gamePlayerDataSymbol.ContainingNamespace.ToDisplayString(),
            m_listProtocolDesc
        });
        // 向编译器添加生成的源代码
        context.AddSource("MsgPackProtoHelper.cs", SourceText.From(source, Encoding.UTF8));
    }

    private string GetEmbeddedTemplate(string templateName)
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            string resourceName = $"PropSourceGeneratorClient.templates.{templateName}";

            // 列出所有嵌入资源，用于调试
            var resourceNames = assembly.GetManifestResourceNames();

            using (Stream? stream = assembly.GetManifestResourceStream(resourceName))
            {
                if (stream == null)
                    return string.Empty;

                using (StreamReader reader = new StreamReader(stream))
                {
                    return reader.ReadToEnd();
                }
            }
        }
        catch
        {
            return string.Empty;
        }
    }

    private void GenerateBasePlayerComponent(GeneratorExecutionContext context)
    {
        string code = $@"using Phoenix.PropSystem;

namespace Phoenix.GameLogic
{{
    public abstract class BasePlayerComponent : Component
    {{
        protected BasePlayerComponent(GameBasePlayer player) : base(player)
        {{
            Player = player;
        }}

        public GameBasePlayer Player {{ get; protected set; }}
    }}
}}
";
        context.AddSource("BasePlayerComponent.g.cs", SourceText.From(code, Encoding.UTF8));
    }

    private IEnumerable<INamedTypeSymbol> GetAllTypes(INamespaceSymbol namespaceSymbol)
    {
        foreach (var type in namespaceSymbol.GetTypeMembers())
        {
            yield return type;
        }

        foreach (var nestedNamespace in namespaceSymbol.GetNamespaceMembers())
        {
            foreach (var type in GetAllTypes(nestedNamespace))
            {
                yield return type;
            }
        }
    }

    private bool HasCustomAttribute(INamedTypeSymbol typeSymbol, string classAttributeFullName)
    {
        foreach (var attribute in typeSymbol.GetAttributes())
        {

            if (attribute.AttributeClass.ToDisplayString() == classAttributeFullName)
            {
                return true;
            }
        }

        return false;
    }

    private void GenerateDBDataClassForType(GeneratorExecutionContext context, INamedTypeSymbol typeSymbol)
    {
        var classAttri = typeSymbol.GetAttributes().First(e => e.AttributeClass.ToDisplayString() == dbDataClassAttributeFullName);
        string classFullName = "";
        string namespaceName = "";
        string className = "";
        int i = 0;
        string parentClassName = "";
        bool isSubclassOfPropObject = false;
        bool isSubclassOfGameObject = false;
        bool isSubclassOfComponent = false;
        bool isSubclassOfServerEntity = false;
        bool isSerializeObject = false;
        string strPartial = string.Empty;
        bool isExport = true;
        foreach (var pair in classAttri.NamedArguments)
        {
            if (pair.Key == "ClassType")
            {
                classFullName = pair.Value.Value.ToString();
                i = classFullName.LastIndexOf('.');
                namespaceName = classFullName.Substring(0, i);
                namespaceName = namespaceName.Replace(".Engine", ".DBData");
                className = classFullName.Substring(i + 1);
            }
            if (pair.Key == "ParentClassType")
            {
                parentClassName = pair.Value.Value.ToString();
                if (parentClassName == "Phoenix.PropSystem.GameObject")
                {
                    isSubclassOfGameObject = true;
                }
                else if (parentClassName == "Phoenix.PropSystem.PropObject")
                {
                    isSubclassOfPropObject = true;
                }
                else if (parentClassName == "Phoenix.PropSystem.Component" || parentClassName == "Phoenix.GameLogic.BasePlayerComponent")
                {
                    isSubclassOfComponent = true;
                }
                else if (parentClassName == "Phoenix.PropSystem.ServerEntity")
                {
                    isSubclassOfServerEntity = true;
                }
            }

            if (pair.Key == "SerializeFlag")
            {
                int iSerializeFlag = (int)pair.Value.Value;
                if (iSerializeFlag == 2)
                {
                    isSerializeObject = true;
                }
            }

            if (pair.Key == "IsPartial")
            {
                Boolean isPartial = (Boolean)pair.Value.Value;
                if (isPartial)
                {
                    strPartial = "partial ";
                }
            }

            if (pair.Key == "ExportType") // ExportTargetType All = 0, Server = 1, Client = 2
            {
                int iExportType = (int)pair.Value.Value;
                if (iExportType == 1)
                {
                    isExport = false;
                    break;
                }
            }

        }

        if (!isExport)
        {
            return;
        }

        List<KeyValuePair<string, PhoenixFieldInfo>> fieldName2FieldInfo = new List<KeyValuePair<string, PhoenixFieldInfo>>();

        List<IFieldSymbol> listFieldSymbol = typeSymbol.GetMembers().OfType<IFieldSymbol>().ToList<IFieldSymbol>();
        for (int j = 0; j < listFieldSymbol.Count; j++)
        {
            IFieldSymbol property = listFieldSymbol[j];
            var propertyAttri = property.GetAttributes().First(e => e.AttributeClass.ToDisplayString() == dbDataPropertyAttributeFullName);
            if (propertyAttri == null)
            {
                continue;
            }

            property.ToDisplayString();
            var attri = property.GetAttributes().First(e => e.AttributeClass.ToDisplayString() == dbDataPropertyAttributeFullName);
            PhoenixFieldInfo fieldInfo = null;
            if (attri != null)
            {
                bool isNeedSerialize = true;
                var args = attri.NamedArguments;
                foreach (var arg in args)
                {
                    if (arg.Key == "SerializeFlag")
                    {
                        int serializeFlag = (int)arg.Value.Value;
                        if (serializeFlag <= 1 || serializeFlag == 4)
                        {
                            isNeedSerialize = false;
                        }
                        break;
                    }
                }
                if (!isNeedSerialize)
                {
                    continue;
                }

                fieldInfo = new PhoenixFieldInfo();
                fieldInfo.FieldSymbol = property;
                fieldInfo.Name = property.MetadataName;

                foreach (var arg in args)
                {
                    if (arg.Key == "KeyWords")
                    {
                        fieldInfo.KeyWords = arg.Value.Value.ToString();
                    }

                    if (arg.Key == "PropertyType")
                    {
                        fieldInfo.TypeName = arg.Value.Value.ToString();
                        fieldInfo.TypeName = fieldInfo.TypeName.Replace(".Engine", ".DBData");
                    }

                    if (arg.Key == "Persist")
                    {
                        fieldInfo.Persist = (bool)arg.Value.Value;
                    }

                    if (arg.Key == "Sync")
                    {
                        fieldInfo.Sync = (int)arg.Value.Value;
                    }

                    if (arg.Key == "DefaultValue")
                    {
                        fieldInfo.defaultValue = arg.Value.Value;
                    }

                    if (arg.Key == "SerializeFlag")
                    {
                        fieldInfo.SerializeFlag = (int)arg.Value.Value;
                    }
                    if (arg.Key == "NewObject")
                    {
                        fieldInfo.NewObject = (bool)arg.Value.Value;
                    }
                }

                fieldName2FieldInfo.Add(new KeyValuePair<string, PhoenixFieldInfo>(property.MetadataName, fieldInfo));
            }
        }

        // 生成代码示例
        string extendClass = "";
        string parentClass = "";
        if (isSubclassOfPropObject)
        {
            parentClass = "PropObject";
        }
        else if (isSubclassOfGameObject)
        {
            parentClass = "GameObject";
        }
        else if (isSubclassOfComponent)
        {
            parentClass = $"{parentClassName.Substring(parentClassName.LastIndexOf(".") + 1)}";
        }
        else if (isSubclassOfServerEntity)
        {
            parentClass = "ServerEntity";
        }

        extendClass = string.IsNullOrEmpty(parentClass) ? "" : $": {parentClass}";
        string propertyPerfix = isSubclassOfPropObject ? "" : "PropObject.";
        string code = $@"using MessagePack;
using System;
using System.Collections.Generic;

namespace {namespaceName}
{{";
        if (isSerializeObject)
        {
            code += $@"
    [MessagePackObject]";
        }

        code += $@"
    public {strPartial}class {className} {extendClass}
    {{";
        string codebody = "";
        int index = 0;
        List<KeyValuePair<string, PhoenixFieldInfo>> listPhoenixFieldInfo = fieldName2FieldInfo.ToList<KeyValuePair<string, PhoenixFieldInfo>>();
        for (int j = 0; j < listPhoenixFieldInfo.Count; j++)
        {
            var pair = listPhoenixFieldInfo[j];
            var fieldInfo = pair.Value;
            string strDefaultValue = string.Empty;

            if (fieldInfo.SerializeFlag == 1)
            {
                codebody += $@"
        [IgnoreMember]";
            }
            else if (fieldInfo.SerializeFlag == 3)
            {
                codebody += $@"
        [Key({index})]";
                index++;
            }

            if (fieldInfo.NewObject)
            {
                string strPropertyType = fieldInfo.TypeName;
                if (strPropertyType.Contains("<") && strPropertyType.Contains(">"))
                {
                    int idxTemp = 0;
                    if (strPropertyType.Contains(","))
                    {
                        idxTemp = strPropertyType.LastIndexOf(',');
                        strPropertyType = strPropertyType.Substring(idxTemp + 1);
                        strPropertyType = strPropertyType.Trim();
                        strPropertyType = strPropertyType.Replace(">", "");
                    }
                    else
                    {
                        strPropertyType = strPropertyType.Trim();
                        idxTemp = strPropertyType.LastIndexOf('<');
                        strPropertyType = strPropertyType.Substring(idxTemp + 1);
                        strPropertyType = strPropertyType.Replace(">", "");
                    }
                }
            }

            if (!string.IsNullOrEmpty(fieldInfo.KeyWords))
            {
                fieldInfo.KeyWords += " ";
            }

            codebody += $@"
        public {fieldInfo.KeyWords}{fieldInfo.TypeName} {pair.Key}";

            if (fieldInfo.NewObject)
            {
                codebody += $@" = new {fieldInfo.TypeName}()";
            }

            if (fieldInfo.defaultValue != null)
            {
                string strTypeName = fieldInfo.TypeName.ToLower();
                //if (strTypeName.Contains("int") || strTypeName.Contains("float") || strTypeName.Contains("double") || strTypeName.Contains("long") || strTypeName.Contains("short"))
                //{
                //}

                if (strTypeName.Contains("string"))
                {
                    fieldInfo.defaultValue = $"\"{fieldInfo.defaultValue}\"";
                }

                if (strTypeName.Contains("bool"))
                {
                    fieldInfo.defaultValue = fieldInfo.defaultValue.ToString().ToLower();
                }

                if (fieldInfo.KeyWords.Contains("readonly"))
                {
                    codebody += $@" = {fieldInfo.defaultValue};";
                }
                else
                {
                    string strDefaultExt = string.Empty;
                    if (strTypeName.Contains("float") || strTypeName.Contains("single"))
                    {
                        strDefaultExt = "f";
                    }
                    else if (strTypeName.Contains("double"))
                    {
                        strDefaultExt = "d";
                    }
                    codebody += $@" {{ get; set; }} = {fieldInfo.defaultValue}{strDefaultExt};";
                }
            }
            else
            {
                codebody += ";";
            }
            codebody += $@"
";

            //index++;
        }

        string code2 = $@"
    }}
}}";
        string codeTotal = code + codebody + code2;
        // 向编译器添加生成的源代码
        context.AddSource($"{className}.cs", SourceText.From(codeTotal, Encoding.UTF8));
    }

    // =========================================MsgPack Class Generator Start=============================================
    private string m_strModuleDescName = string.Empty;
    private Dictionary<string, MsgPackModuleInfo> m_dictMsgPackModuleByName = new Dictionary<string, MsgPackModuleInfo>();
    private Dictionary<int, MsgPackModuleInfo> m_dictMsgPackModuleByProtoCode = new Dictionary<int, MsgPackModuleInfo>();
    private Dictionary<string, int> m_dictModuleName2ProtoIncrease = new Dictionary<string, int>();
    private Dictionary<int, string> m_dictProtoCode = new Dictionary<int, string>();
    // private Dictionary<string, int> m_dictClassName2ProtoCode = new Dictionary<string, int>();
    private List<ProtocolDesc> m_listProtocolDesc = new List<ProtocolDesc>(); //所有的协议描述集合
    private Dictionary<string, List<INamedTypeSymbol>> m_dictModuleTypeSymbols = new Dictionary<string, List<INamedTypeSymbol>>();

    private void AddINamedTypeSymbolByFullName(INamedTypeSymbol typeSymbol)
    {
        var classAttri = typeSymbol.GetAttributes().First(e => e.AttributeClass.ToDisplayString() == msgPackClassAttributeFullName);
        List<INamedTypeSymbol> typesWithAttributeMsgPack;
        if (!m_dictModuleTypeSymbols.TryGetValue(m_strModuleDescName, out typesWithAttributeMsgPack))
        {
            typesWithAttributeMsgPack = new List<INamedTypeSymbol>();
        }
        m_dictModuleTypeSymbols[m_strModuleDescName] = typesWithAttributeMsgPack;

        typesWithAttributeMsgPack.Add(typeSymbol);
    }

    private void GenerateMsgPackClassForType(GeneratorExecutionContext context, KeyValuePair<string, List<INamedTypeSymbol>> keyValuePairs)
    {
        String strModuleDescName = keyValuePairs.Key;

        MsgPackModuleInfo moduleInfo;
        if (!m_dictMsgPackModuleByName.TryGetValue(strModuleDescName, out moduleInfo))
        {
            System.Diagnostics.Debug.Assert(false, $"MsgPackModuleInfo not found ClassName = {strModuleDescName}");
        }
        if (moduleInfo.iProtoIncrease == 0)
        {
            System.Diagnostics.Debug.Assert(false, $"ProtoIncrease not found ClassName = {strModuleDescName}, iProtoIncrease = {moduleInfo.iProtoIncrease}");
        }

        String strClassFileName = strModuleDescName.Replace("Desc", string.Empty);

        var getFileName = () =>
        {
            if (moduleInfo.isClientProtocol)
            {
                return $"Protocol{moduleInfo.strModulename}.cs";
            }
            return $"RpcProtocol{moduleInfo.strModulename}.cs";
        };



        string codeModule = $@"// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{{";
        List<INamedTypeSymbol> listNamedTypeSymbol = keyValuePairs.Value;
        foreach (var namedTypeSymbol in listNamedTypeSymbol)
        {
            var classAttri = namedTypeSymbol.GetAttributes().First(e => e.AttributeClass.ToDisplayString() == msgPackClassAttributeFullName);
            // bool isBsonClass = namedTypeSymbol.GetAttributes().FirstOrDefault(e => e.AttributeClass.ToDisplayString() == dbDataClassAttributeFullName) != null;

            string strClassName = namedTypeSymbol.MetadataName;
            // string strOpType = "EMsgPackOpType.OP_DEFAULT";
            bool hasToken = false;
            bool isPartial = false;
            ProtocolSource source  = ProtocolSource.Internal;
            string strPartial = string.Empty;
            bool isPersistentData = false;
            bool isMsgPackEmbed = false;
            bool isShared = false;
            foreach (var pair in classAttri.NamedArguments)
            {
                if (pair.Key == "IsPartial")
                {
                    isPartial = (bool)pair.Value.Value;
                }
            }

            if (isPartial)
            {
                strPartial = "partial ";
            }

            foreach (var pair in classAttri.NamedArguments)
            {
                if (pair.Key == "HasToken")
                {
                    hasToken = (bool)pair.Value.Value;
                }
            }
            foreach (var pair in classAttri.NamedArguments)
            {
                if (pair.Key == "Source")
                {
                    source = (ProtocolSource)pair.Value.Value;
                }
            }

            foreach (var pair in classAttri.NamedArguments)
            {
                if (pair.Key == "IsShared")
                {
                    isShared = (bool)pair.Value.Value;
                }
            }

            List<KeyValuePair<string, PhoenixFieldInfo>> fieldName2FieldInfo = new List<KeyValuePair<string, PhoenixFieldInfo>>();

            List<IFieldSymbol> listFieldSymbol = namedTypeSymbol.GetMembers().OfType<IFieldSymbol>().ToList<IFieldSymbol>();
            for (int j = 0; j < listFieldSymbol.Count; j++)
            {
                IFieldSymbol property = listFieldSymbol[j];
                var propertyAttri = property.GetAttributes().First(e => e.AttributeClass.ToDisplayString() == msgPackPropertyAttributeFullName);
                if (propertyAttri == null)
                {
                    continue;
                }

                // var bsonAttri = property.GetAttributes().Where((data => data.));

                var attri = property.GetAttributes().First(e => e.AttributeClass.ToDisplayString() == msgPackPropertyAttributeFullName);
                PhoenixFieldInfo fieldInfo = null;
                if (attri != null)
                {
                    bool isNeedSerialize = true;
                    var args = attri.NamedArguments;
                    foreach (var arg in args)
                    {
                        if (arg.Key == "SerializeFlag")
                        {
                            int serializeFlag = (int)arg.Value.Value;
                            if (serializeFlag <= 1)
                            {
                                isNeedSerialize = false;
                            }
                            break;
                        }
                    }
                    if (!isNeedSerialize)
                    {
                        continue;
                    }

                    fieldInfo = new PhoenixFieldInfo();
                    fieldInfo.FieldSymbol = property;
                    fieldInfo.Name = property.MetadataName;
                    fieldInfo.TypeName = property.Type.ToDisplayString(SymbolDisplayFormat.MinimallyQualifiedFormat);

                    foreach (var arg in args)
                    {
                        if (arg.Key == "KeyWords")
                        {
                            fieldInfo.KeyWords = arg.Value.Value.ToString();
                        }

                        if (arg.Key == "Persist")
                        {
                            fieldInfo.Persist = (bool)arg.Value.Value;
                        }

                        if (arg.Key == "Sync")
                        {
                            fieldInfo.Sync = (int)arg.Value.Value;
                        }

                        if (arg.Key == "DefaultValue")
                        {
                            fieldInfo.defaultValue = arg.Value.Value;
                        }

                        if (arg.Key == "SerializeFlag")
                        {
                            fieldInfo.SerializeFlag = (int)arg.Value.Value;
                        }
                        if (arg.Key == "NewObject")
                        {
                            fieldInfo.NewObject = (bool)arg.Value.Value;
                        }
                    }

                    fieldName2FieldInfo.Add(new KeyValuePair<string, PhoenixFieldInfo>(property.MetadataName, fieldInfo));
                }

                // bson相关

            }

            string codeSubModuleStart = "";
            int index = 0;
            // 生成代码示例
            string parentClass = string.Empty;
            // int classType = 0; // 0-普通 1-协议 2-同步Data

            var classType = CheckClassSuffixForEmbed(namedTypeSymbol);
            // isPersistentData = tuple.Item1;
            // isMsgPackEmbed = tuple.Item2;
            if (classType == EMsgPackClassType.PersistentData)
            {
                // classType = 2;
                parentClass = ": DBCacheObject";
                codeSubModuleStart += $@"
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif";
            }
            else if (classType == EMsgPackClassType.Protocol)
            {
                // classType = 1;
                parentClass = ": MsgPackStructBase";
                index = 2;
                if (hasToken)
                {
                    index++;
                }
            }

            codeSubModuleStart += $@"
    [MessagePackObject]
    public {strPartial }class {strClassName} {parentClass}
    {{";
            string strModule = strClassFileName.Replace("MsgPack_", string.Empty).ToUpper();
            string strSubModule = strClassName.Replace("MsgPack_", string.Empty).ToUpper();
            string strProtoCode = $"EProtoCode.{strModule}_{strSubModule}";

            if (m_listProtocolDesc.Find((desc => desc.ProtoName == strClassName)) != null)
            {
                System.Diagnostics.Debug.Assert(false, $"Duplicate Error ClassName = {strClassName}， ProtoCode = {moduleInfo.iProtoIncrease}");
            }

            string codebody = "";
            if (classType == EMsgPackClassType.Protocol)
            {
                Int32 iProtoCode = 0;
                if (!moduleInfo.dictProtoCode.TryGetValue(strProtoCode, out iProtoCode))
                {
                    iProtoCode = moduleInfo.iProtoIncrease;
                    ++moduleInfo.iProtoIncrease;
                }
                if (m_dictProtoCode.ContainsKey(iProtoCode))
                {
                    System.Diagnostics.Debug.Assert(false, $"Duplicate Error ProtoCode = {moduleInfo.iProtoIncrease}");
                }
                moduleInfo.dictProtoCode[strProtoCode] = iProtoCode;
                m_dictProtoCode[iProtoCode] = strProtoCode.Replace("EProtoCode.", string.Empty);
                //m_dictClassName2ProtoCode[strClassName] = iProtoCode;
                m_listProtocolDesc.Add(new ProtocolDesc()
                {
                    ProtoName = strClassName,
                    ProtoCode = iProtoCode,
                    IsClient = moduleInfo.isClientProtocol,
                    IsNtf = strClassName.ToLower().EndsWith("ntf"),
                    IsFromClient = source == ProtocolSource.Client,
                    IsShared = isShared
                });

                m_dictMsgPackModuleByProtoCode[iProtoCode] = moduleInfo;

                String strKeyModuleProto = $"{strModuleDescName}={strProtoCode}";

                codebody += $@"
        public override void Init()
		{{
			ProtoCode = {strProtoCode};
		}}";

                if (hasToken)
                {
                    codebody += $@"

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;"
            ;
                }
                codebody += $@"
";
            }

            if (hasToken)
            {
                codebody += $@"
        [Key({index++})]
        public uint Token {{ get; set; }}
";
            }

            if (isShared)
            {
                codebody += $@"
        [IgnoreMember]
        public static readonly {strClassName} Shared = new {strClassName}();
";
            }

            List<KeyValuePair<string, PhoenixFieldInfo>> listPhoenixFieldInfo = fieldName2FieldInfo.ToList<KeyValuePair<string, PhoenixFieldInfo>>();
            for (int j = 0; j < listPhoenixFieldInfo.Count; j++)
            {
                var pair = listPhoenixFieldInfo[j];
                var fieldInfo = pair.Value;
                string strDefaultValue = string.Empty;
                var attributes = fieldInfo.FieldSymbol.GetAttributes();
                var dbProperty = attributes.FirstOrDefault(e =>
                    e.AttributeClass.ToDisplayString() == dbDataPropertyAttributeFullName);
                bool isDBProperty = dbProperty != null;

                codebody += $@"
        [Key({index})]";
                index++;

                if (fieldInfo.NewObject)
                {
                    string strPropertyType = fieldInfo.TypeName;
                    if (strPropertyType.Contains("<") && strPropertyType.Contains(">"))
                    {
                        int idxTemp = 0;
                        if (strPropertyType.Contains(","))
                        {
                            idxTemp = strPropertyType.LastIndexOf(',');
                            strPropertyType = strPropertyType.Substring(idxTemp + 1);
                            strPropertyType = strPropertyType.Trim();
                            strPropertyType = strPropertyType.Replace(">", "");
                        }
                        else
                        {
                            strPropertyType = strPropertyType.Trim();
                            idxTemp = strPropertyType.LastIndexOf('<');
                            strPropertyType = strPropertyType.Substring(idxTemp + 1);
                            strPropertyType = strPropertyType.Replace(">", "");
                        }
                    }
                }

                if (!string.IsNullOrEmpty(fieldInfo.KeyWords))
                {
                    fieldInfo.KeyWords += " ";
                }

                if (isDBProperty) //加上BsonElement特性
                {
                    var bsonElementName = pair.Key;
                    var bsonElementAttr = dbProperty.NamedArguments.FirstOrDefault(e => e.Key == "BsonElement");
                    if (!string.IsNullOrEmpty(bsonElementAttr.Key))
                    {
                        bsonElementName = bsonElementAttr.Value.Value.ToString();
                    }

                    codebody += $@"
        #if PHOENIX_SERVER
        [BsonElement(""{bsonElementName}"")]";

                    //如果是DateTime类型，则加上BsonDateTimeOptions特性
                    if (fieldInfo.TypeName == "DateTime")
                    {
                        codebody += $@"
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]";
                    }
                    else if (fieldInfo.TypeName.Contains("Dictionary"))
                    {
                        codebody += $@"
        [BsonDictionaryOptions(Representation = MongoDB.Bson.Serialization.Options.DictionaryRepresentation.ArrayOfArrays)]";
                    }
                    codebody += $@"
        #endif";

                }

                codebody += $@"
        public {fieldInfo.KeyWords}{fieldInfo.TypeName} {pair.Key}";

                string strTypeName = fieldInfo.TypeName.ToLower();
                if (fieldInfo.NewObject || strTypeName.Contains("list") || strTypeName.Contains("dictionary") || CheckPropertyTypeNameForNewObject(strTypeName))
                {
                    codebody += $@" = new {fieldInfo.TypeName}();";
                }
                else
                {
                    if (fieldInfo.defaultValue != null)
                    {
                        if (strTypeName.Contains("string"))
                        {
                            fieldInfo.defaultValue = $"\"{fieldInfo.defaultValue}\"";
                        }

                        if (strTypeName.Contains("bool"))
                        {
                            fieldInfo.defaultValue = fieldInfo.defaultValue.ToString().ToLower();
                        }

                        if (fieldInfo.KeyWords.Contains("readonly"))
                        {
                            codebody += $@" = {fieldInfo.defaultValue};";
                        }
                        else
                        {
                            string strDefaultExt = string.Empty;
                            if (strTypeName.Contains("float") || strTypeName.Contains("single"))
                            {
                                strDefaultExt = "f";
                            }
                            else if (strTypeName.Contains("double"))
                            {
                                strDefaultExt = "d";
                            }
                            codebody += $@" {{ get; set; }} = {fieldInfo.defaultValue}{strDefaultExt};";
                        }
                    }
                    else
                    {
                        codebody += $@" {{ get; set; }}";
                    }
                }

                codebody += $@"
";
            }

            string codeSubModuleEnd = $@"
    }}
";
            string codeSubModule = codeSubModuleStart + codebody + codeSubModuleEnd;
            codeModule += codeSubModule;
        }

        string codeModuleEnd = $@"
}}";
        codeModule += codeModuleEnd;
        // 向编译器添加生成的源代码
        context.AddSource(getFileName(), SourceText.From(codeModule, Encoding.UTF8));
    }

    private void GenerateMsgPackProtoDeclare(GeneratorExecutionContext context)
    {
        List<int> listSortKey = m_dictProtoCode.OrderBy(kvp => kvp.Key).Select(kvp => kvp.Key).ToList();
        string codeModule = $@"// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif

namespace Phoenix.MsgPackLogic.Protocol
{{
    public enum EResultType : SByte
    {{
        SUCCESS = 0,
        FAIL = 1,
        ERROR = 2,
        TIMEOUT = 3,
        WARNING = 4,
        EXCEPTION = 5,
    }}

    public enum EMsgPackOpType
    {{
        // Do nothing
        OP_NULL = 0,
        // Excute default protocol logic
        OP_DEFAULT,
        // Call Client <-> Server Func
        OP_FUNC,
        // Clear all elements
        OP_CLEAR,
        // Erase key (for array, the key is index)
        OP_ERASE,
        // Set key-value
        OP_UPDATE,
        // Array operation
        OP_A_APPEND,
        OP_A_INSERT,
        OP_BITSET_SET,
        OP_BITSET_RESET,
        OP_SET_ADD,
        OP_SET_REMOVE,



        OP_END
    }}

    public enum EProtoCode
    {{
        // REQ Client to Server
        // ACK Server to Client
        // NTF Server to Client
        // PROTO Server to Server

";

        Dictionary<string, string> dictProtoCode = new Dictionary<string, string>();
        for (int i = 0; i < listSortKey.Count; i++)
        {
            int iProtoCode = listSortKey[i];
            if (m_dictMsgPackModuleByProtoCode.TryGetValue(iProtoCode, out MsgPackModuleInfo moduleInfo))
            {
                ProtoCodeAnnotation(ref codeModule, iProtoCode, moduleInfo.iProtoStart, moduleInfo.iProtoEnd, $"{moduleInfo.strModulename.ToUpper()}_START", dictProtoCode);
                if (m_dictProtoCode.TryGetValue(iProtoCode, out string strProtoCode))
                {
                    codeModule += $@"        {strProtoCode} = {iProtoCode},
";
                }

            }
        }

        // 0 ~ 49
        // EXAMPLE_START = 0,

        // 50 ~ 99
        // TEST_START = 50,


        // 200 ~ 399
        // CORE_START = 200

        // 400 ~ 599
        // SYS_START = 400

        // 600 ~ 799
        // RPC_START = 600

        // 800 ~ 999
        // LOGIN_START = 800,

        // 1000 ~ 1499
        // PLAYERINIT_START = 1000,

        // 1500 ~ 1999
        // COMMON_START = 1500,

        // 2000 ~ 2999
        // WORLD_START = 2000,

        // 3000 ~ 3999
        // HAKONIWA_START = 3000,

        // 4000 ~ 4999
        // BATTLE_START = 4000,

        // 5000 ~ 5999
        // BATTLE_DUEL_START = 5000,

        // 6000 ~ 6999
        // BATTLE_PVP_START = 6000,

        // 7000 ~ 7999
        // BAG_START = 7000,


        codeModule += $@"
    }}

    [MessagePackObject]
    public class MsgPackStructBase
    {{
        [Key(0)]
        public EProtoCode ProtoCode {{ get; set; }}

        [Key(1)]
        public EMsgPackOpType OpType {{ get; set; }}

        public virtual bool HasToken() => false;

        public virtual uint? GetToken() => null;

        public virtual void SetToken(uint token) {{ }}

        public MsgPackStructBase()
        {{
            Init();
        }}

        public virtual void Init() {{ }}

    }}

    /// <summary>
    /// db缓存对象基类,Dirty状态标记数据脏
    /// </summary>
    /// <returns></returns>
    [MessagePackObject]
    public partial class DBCacheObject
    {{
        /// <summary>
        /// 获取或设置一个值，指示此模式是否处于脏状态（需要保存）
        /// </summary>
#if PHOENIX_SERVER
        [BsonIgnore]
#endif
        [IgnoreMember]
        private bool IsDirty {{ get;
            set; }} = false;

        /// <summary>
        /// 将模式标记为脏状态
        /// </summary>
        public void MarkDirty()
        {{
            IsDirty = true;
        }}

        /// <summary>
        /// 重置脏状态标记
        /// </summary>
        public void ResetDirty()
        {{
            IsDirty = false;
        }}

        /// <summary>
        /// 检查模式是否处于脏状态
        /// </summary>
        /// <returns>如果模式处于脏状态，则为 true；否则为 false</returns>
        public bool CheckDirty()
        {{
            return IsDirty;
        }}
    }}



}}";
        // 向编译器添加生成的源代码
        context.AddSource("MsgPackStruct.cs", SourceText.From(codeModule, Encoding.UTF8));
    }

    public enum EMsgPackClassType
    {
        Normal = 0, // 普通类
        Protocol = 1, // 协议类
        PersistentData = 2, // 持久化数据类
    }


    private String[] arrTypeNameSuffix = { "req", "ack", "ntf", "proto" };
    private String arrTypeNameDB = "_DB_";
    private EMsgPackClassType CheckClassSuffixForEmbed(INamedTypeSymbol namedTypeSymbol)
    {
        var classAttri = namedTypeSymbol.GetAttributes().First(e => e.AttributeClass.ToDisplayString() == msgPackClassAttributeFullName);
        bool isBsonClass = namedTypeSymbol.GetAttributes().FirstOrDefault(e => e.AttributeClass.ToDisplayString() == dbDataClassAttributeFullName) != null;
        string strClassName = namedTypeSymbol.MetadataName;
        // 检查类名后缀
        for (int i = 0; i < arrTypeNameSuffix.Length; i++)
        {
            if (strClassName.ToLower().EndsWith(arrTypeNameSuffix[i]))
            {
                return EMsgPackClassType.Protocol;
            }
        }

        // 检查是否为持久化数据类
        if (isBsonClass)
        {
            return EMsgPackClassType.PersistentData;
        }


        return EMsgPackClassType.Normal;


        // bool isEmbed = true;
        // bool isDB = strClassName.Contains(arrTypeNameDB);
        //
        // String[] arrStrClassName = strClassName.Split('_');
        //
        // if (arrStrClassName.Length > 0)
        // {
        //     String strSuffix = arrStrClassName[arrStrClassName.Length - 1].ToLower();
        //     if (arrTypeNameSuffix.Contains(strSuffix))
        //     {
        //         isEmbed = false;
        //     }
        // }
        //
        // return new Tuple<bool, bool>(isDB, isEmbed);
    }

    private String[] arrPropertyTypeName = { "byte", "short", "int", "long", "float", "single", "double", "string", "bool" };
    private bool CheckPropertyTypeNameForNewObject(String strTypeName)
    {
        for (int i = 0; i < arrPropertyTypeName.Length; i++)
        {
            if (strTypeName.Contains(arrPropertyTypeName[i]))
            {
                return false;
            }
        }
        return true;
    }

    private void ProtoCodeAnnotation(ref string strCodeModule, int iProtoCode, int iProtoStart, int iProtoEnd, string strAnnotation, Dictionary<string, string> dictProtoCode)
    {
        if (iProtoCode >= iProtoStart && iProtoCode <= iProtoEnd && !dictProtoCode.ContainsKey(strAnnotation))
        {
            dictProtoCode.Add(strAnnotation, strAnnotation);
            strCodeModule += $@"
        // {iProtoStart} ~ {iProtoEnd}
        {strAnnotation} = {iProtoStart},
";
        }
    }

    // =========================================MsgPack Class Generator End=============================================

}

public class PhoenixFieldInfo
{
    public string Name { get; set; } = "";
    public string TypeName { get; set; } = "";
    public int Sync { get; set; } = 0;
    public object defaultValue { get; set; } = null;
    public bool Persist { get; internal set; } = false;

    public IFieldSymbol FieldSymbol;

    // DB Field
    public string KeyWords { get; set; } = "";
    public int SerializeFlag { get; set; } = 0;
    public bool NewObject { get; set; } = false;


    // Bson相关
    public bool IsDBProperty { get; set; } = false;
    public string BsonElementName { get; set; } = "";
}

public class TypeSymbolUtils
{
    public static bool InheritsFrom(ITypeSymbol typeSymbol, string baseClassName)
    {
        ITypeSymbol currentType = typeSymbol;

        while (currentType != null)
        {
            if (currentType.Name == baseClassName)
            {
                return true;
            }

            currentType = currentType.BaseType;
        }

        return false;
    }

    public static bool IsGenericClassAndRetrieveTypeArguments(ITypeSymbol typeSymbol, out ITypeSymbol[] typeArguments)
    {
        typeArguments = null;

        if (typeSymbol is INamedTypeSymbol namedTypeSymbol && namedTypeSymbol.IsGenericType)
        {
            typeArguments = namedTypeSymbol.TypeArguments.ToArray();
            return true;
        }

        return false;
    }
}

