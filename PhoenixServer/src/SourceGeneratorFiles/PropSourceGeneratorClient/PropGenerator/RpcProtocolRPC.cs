// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    [MessagePackObject]
    public partial class ForwardClientMessageToServerNtf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.RPC_FORWARDCLIENTMESSAGETOSERVERNTF;
		}

        [Key(2)]
        public long PlayerId { get; set; }

        [Key(3)]
        public uint FromGate { get; set; }

        [Key(4)]
        public int MsgProtoCode { get; set; }

        [Key(5)]
        public byte[] Data { get; set; }

    }

    [MessagePackObject]
    public partial class ForwardMessageToClientNtf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.RPC_FORWARDMESSAGETOCLIENTNTF;
		}

        [Key(2)]
        public long PlayerId { get; set; }

        [Key(3)]
        public uint SrcHost { get; set; }

        [Key(4)]
        public int MsgProtoCode { get; set; }

        [Key(5)]
        public byte[] Data { get; set; }

        [Key(6)]
        public byte Flag { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_ForwardClientMsgBroadcast_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.RPC_S_FORWARDCLIENTMSGBROADCAST_PROTO;
		}

        [Key(2)]
        public List<long> UidList = new List<long>();

        [Key(3)]
        public int SrcHost { get; set; }

        [Key(4)]
        public byte[] Data { get; set; }

        [Key(5)]
        public bool IsBroadcastAll { get; set; }

    }

}