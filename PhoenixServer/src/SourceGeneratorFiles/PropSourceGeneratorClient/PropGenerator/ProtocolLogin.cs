// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    [MessagePackObject]
    public class LoginByAuthTokenReq : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.LOGIN_LOGINBYAUTHTOKENREQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public string AuthToken { get; set; }

        [Key(5)]
        public string ClientVersion { get; set; }

        [Key(6)]
        public string ClientDeviceId { get; set; }

        [Key(7)]
        public string Localization { get; set; }

        [Key(8)]
        public string AesKey { get; set; }

    }

    [MessagePackObject]
    public class LoginByAuthTokenAck : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.LOGIN_LOGINBYAUTHTOKENACK;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public int ErrCode { get; set; }

        [Key(5)]
        public string SessionToken { get; set; }

    }

    [MessagePackObject]
    public class LoginBySessionTokenReq : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.LOGIN_LOGINBYSESSIONTOKENREQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public string SessionToken { get; set; }

        [Key(5)]
        public string ClientVersion { get; set; }

        [Key(6)]
        public string Localization { get; set; }

        [Key(7)]
        public string GameService { get; set; }

    }

    [MessagePackObject]
    public class LoginBySessionTokenAck : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.LOGIN_LOGINBYSESSIONTOKENACK;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public int ErrCode { get; set; }

        [Key(5)]
        public string GameService { get; set; }

        [Key(6)]
        public byte XORCode { get; set; }

    }

    [MessagePackObject]
    public class ServerDisconnectNtf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.LOGIN_SERVERDISCONNECTNTF;
		}

        [Key(2)]
        public int ReasonCode { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_Logout_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.LOGIN_LOGOUT_REQ;
		}

    }

}