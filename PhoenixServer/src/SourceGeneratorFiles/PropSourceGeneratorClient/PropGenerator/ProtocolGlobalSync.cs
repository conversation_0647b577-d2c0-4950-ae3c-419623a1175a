// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    [MessagePackObject]
    public class MsgPack_HeartBeat_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.GLOBALSYNC_HEARTBEAT_REQ;
		}

        [IgnoreMember]
        public static readonly MsgPack_HeartBeat_Req Shared = new MsgPack_HeartBeat_Req();

    }

    [MessagePackObject]
    public class MsgPack_HeartBeat_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.GLOBALSYNC_HEARTBEAT_ACK;
		}

        [Key(2)]
        public long CurrentServerTime { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_ServerTime_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.GLOBALSYNC_SERVERTIME_REQ;
		}

    }

    [MessagePackObject]
    public class MsgPack_ServerTime_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.GLOBALSYNC_SERVERTIME_ACK;
		}

        [Key(2)]
        public long CurrentServerTime { get; set; }

    }

}