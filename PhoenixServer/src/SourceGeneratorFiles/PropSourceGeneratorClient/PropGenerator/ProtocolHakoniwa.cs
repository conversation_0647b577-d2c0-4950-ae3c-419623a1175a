// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    [MessagePackObject]
    public class PosInfo 
    {
        [Key(0)]
        #if PHOENIX_SERVER
        [BsonElement("X")]
        #endif
        public float X { get; set; }

        [Key(1)]
        #if PHOENIX_SERVER
        [BsonElement("Y")]
        #endif
        public float Y { get; set; }

        [Key(2)]
        #if PHOENIX_SERVER
        [BsonElement("Z")]
        #endif
        public float Z { get; set; }

    }

    [MessagePackObject]
    public class HakoniwaQuestCondInfo 
    {
    }

    [MessagePackObject]
    public class HakoniwaQuestInfo 
    {
        [Key(0)]
        #if PHOENIX_SERVER
        [BsonElement("QuestCondInfos")]
        #endif
        public List<HakoniwaQuestCondInfo> QuestCondInfos = new List<HakoniwaQuestCondInfo>();

    }

    [MessagePackObject]
    public class HakoniwaTreasureInfo 
    {
    }

    [MessagePackObject]
    public class ExploringHakoniwaInfo 
    {
        [Key(0)]
        #if PHOENIX_SERVER
        [BsonElement("HakoniwaId")]
        #endif
        public int HakoniwaId { get; set; }

        [Key(1)]
        #if PHOENIX_SERVER
        [BsonElement("Quests")]
        #endif
        public List<HakoniwaQuestInfo> Quests = new List<HakoniwaQuestInfo>();

        [Key(2)]
        #if PHOENIX_SERVER
        [BsonElement("CreateTime")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        #endif
        public DateTime CreateTime = new DateTime();

        [Key(3)]
        #if PHOENIX_SERVER
        [BsonElement("UnlockLocationIds")]
        #endif
        public List<int> UnlockLocationIds = new List<int>();

        [Key(4)]
        #if PHOENIX_SERVER
        [BsonElement("SceneId")]
        #endif
        public int SceneId { get; set; }

        [Key(5)]
        #if PHOENIX_SERVER
        [BsonElement("PosInfo")]
        #endif
        public PosInfo PosInfo = new PosInfo();

    }

    [MessagePackObject]
    public class CompletedHakoniwaInfo 
    {
        [Key(0)]
        #if PHOENIX_SERVER
        [BsonElement("CompletedQuestIds")]
        #endif
        public List<int> CompletedQuestIds = new List<int>();

    }

    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public class PlayerCompDataHakoniwaInfo : DBCacheObject
    {
        [Key(0)]
        #if PHOENIX_SERVER
        [BsonElement("ExploringHakoniwaList")]
        #endif
        public List<ExploringHakoniwaInfo> ExploringHakoniwaList = new List<ExploringHakoniwaInfo>();

        [Key(1)]
        #if PHOENIX_SERVER
        [BsonElement("CompletedHakoniwaList")]
        #endif
        public List<CompletedHakoniwaInfo> CompletedHakoniwaList = new List<CompletedHakoniwaInfo>();

    }

    [MessagePackObject]
    public class PlayerHakoniwaCompDataNtf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.HAKONIWA_PLAYERHAKONIWACOMPDATANTF;
		}

        [Key(2)]
        public PlayerCompDataHakoniwaInfo HakoniwaInfo = new PlayerCompDataHakoniwaInfo();

    }

    [MessagePackObject]
    public class HakoniwaCreateReq : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.HAKONIWA_HAKONIWACREATEREQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public int HakoniwaId { get; set; }

    }

    [MessagePackObject]
    public class HakoniwaCreateAck : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.HAKONIWA_HAKONIWACREATEACK;
		}

        [Key(2)]
        public int ErrCode { get; set; }

        [Key(3)]
        public int HakoniwaId { get; set; }

        [Key(4)]
        public ExploringHakoniwaInfo HakoniwaInfo = new ExploringHakoniwaInfo();

    }

    [MessagePackObject]
    public class HakoniwaEnterReq : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.HAKONIWA_HAKONIWAENTERREQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public int HakoniwaId { get; set; }

        [Key(5)]
        public int EntryLocationId { get; set; }

    }

    [MessagePackObject]
    public class HakoniwaEnterAck : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.HAKONIWA_HAKONIWAENTERACK;
		}

        [Key(2)]
        public int ErrCode { get; set; }

        [Key(3)]
        public int HakoniwaId { get; set; }

        [Key(4)]
        public int EntryLocationId { get; set; }

    }

    [MessagePackObject]
    public class HakoniwaGiveUpReq : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.HAKONIWA_HAKONIWAGIVEUPREQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public int HakoniwaId { get; set; }

    }

    [MessagePackObject]
    public class HakoniwaGiveUpAck : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.HAKONIWA_HAKONIWAGIVEUPACK;
		}

        [Key(2)]
        public int ErrCode { get; set; }

        [Key(3)]
        public int HakoniwaId { get; set; }

    }

}