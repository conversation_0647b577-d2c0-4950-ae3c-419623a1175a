// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    [MessagePackObject]
    public class MsgPack_Duel_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.DUEL_DUEL_REQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public long UidTarget { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_Duel_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.DUEL_DUEL_ACK;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public long ErrCode { get; set; }

        [Key(5)]
        public long UidTarget { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_Duel_Ntf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.DUEL_DUEL_NTF;
		}

        [Key(2)]
        public long SrcUid { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_DuelReply_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.DUEL_DUELREPLY_REQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public bool IsAccept { get; set; }

        [Key(5)]
        public long SrcUid { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_DuelReply_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.DUEL_DUELREPLY_ACK;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public long ErrCode { get; set; }

        [Key(5)]
        public long SrcUid { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_DuelReply_Ntf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.DUEL_DUELREPLY_NTF;
		}

        [Key(2)]
        public bool IsAccept { get; set; }

        [Key(3)]
        public long UidTarget { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_DuelBattleSessionInit_Ntf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.DUEL_DUELBATTLESESSIONINIT_NTF;
		}

        [Key(2)]
        public long SessionId { get; set; }

        [Key(3)]
        public int BattleRid { get; set; }

        [Key(4)]
        public int RandomSeed { get; set; }

        [Key(5)]
        public List<ulong> PlayerIds = new List<ulong>();

    }

    [MessagePackObject]
    public class MsgPack_DuelBattleSessionEnd_Ntf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.DUEL_DUELBATTLESESSIONEND_NTF;
		}

        [Key(2)]
        public long SessionId { get; set; }

        [Key(3)]
        public int ErrCode { get; set; }

        [Key(4)]
        public long WinnerUid { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_DuelReply_Ntf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.DUEL_S_DUELREPLY_NTF;
		}

        [Key(2)]
        public bool IsAccept { get; set; }

        [Key(3)]
        public long UidTarget { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_Duel_Ntf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.DUEL_S_DUEL_NTF;
		}

        [Key(2)]
        public long SrcTarget { get; set; }

    }

}