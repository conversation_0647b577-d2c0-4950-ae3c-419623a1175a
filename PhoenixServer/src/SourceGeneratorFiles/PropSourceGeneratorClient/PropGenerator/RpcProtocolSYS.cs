// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    [MessagePackObject]
    public class MsgPack_C_CallBasePlayerMethod_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.SYS_C_CALLBASEPLAYERMETHOD_PROTO;
		}

        [Key(2)]
        public string Func { get; set; }

        [Key(3)]
        public byte[] Args { get; set; }

        [Key(4)]
        public int FuncIndex { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_C_CallEntityMethod_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.SYS_C_CALLENTITYMETHOD_PROTO;
		}

        [Key(2)]
        public long EntityId { get; set; }

        [Key(3)]
        public string Func { get; set; }

        [Key(4)]
        public byte[] Args { get; set; }

        [Key(5)]
        public int FuncIndex { get; set; }

        [Key(6)]
        public sbyte ForwardType { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_BasicEntityCreate_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.SYS_S_BASICENTITYCREATE_PROTO;
		}

        [Key(2)]
        public long EntityId { get; set; }

        [Key(3)]
        public string Classname { get; set; }

        [Key(4)]
        public long PrefabId { get; set; }

        [Key(5)]
        public byte[] Initdata { get; set; }

        [Key(6)]
        public string ModelId { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_CallClientMethod_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.SYS_S_CALLCLIENTMETHOD_PROTO;
		}

        [Key(2)]
        public long EntityId { get; set; }

        [Key(3)]
        public string FuncOrIndex { get; set; }

        [Key(4)]
        public byte[] Args { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_EntityCreawte_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.SYS_S_ENTITYCREAWTE_PROTO;
		}

        [Key(2)]
        public long EntityId { get; set; }

        [Key(3)]
        public string Classname { get; set; }

        [Key(4)]
        public byte[] Initdata { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_EntityDestroy_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.SYS_S_ENTITYDESTROY_PROTO;
		}

        [Key(2)]
        public long EntityId { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_EntityMigrate_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.SYS_S_ENTITYMIGRATE_PROTO;
		}

    }

    [MessagePackObject]
    public class MsgPack_S_EntityMigrateReply_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.SYS_S_ENTITYMIGRATEREPLY_PROTO;
		}

    }

    [MessagePackObject]
    public class MsgPack_S_ForwardEntityRpc_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.SYS_S_FORWARDENTITYRPC_PROTO;
		}

        [Key(2)]
        public long EntityId { get; set; }

        [Key(3)]
        public long Uid { get; set; }

        [Key(4)]
        public string Funcname { get; set; }

        [Key(5)]
        public byte[] ArgsData { get; set; }

    }

}