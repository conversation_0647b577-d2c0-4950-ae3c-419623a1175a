// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    [MessagePackObject]
    public class MsgPack_Test_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.TEST_TEST_REQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public long UidTarget { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_Test_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.TEST_TEST_ACK;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public int ErrCode { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_Echo_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.TEST_ECHO_REQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public string Content { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_Echo_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.TEST_ECHO_ACK;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public string Content { get; set; }

    }

}