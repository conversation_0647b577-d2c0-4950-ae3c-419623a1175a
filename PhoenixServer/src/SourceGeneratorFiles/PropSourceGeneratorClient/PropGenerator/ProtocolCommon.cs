// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public class MsgPack_DB_Item : DBCacheObject
    {
        [Key(0)]
        #if PHOENIX_SERVER
        [BsonElement("Id")]
        #endif
        public long Id { get; set; }

        [Key(1)]
        #if PHOENIX_SERVER
        [BsonElement("Type")]
        #endif
        public int Type { get; set; }

        [Key(2)]
        #if PHOENIX_SERVER
        [BsonElement("Count")]
        #endif
        public long Count { get; set; }

        [Key(3)]
        #if PHOENIX_SERVER
        [BsonElement("Name")]
        #endif
        public string Name { get; set; }

    }

}