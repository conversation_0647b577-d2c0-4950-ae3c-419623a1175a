// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    [MessagePackObject]
    public class MsgPack_Example_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.EXAMPLE_EXAMPLE_REQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public long UidTarget { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_Example_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.EXAMPLE_EXAMPLE_ACK;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public int ErrCode { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_ExampleData_Ntf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.EXAMPLE_EXAMPLEDATA_NTF;
		}

        [Key(2)]
        public MsgPack_DB_ExampleData ExampleData = new MsgPack_DB_ExampleData();

    }

    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public class MsgPack_DB_ExampleData : DBCacheObject
    {
        [Key(0)]
        #if PHOENIX_SERVER
        [BsonElement("SByteValue")]
        #endif
        public sbyte SByteValue { get; set; }

        [Key(1)]
        #if PHOENIX_SERVER
        [BsonElement("ByteValue")]
        #endif
        public byte ByteValue { get; set; }

        [Key(2)]
        #if PHOENIX_SERVER
        [BsonElement("Int16Value")]
        #endif
        public short Int16Value { get; set; }

        [Key(3)]
        #if PHOENIX_SERVER
        [BsonElement("UInt16Value")]
        #endif
        public ushort UInt16Value { get; set; }

        [Key(4)]
        #if PHOENIX_SERVER
        [BsonElement("Int32Value")]
        #endif
        public int Int32Value { get; set; }

        [Key(5)]
        #if PHOENIX_SERVER
        [BsonElement("UInt32Value")]
        #endif
        public uint UInt32Value { get; set; }

        [Key(6)]
        #if PHOENIX_SERVER
        [BsonElement("Int64Value")]
        #endif
        public long Int64Value { get; set; }

        [Key(7)]
        #if PHOENIX_SERVER
        [BsonElement("UInt64Value")]
        #endif
        public ulong UInt64Value { get; set; }

        [Key(8)]
        #if PHOENIX_SERVER
        [BsonElement("FloatValue")]
        #endif
        public float FloatValue { get; set; }

        [Key(9)]
        #if PHOENIX_SERVER
        [BsonElement("SingleValue")]
        #endif
        public float SingleValue { get; set; }

        [Key(10)]
        #if PHOENIX_SERVER
        [BsonElement("DoubleValue")]
        #endif
        public double DoubleValue { get; set; }

        [Key(11)]
        #if PHOENIX_SERVER
        [BsonElement("StringValue")]
        #endif
        public string StringValue { get; set; } = "AAA";

        [Key(12)]
        #if PHOENIX_SERVER
        [BsonElement("BooleanValue")]
        #endif
        public bool BooleanValue { get; set; } = false;

        [Key(13)]
        #if PHOENIX_SERVER
        [BsonElement("ListExample")]
        #endif
        public List<ulong> ListExample = new List<ulong>();

        [Key(14)]
        #if PHOENIX_SERVER
        [BsonElement("DictExample")]
        [BsonDictionaryOptions(Representation = MongoDB.Bson.Serialization.Options.DictionaryRepresentation.ArrayOfArrays)]
        #endif
        public Dictionary<ulong, double> DictExample = new Dictionary<ulong, double>();

        [Key(15)]
        #if PHOENIX_SERVER
        [BsonElement("ListItemExample")]
        #endif
        public List<MsgPack_DB_Item> ListItemExample = new List<MsgPack_DB_Item>();

        [Key(16)]
        #if PHOENIX_SERVER
        [BsonElement("DictItemExample")]
        [BsonDictionaryOptions(Representation = MongoDB.Bson.Serialization.Options.DictionaryRepresentation.ArrayOfArrays)]
        #endif
        public Dictionary<string, MsgPack_DB_Item> DictItemExample = new Dictionary<string, MsgPack_DB_Item>();

    }

    [MessagePackObject]
    public class MsgPack_ExampleExternal_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.EXAMPLE_EXAMPLEEXTERNAL_REQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public bool IsExternal { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_ExampleExternal_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.EXAMPLE_EXAMPLEEXTERNAL_ACK;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public int ErrCode { get; set; }

        [Key(5)]
        public string strExternalInfo { get; set; }

    }

}