// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    [MessagePackObject]
    public class PlayerWorldCompDataNtf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.WORD_PLAYERWORLDCOMPDATANTF;
		}

        [Key(2)]
        public PlayerCompDataWorldInfo WorldInfo = new PlayerCompDataWorldInfo();

    }

    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public class PlayerCompDataWorldInfo : DBCacheObject
    {
        [Key(0)]
        #if PHOENIX_SERVER
        [BsonElement("CompletedStoryIds")]
        #endif
        public List<int> CompletedStoryIds = new List<int>();

    }

}