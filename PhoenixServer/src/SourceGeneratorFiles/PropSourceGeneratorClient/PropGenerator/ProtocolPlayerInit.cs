// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    [MessagePackObject]
    public class PlayerInfoInitReq : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.PLAYERINIT_PLAYERINFOINITREQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [IgnoreMember]
        public static readonly PlayerInfoInitReq Shared = new PlayerInfoInitReq();

    }

    [MessagePackObject]
    public class PlayerInfoInitAck : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.PLAYERINIT_PLAYERINFOINITACK;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public int ErrCode { get; set; }

    }

    [MessagePackObject]
    public class CharacterCreateReq : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.PLAYERINIT_CHARACTERCREATEREQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public string NickName { get; set; }

    }

    [MessagePackObject]
    public class CharacterCreateAck : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.PLAYERINIT_CHARACTERCREATEACK;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public int ErrCode { get; set; }

    }

    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public class TestInfo : DBCacheObject
    {
        [Key(0)]
        #if PHOENIX_SERVER
        [BsonElement("id")]
        #endif
        public string id { get; set; }

        [Key(1)]
        #if PHOENIX_SERVER
        [BsonElement("score")]
        #endif
        public int score { get; set; }

    }

    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public class PlayerCompDataBasicInfo : DBCacheObject
    {
        [Key(0)]
        #if PHOENIX_SERVER
        [BsonElement("Name")]
        #endif
        public string Name { get; set; }

        [Key(1)]
        #if PHOENIX_SERVER
        [BsonElement("Level")]
        #endif
        public int Level { get; set; }

        [Key(2)]
        #if PHOENIX_SERVER
        [BsonElement("Exp")]
        #endif
        public int Exp { get; set; }

        [Key(3)]
        #if PHOENIX_SERVER
        [BsonElement("PlayerId")]
        #endif
        public long PlayerId { get; set; }

        [Key(4)]
        #if PHOENIX_SERVER
        [BsonElement("Gender")]
        #endif
        public int Gender { get; set; }

    }

    [MessagePackObject]
    public class PlayerBasicCompDataNtf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.PLAYERINIT_PLAYERBASICCOMPDATANTF;
		}

        [Key(2)]
        public PlayerCompDataBasicInfo BasicInfo = new PlayerCompDataBasicInfo();

    }

    [MessagePackObject]
    public class PlayerDataSectionSyncEndNtf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.PLAYERINIT_PLAYERDATASECTIONSYNCENDNTF;
		}

        [IgnoreMember]
        public static readonly PlayerDataSectionSyncEndNtf Shared = new PlayerDataSectionSyncEndNtf();

    }

}