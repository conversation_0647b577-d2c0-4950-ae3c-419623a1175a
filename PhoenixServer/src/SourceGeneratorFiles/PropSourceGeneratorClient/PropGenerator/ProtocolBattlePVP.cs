// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    [MessagePackObject]
    public class MsgPack_BattleCommand_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.BATTLEPVP_BATTLECOMMAND_REQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public byte[] Command { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_BattleCommand_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.BATTLEPVP_BATTLECOMMAND_ACK;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public long ErrCode { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_BattleCommand_Ntf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.BATTLEPVP_BATTLECOMMAND_NTF;
		}

        [Key(2)]
        public byte[] Command { get; set; }

    }

}