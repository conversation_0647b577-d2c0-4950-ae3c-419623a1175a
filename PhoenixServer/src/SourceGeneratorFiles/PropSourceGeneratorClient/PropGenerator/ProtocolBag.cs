// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    [MessagePackObject]
    public class MsgPack_BagInfo_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.BAG_BAGINFO_REQ;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public long Uid { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_BagInfo_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.BAG_BAGINFO_ACK;
		}

        public override bool HasToken() => true;

        public override uint? GetToken() => Token;

        public override void SetToken(uint token) => Token = token;

        [Key(3)]
        public uint Token { get; set; }

        [Key(4)]
        public int ErrCode { get; set; }

        [Key(5)]
        public MsgPack_DB_BagData BagData = new MsgPack_DB_BagData();

    }

    #if PHOENIX_SERVER
    [BsonIgnoreExtraElements(true)]
    #endif
    [MessagePackObject]
    public class MsgPack_DB_BagData : DBCacheObject
    {
        [Key(0)]
        #if PHOENIX_SERVER
        [BsonElement("ListItemExample")]
        #endif
        public List<MsgPack_DB_Item> ListItemExample = new List<MsgPack_DB_Item>();

        [Key(1)]
        #if PHOENIX_SERVER
        [BsonElement("DictItemExample")]
        [BsonDictionaryOptions(Representation = MongoDB.Bson.Serialization.Options.DictionaryRepresentation.ArrayOfArrays)]
        #endif
        public Dictionary<string, MsgPack_DB_Item> DictItemExample = new Dictionary<string, MsgPack_DB_Item>();

    }

}