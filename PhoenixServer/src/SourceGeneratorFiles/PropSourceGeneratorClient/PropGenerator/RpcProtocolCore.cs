// Generated by Tools.  DO NOT EDIT!

using MessagePack;
using System;
using System.Collections.Generic;
// using Phoenix.ConfigData;

#if PHOENIX_SERVER
using MongoDB.Bson.Serialization.Attributes;
#endif
namespace Phoenix.MsgPackLogic.Protocol
{
    [MessagePackObject]
    public class MsgPack_S_CheckConsistentDigest_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_CHECKCONSISTENTDIGEST_PROTO;
		}

        [Key(2)]
        public List<MsgPack_S_CheckConsistentDigestInfo> Digests = new List<MsgPack_S_CheckConsistentDigestInfo>();

    }

    [MessagePackObject]
    public class MsgPack_S_CheckConsistentDigestInfo 
    {
        [Key(0)]
        public string Role { get; set; }

        [Key(1)]
        public uint Digest { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_EngineRpc_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_ENGINERPC_PROTO;
		}

    }

    [MessagePackObject]
    public class MsgPack_S_RpcConnect_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_RPCCONNECT_PROTO;
		}

        [Key(2)]
        public int Magic { get; set; }

        [Key(3)]
        public byte[] AuthToken { get; set; }

        [Key(4)]
        public MsgPack_S_ServerNodeInfo_Proto NodeInfo = new MsgPack_S_ServerNodeInfo_Proto();

        [Key(5)]
        public long ProcId { get; set; }

        [Key(6)]
        public bool IsReady { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_RpcEstablish_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_RPCESTABLISH_PROTO;
		}

        [Key(2)]
        public uint NodeId { get; set; }

        [Key(3)]
        public int ErrorCode { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_GetConsistentDigest_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_GETCONSISTENTDIGEST_PROTO;
		}

        [Key(2)]
        public string Role { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_NodesReady_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_NODESREADY_PROTO;
		}

        [Key(2)]
        public List<uint> ReadyNodeList = new List<uint>();

    }

    [MessagePackObject]
    public class MsgPack_S_NodeHeartbeat_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_NODEHEARTBEAT_REQ;
		}

        [Key(2)]
        public long Timestamp { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_NodeHeartbeat_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_NODEHEARTBEAT_ACK;
		}

        [Key(2)]
        public long Timestamp { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_NodeInfoList_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_NODEINFOLIST_PROTO;
		}

        [Key(2)]
        public List<MsgPack_S_ServerNodeInfo_Proto> NodeInfoList = new List<MsgPack_S_ServerNodeInfo_Proto>();

    }

    [MessagePackObject]
    public class MsgPack_S_NodeJoin_Ntf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_NODEJOIN_NTF;
		}

        [Key(2)]
        public MsgPack_S_ServerNodeInfo_Proto NodeInfo = new MsgPack_S_ServerNodeInfo_Proto();

    }

    [MessagePackObject]
    public class MsgPack_S_NodeLeave_Ntf : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_NODELEAVE_NTF;
		}

        [Key(2)]
        public uint NodeId { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_NodeReady_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_NODEREADY_PROTO;
		}

        [Key(2)]
        public uint NodeId { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_RegisterMailBox_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_REGISTERMAILBOX_REQ;
		}

        [Key(2)]
        public string MailboxName { get; set; }

        [Key(3)]
        public uint NodeId { get; set; }

        [Key(4)]
        public bool IsOverride { get; set; }

        [Key(5)]
        public bool IsBroadcastRegister { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_RegisterMailBox_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_REGISTERMAILBOX_ACK;
		}

        [Key(2)]
        public string MailboxName { get; set; }

        [Key(3)]
        public int ErrorCode { get; set; }

        [Key(4)]
        public uint RouterNodeId { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_RpcByMailBox_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_RPCBYMAILBOX_PROTO;
		}

        [Key(2)]
        public string MailboxName { get; set; }

        [Key(3)]
        public MsgPack_S_Rpc_Req RpcReq = new MsgPack_S_Rpc_Req();

    }

    [MessagePackObject]
    public class MsgPack_S_Rpc_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_RPC_REQ;
		}

        [Key(2)]
        public long ReqId { get; set; }

        [Key(3)]
        public int Flag { get; set; }

        [Key(4)]
        public uint SrcNode { get; set; }

        [Key(5)]
        public uint HiddenSrcNode { get; set; }

        [Key(6)]
        public string Uri { get; set; }

        [Key(7)]
        public string Func { get; set; }

        [Key(8)]
        public int MsgProtoCode { get; set; }

        [Key(9)]
        public byte[] Data { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_Rpc_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_RPC_ACK;
		}

        [Key(2)]
        public long ReqId { get; set; }

        [Key(3)]
        public int Flag { get; set; }

        [Key(4)]
        public int ErrorCode { get; set; }

        [Key(5)]
        public uint ReqNode { get; set; }

        [Key(6)]
        public uint HiddenReqNode { get; set; }

        [Key(7)]
        public uint RespNodeId { get; set; }

        [Key(8)]
        public int MsgProtoCode { get; set; }

        [Key(9)]
        public byte[] RespData { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_RpcToNode_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_RPCTONODE_PROTO;
		}

        [Key(2)]
        public uint DestNodeId { get; set; }

        [Key(3)]
        public MsgPack_S_Rpc_Req RpcReq = new MsgPack_S_Rpc_Req();

    }

    [MessagePackObject]
    public class MsgPack_S_RpcToPlayer_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_RPCTOPLAYER_PROTO;
		}

        [Key(2)]
        public bool IsSpacePlayer { get; set; }

        [Key(3)]
        public long Uid { get; set; }

        [Key(4)]
        public MsgPack_S_Rpc_Req RpcReq = new MsgPack_S_Rpc_Req();

    }

    [MessagePackObject]
    public class MsgPack_S_RpcToPlayerRouter_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_RPCTOPLAYERROUTER_PROTO;
		}

        [Key(2)]
        public bool IsSpacePlayer { get; set; }

        [Key(3)]
        public long Uid { get; set; }

        [Key(4)]
        public MsgPack_S_Rpc_Req RpcReq = new MsgPack_S_Rpc_Req();

    }

    [MessagePackObject]
    public class MsgPack_S_ServerNodeInfo_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_SERVERNODEINFO_PROTO;
		}

        [Key(2)]
        public uint NodeId { get; set; }

        [Key(3)]
        public string Role { get; set; }

        [Key(4)]
        public string Ip { get; set; }

        [Key(5)]
        public uint Port { get; set; }

        [Key(6)]
        public uint RealmId { get; set; }

        [Key(7)]
        public bool IsReady { get; set; }

        [Key(8)]
        public byte[] ExtraData { get; set; }

        [Key(9)]
        public string Tag { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_TransUpdateConsistentHash_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_TRANSUPDATECONSISTENTHASH_REQ;
		}

        [Key(2)]
        public long TransId { get; set; }

        [Key(3)]
        public byte TransState { get; set; }

        [Key(4)]
        public string Role { get; set; }

        [Key(5)]
        public byte[] ConsistentHashData { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_TransUpdateConsistentHash_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_TRANSUPDATECONSISTENTHASH_ACK;
		}

        [Key(2)]
        public long TransId { get; set; }

        [Key(3)]
        public byte TransState { get; set; }

        [Key(4)]
        public string Role { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_UnregisterMailBox_Req : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_UNREGISTERMAILBOX_REQ;
		}

        [Key(2)]
        public string MailboxName { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_UnregisterMailBox_Ack : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_UNREGISTERMAILBOX_ACK;
		}

        [Key(2)]
        public string MailboxName { get; set; }

        [Key(3)]
        public int ErrorCode { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_UpdateConsistentHash_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_UPDATECONSISTENTHASH_PROTO;
		}

        [Key(2)]
        public string Role { get; set; }

        [Key(3)]
        public byte[] ConsistentHashData { get; set; }

    }

    [MessagePackObject]
    public class MsgPack_S_BroadcastData_Proto : MsgPackStructBase
    {
        public override void Init()
		{
			ProtoCode = EProtoCode.CORE_S_BROADCASTDATA_PROTO;
		}

        [Key(2)]
        public string Key { get; set; }

        [Key(3)]
        public byte[] Data { get; set; }

    }

}