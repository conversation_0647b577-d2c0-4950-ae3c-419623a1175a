// Generated by Tools.  DO NOT EDIT!
using MessagePack;
using System;
using System.Collections.Generic;
using System.IO;

namespace Phoenix.MsgPackLogic.Protocol
{
    public static class MsgPackProtoHelper
    {
        static MsgPackProtoHelper()
        {
            # if PHOENIX_SERVER || PHOENIX_MINICLIENT

            var resolver = MessagePack.Resolvers.CompositeResolver.Create(
                MessagePack.Resolvers.NativeDateTimeResolver.Instance,
                MessagePack.Resolvers.StandardResolver.Instance
            );
            MessagePackSerializer.DefaultOptions = MessagePackSerializerOptions.Standard.WithResolver(resolver);
            #endif

           Add(1, typeof(MsgPack_Example_Req), true, true);
           Add(2, typeof(MsgPack_Example_Ack), true, false);
           Add(3, typeof(MsgPack_ExampleData_Ntf), true, false);
           Add(4, typeof(MsgPack_ExampleExternal_Req), true, true);
           Add(5, typeof(MsgPack_ExampleExternal_Ack), true, false);
           Add(51, typeof(MsgPack_Test_Req), true, false);
           Add(52, typeof(MsgPack_Test_Ack), true, false);
           Add(53, typeof(MsgPack_Echo_Req), true, true);
           Add(54, typeof(MsgPack_Echo_Ack), true, false);
           Add(201, typeof(MsgPack_S_CheckConsistentDigest_Proto), false, false);
           Add(202, typeof(MsgPack_S_EngineRpc_Proto), false, false);
           Add(203, typeof(MsgPack_S_RpcConnect_Proto), false, false);
           Add(204, typeof(MsgPack_S_RpcEstablish_Proto), false, false);
           Add(205, typeof(MsgPack_S_GetConsistentDigest_Proto), false, false);
           Add(206, typeof(MsgPack_S_NodesReady_Proto), false, false);
           Add(207, typeof(MsgPack_S_NodeHeartbeat_Req), false, false);
           Add(208, typeof(MsgPack_S_NodeHeartbeat_Ack), false, false);
           Add(209, typeof(MsgPack_S_NodeInfoList_Proto), false, false);
           Add(210, typeof(MsgPack_S_NodeJoin_Ntf), false, false);
           Add(211, typeof(MsgPack_S_NodeLeave_Ntf), false, false);
           Add(212, typeof(MsgPack_S_NodeReady_Proto), false, false);
           Add(213, typeof(MsgPack_S_RegisterMailBox_Req), false, false);
           Add(214, typeof(MsgPack_S_RegisterMailBox_Ack), false, false);
           Add(215, typeof(MsgPack_S_RpcByMailBox_Proto), false, false);
           Add(216, typeof(MsgPack_S_Rpc_Req), false, false);
           Add(217, typeof(MsgPack_S_Rpc_Ack), false, false);
           Add(218, typeof(MsgPack_S_RpcToNode_Proto), false, false);
           Add(219, typeof(MsgPack_S_RpcToPlayer_Proto), false, false);
           Add(220, typeof(MsgPack_S_RpcToPlayerRouter_Proto), false, false);
           Add(221, typeof(MsgPack_S_ServerNodeInfo_Proto), false, false);
           Add(222, typeof(MsgPack_S_TransUpdateConsistentHash_Req), false, false);
           Add(223, typeof(MsgPack_S_TransUpdateConsistentHash_Ack), false, false);
           Add(224, typeof(MsgPack_S_UnregisterMailBox_Req), false, false);
           Add(225, typeof(MsgPack_S_UnregisterMailBox_Ack), false, false);
           Add(226, typeof(MsgPack_S_UpdateConsistentHash_Proto), false, false);
           Add(227, typeof(MsgPack_S_BroadcastData_Proto), false, false);
           Add(401, typeof(MsgPack_C_CallBasePlayerMethod_Proto), false, false);
           Add(402, typeof(MsgPack_C_CallEntityMethod_Proto), false, false);
           Add(403, typeof(MsgPack_S_BasicEntityCreate_Proto), false, false);
           Add(404, typeof(MsgPack_S_CallClientMethod_Proto), false, false);
           Add(405, typeof(MsgPack_S_EntityCreawte_Proto), false, false);
           Add(406, typeof(MsgPack_S_EntityDestroy_Proto), false, false);
           Add(407, typeof(MsgPack_S_EntityMigrate_Proto), false, false);
           Add(408, typeof(MsgPack_S_EntityMigrateReply_Proto), false, false);
           Add(409, typeof(MsgPack_S_ForwardEntityRpc_Proto), false, false);
           Add(603, typeof(MsgPack_S_ForwardClientMsgBroadcast_Proto), false, false);
           Add(604, typeof(ForwardClientMessageToServerNtf), false, false);
           Add(605, typeof(ForwardMessageToClientNtf), false, false);
           Add(806, typeof(MsgPack_Logout_Req), true, false);
           Add(807, typeof(LoginByAuthTokenReq), true, true);
           Add(808, typeof(LoginByAuthTokenAck), true, false);
           Add(809, typeof(LoginBySessionTokenReq), true, true);
           Add(810, typeof(LoginBySessionTokenAck), true, false);
           Add(811, typeof(ServerDisconnectNtf), true, false);
           Add(1007, typeof(PlayerBasicCompDataNtf), true, false);
           Add(1008, typeof(PlayerDataSectionSyncEndNtf), true, false);
           Add(1009, typeof(PlayerInfoInitReq), true, true);
           Add(1010, typeof(PlayerInfoInitAck), true, false);
           Add(1011, typeof(CharacterCreateReq), true, true);
           Add(1012, typeof(CharacterCreateAck), true, false);
           Add(2002, typeof(PlayerWorldCompDataNtf), true, false);
           Add(3001, typeof(PlayerHakoniwaCompDataNtf), true, false);
           Add(3002, typeof(HakoniwaEnterReq), true, true);
           Add(3003, typeof(HakoniwaEnterAck), true, false);
           Add(3006, typeof(HakoniwaGiveUpReq), true, true);
           Add(3007, typeof(HakoniwaGiveUpAck), true, false);
           Add(3008, typeof(HakoniwaCreateReq), true, true);
           Add(3009, typeof(HakoniwaCreateAck), true, false);
           Add(4001, typeof(MsgPack_BattleVerify_Req), true, true);
           Add(4002, typeof(MsgPack_BattleVerify_Ack), true, true);
           Add(5001, typeof(MsgPack_Duel_Req), true, true);
           Add(5002, typeof(MsgPack_Duel_Ack), true, false);
           Add(5003, typeof(MsgPack_Duel_Ntf), true, false);
           Add(5004, typeof(MsgPack_DuelReply_Req), true, true);
           Add(5005, typeof(MsgPack_DuelReply_Ack), true, false);
           Add(5006, typeof(MsgPack_DuelReply_Ntf), true, false);
           Add(5007, typeof(MsgPack_DuelBattleSessionInit_Ntf), true, false);
           Add(5008, typeof(MsgPack_DuelBattleSessionEnd_Ntf), true, false);
           Add(5009, typeof(MsgPack_S_DuelReply_Ntf), true, false);
           Add(5010, typeof(MsgPack_S_Duel_Ntf), true, false);
           Add(6001, typeof(MsgPack_BattleCommand_Req), true, true);
           Add(6002, typeof(MsgPack_BattleCommand_Ack), true, false);
           Add(6003, typeof(MsgPack_BattleCommand_Ntf), true, false);
           Add(7001, typeof(MsgPack_BagInfo_Req), true, true);
           Add(7002, typeof(MsgPack_BagInfo_Ack), true, false);
           Add(65002, typeof(MsgPack_ServerTime_Req), true, true);
           Add(65003, typeof(MsgPack_ServerTime_Ack), true, false);
           Add(65004, typeof(MsgPack_HeartBeat_Req), true, true);
           Add(65005, typeof(MsgPack_HeartBeat_Ack), true, false);
        }

        private static void Add(int protoId, Type type, bool isClient, bool isFromClient)
        {
           s_type2ProtoId.Add(type, protoId);
           s_protoId2Type.Add(protoId, type);
           if (isClient)
           {
                s_clientProtoId2Type.Add(protoId, type);
           }
           if (isFromClient)
           {
                s_protoIdsFromClient.Add(protoId);
           }
        }

        public static MsgPackStructBase? Deserialize(int protoId, ReadOnlyMemory<byte> memory)
        {
            switch (protoId)
            {
                case 1:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Example_Req>(memory);
                }
                case 2:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Example_Ack>(memory);
                }
                case 3:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ExampleData_Ntf>(memory);
                }
                case 4:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ExampleExternal_Req>(memory);
                }
                case 5:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ExampleExternal_Ack>(memory);
                }
                case 51:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Test_Req>(memory);
                }
                case 52:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Test_Ack>(memory);
                }
                case 53:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Echo_Req>(memory);
                }
                case 54:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Echo_Ack>(memory);
                }
                case 201:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_CheckConsistentDigest_Proto>(memory);
                }
                case 202:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EngineRpc_Proto>(memory);
                }
                case 203:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcConnect_Proto>(memory);
                }
                case 204:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcEstablish_Proto>(memory);
                }
                case 205:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_GetConsistentDigest_Proto>(memory);
                }
                case 206:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_NodesReady_Proto>(memory);
                }
                case 207:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_NodeHeartbeat_Req>(memory);
                }
                case 208:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_NodeHeartbeat_Ack>(memory);
                }
                case 209:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_NodeInfoList_Proto>(memory);
                }
                case 210:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_NodeJoin_Ntf>(memory);
                }
                case 211:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_NodeLeave_Ntf>(memory);
                }
                case 212:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_NodeReady_Proto>(memory);
                }
                case 213:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RegisterMailBox_Req>(memory);
                }
                case 214:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RegisterMailBox_Ack>(memory);
                }
                case 215:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcByMailBox_Proto>(memory);
                }
                case 216:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_Rpc_Req>(memory);
                }
                case 217:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_Rpc_Ack>(memory);
                }
                case 218:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcToNode_Proto>(memory);
                }
                case 219:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcToPlayer_Proto>(memory);
                }
                case 220:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcToPlayerRouter_Proto>(memory);
                }
                case 221:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_ServerNodeInfo_Proto>(memory);
                }
                case 222:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_TransUpdateConsistentHash_Req>(memory);
                }
                case 223:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_TransUpdateConsistentHash_Ack>(memory);
                }
                case 224:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_UnregisterMailBox_Req>(memory);
                }
                case 225:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_UnregisterMailBox_Ack>(memory);
                }
                case 226:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_UpdateConsistentHash_Proto>(memory);
                }
                case 227:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_BroadcastData_Proto>(memory);
                }
                case 401:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_C_CallBasePlayerMethod_Proto>(memory);
                }
                case 402:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_C_CallEntityMethod_Proto>(memory);
                }
                case 403:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_BasicEntityCreate_Proto>(memory);
                }
                case 404:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_CallClientMethod_Proto>(memory);
                }
                case 405:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityCreawte_Proto>(memory);
                }
                case 406:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityDestroy_Proto>(memory);
                }
                case 407:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityMigrate_Proto>(memory);
                }
                case 408:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityMigrateReply_Proto>(memory);
                }
                case 409:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_ForwardEntityRpc_Proto>(memory);
                }
                case 603:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_ForwardClientMsgBroadcast_Proto>(memory);
                }
                case 604:
                {
                    return MessagePackSerializer.Deserialize<ForwardClientMessageToServerNtf>(memory);
                }
                case 605:
                {
                    return MessagePackSerializer.Deserialize<ForwardMessageToClientNtf>(memory);
                }
                case 806:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Logout_Req>(memory);
                }
                case 807:
                {
                    return MessagePackSerializer.Deserialize<LoginByAuthTokenReq>(memory);
                }
                case 808:
                {
                    return MessagePackSerializer.Deserialize<LoginByAuthTokenAck>(memory);
                }
                case 809:
                {
                    return MessagePackSerializer.Deserialize<LoginBySessionTokenReq>(memory);
                }
                case 810:
                {
                    return MessagePackSerializer.Deserialize<LoginBySessionTokenAck>(memory);
                }
                case 811:
                {
                    return MessagePackSerializer.Deserialize<ServerDisconnectNtf>(memory);
                }
                case 1007:
                {
                    return MessagePackSerializer.Deserialize<PlayerBasicCompDataNtf>(memory);
                }
                case 1008:
                {
                    return PlayerDataSectionSyncEndNtf.Shared;
                }
                case 1009:
                {
                    return PlayerInfoInitReq.Shared;
                }
                case 1010:
                {
                    return MessagePackSerializer.Deserialize<PlayerInfoInitAck>(memory);
                }
                case 1011:
                {
                    return MessagePackSerializer.Deserialize<CharacterCreateReq>(memory);
                }
                case 1012:
                {
                    return MessagePackSerializer.Deserialize<CharacterCreateAck>(memory);
                }
                case 2002:
                {
                    return MessagePackSerializer.Deserialize<PlayerWorldCompDataNtf>(memory);
                }
                case 3001:
                {
                    return MessagePackSerializer.Deserialize<PlayerHakoniwaCompDataNtf>(memory);
                }
                case 3002:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaEnterReq>(memory);
                }
                case 3003:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaEnterAck>(memory);
                }
                case 3006:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaGiveUpReq>(memory);
                }
                case 3007:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaGiveUpAck>(memory);
                }
                case 3008:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaCreateReq>(memory);
                }
                case 3009:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaCreateAck>(memory);
                }
                case 4001:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleVerify_Req>(memory);
                }
                case 4002:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleVerify_Ack>(memory);
                }
                case 5001:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Duel_Req>(memory);
                }
                case 5002:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Duel_Ack>(memory);
                }
                case 5003:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Duel_Ntf>(memory);
                }
                case 5004:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelReply_Req>(memory);
                }
                case 5005:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelReply_Ack>(memory);
                }
                case 5006:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelReply_Ntf>(memory);
                }
                case 5007:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelBattleSessionInit_Ntf>(memory);
                }
                case 5008:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelBattleSessionEnd_Ntf>(memory);
                }
                case 5009:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_DuelReply_Ntf>(memory);
                }
                case 5010:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_Duel_Ntf>(memory);
                }
                case 6001:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleCommand_Req>(memory);
                }
                case 6002:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleCommand_Ack>(memory);
                }
                case 6003:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleCommand_Ntf>(memory);
                }
                case 7001:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BagInfo_Req>(memory);
                }
                case 7002:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BagInfo_Ack>(memory);
                }
                case 65002:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ServerTime_Req>(memory);
                }
                case 65003:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ServerTime_Ack>(memory);
                }
                case 65004:
                {
                    return MsgPack_HeartBeat_Req.Shared;
                }
                case 65005:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_HeartBeat_Ack>(memory);
                }
                default:
                {
                    return null;
                }
            }
        }

        public static MsgPackStructBase? Deserialize(int protoId, byte[] data)
        {
            switch (protoId)
            {
                case 1:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Example_Req>(data);
                }
                case 2:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Example_Ack>(data);
                }
                case 3:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ExampleData_Ntf>(data);
                }
                case 4:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ExampleExternal_Req>(data);
                }
                case 5:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ExampleExternal_Ack>(data);
                }
                case 51:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Test_Req>(data);
                }
                case 52:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Test_Ack>(data);
                }
                case 53:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Echo_Req>(data);
                }
                case 54:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Echo_Ack>(data);
                }
                case 201:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_CheckConsistentDigest_Proto>(data);
                }
                case 202:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EngineRpc_Proto>(data);
                }
                case 203:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcConnect_Proto>(data);
                }
                case 204:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcEstablish_Proto>(data);
                }
                case 205:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_GetConsistentDigest_Proto>(data);
                }
                case 206:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_NodesReady_Proto>(data);
                }
                case 207:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_NodeHeartbeat_Req>(data);
                }
                case 208:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_NodeHeartbeat_Ack>(data);
                }
                case 209:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_NodeInfoList_Proto>(data);
                }
                case 210:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_NodeJoin_Ntf>(data);
                }
                case 211:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_NodeLeave_Ntf>(data);
                }
                case 212:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_NodeReady_Proto>(data);
                }
                case 213:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RegisterMailBox_Req>(data);
                }
                case 214:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RegisterMailBox_Ack>(data);
                }
                case 215:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcByMailBox_Proto>(data);
                }
                case 216:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_Rpc_Req>(data);
                }
                case 217:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_Rpc_Ack>(data);
                }
                case 218:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcToNode_Proto>(data);
                }
                case 219:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcToPlayer_Proto>(data);
                }
                case 220:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_RpcToPlayerRouter_Proto>(data);
                }
                case 221:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_ServerNodeInfo_Proto>(data);
                }
                case 222:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_TransUpdateConsistentHash_Req>(data);
                }
                case 223:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_TransUpdateConsistentHash_Ack>(data);
                }
                case 224:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_UnregisterMailBox_Req>(data);
                }
                case 225:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_UnregisterMailBox_Ack>(data);
                }
                case 226:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_UpdateConsistentHash_Proto>(data);
                }
                case 227:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_BroadcastData_Proto>(data);
                }
                case 401:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_C_CallBasePlayerMethod_Proto>(data);
                }
                case 402:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_C_CallEntityMethod_Proto>(data);
                }
                case 403:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_BasicEntityCreate_Proto>(data);
                }
                case 404:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_CallClientMethod_Proto>(data);
                }
                case 405:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityCreawte_Proto>(data);
                }
                case 406:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityDestroy_Proto>(data);
                }
                case 407:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityMigrate_Proto>(data);
                }
                case 408:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_EntityMigrateReply_Proto>(data);
                }
                case 409:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_ForwardEntityRpc_Proto>(data);
                }
                case 603:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_ForwardClientMsgBroadcast_Proto>(data);
                }
                case 604:
                {
                    return MessagePackSerializer.Deserialize<ForwardClientMessageToServerNtf>(data);
                }
                case 605:
                {
                    return MessagePackSerializer.Deserialize<ForwardMessageToClientNtf>(data);
                }
                case 806:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Logout_Req>(data);
                }
                case 807:
                {
                    return MessagePackSerializer.Deserialize<LoginByAuthTokenReq>(data);
                }
                case 808:
                {
                    return MessagePackSerializer.Deserialize<LoginByAuthTokenAck>(data);
                }
                case 809:
                {
                    return MessagePackSerializer.Deserialize<LoginBySessionTokenReq>(data);
                }
                case 810:
                {
                    return MessagePackSerializer.Deserialize<LoginBySessionTokenAck>(data);
                }
                case 811:
                {
                    return MessagePackSerializer.Deserialize<ServerDisconnectNtf>(data);
                }
                case 1007:
                {
                    return MessagePackSerializer.Deserialize<PlayerBasicCompDataNtf>(data);
                }
                case 1008:
                {
                    return MessagePackSerializer.Deserialize<PlayerDataSectionSyncEndNtf>(data);
                }
                case 1009:
                {
                    return MessagePackSerializer.Deserialize<PlayerInfoInitReq>(data);
                }
                case 1010:
                {
                    return MessagePackSerializer.Deserialize<PlayerInfoInitAck>(data);
                }
                case 1011:
                {
                    return MessagePackSerializer.Deserialize<CharacterCreateReq>(data);
                }
                case 1012:
                {
                    return MessagePackSerializer.Deserialize<CharacterCreateAck>(data);
                }
                case 2002:
                {
                    return MessagePackSerializer.Deserialize<PlayerWorldCompDataNtf>(data);
                }
                case 3001:
                {
                    return MessagePackSerializer.Deserialize<PlayerHakoniwaCompDataNtf>(data);
                }
                case 3002:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaEnterReq>(data);
                }
                case 3003:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaEnterAck>(data);
                }
                case 3006:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaGiveUpReq>(data);
                }
                case 3007:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaGiveUpAck>(data);
                }
                case 3008:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaCreateReq>(data);
                }
                case 3009:
                {
                    return MessagePackSerializer.Deserialize<HakoniwaCreateAck>(data);
                }
                case 4001:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleVerify_Req>(data);
                }
                case 4002:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleVerify_Ack>(data);
                }
                case 5001:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Duel_Req>(data);
                }
                case 5002:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Duel_Ack>(data);
                }
                case 5003:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_Duel_Ntf>(data);
                }
                case 5004:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelReply_Req>(data);
                }
                case 5005:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelReply_Ack>(data);
                }
                case 5006:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelReply_Ntf>(data);
                }
                case 5007:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelBattleSessionInit_Ntf>(data);
                }
                case 5008:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_DuelBattleSessionEnd_Ntf>(data);
                }
                case 5009:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_DuelReply_Ntf>(data);
                }
                case 5010:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_S_Duel_Ntf>(data);
                }
                case 6001:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleCommand_Req>(data);
                }
                case 6002:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleCommand_Ack>(data);
                }
                case 6003:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BattleCommand_Ntf>(data);
                }
                case 7001:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BagInfo_Req>(data);
                }
                case 7002:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_BagInfo_Ack>(data);
                }
                case 65002:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ServerTime_Req>(data);
                }
                case 65003:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_ServerTime_Ack>(data);
                }
                case 65004:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_HeartBeat_Req>(data);
                }
                case 65005:
                {
                    return MessagePackSerializer.Deserialize<MsgPack_HeartBeat_Ack>(data);
                }
                default:
                {
                    return null;
                }
            }
        }

        public static void Serialize(Stream stream, MsgPackStructBase data)
        {
            switch ((int)data.ProtoCode)
            {
                case 1:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Example_Req);
                }
                break;
                case 2:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Example_Ack);
                }
                break;
                case 3:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_ExampleData_Ntf);
                }
                break;
                case 4:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_ExampleExternal_Req);
                }
                break;
                case 5:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_ExampleExternal_Ack);
                }
                break;
                case 51:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Test_Req);
                }
                break;
                case 52:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Test_Ack);
                }
                break;
                case 53:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Echo_Req);
                }
                break;
                case 54:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Echo_Ack);
                }
                break;
                case 201:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_CheckConsistentDigest_Proto);
                }
                break;
                case 202:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_EngineRpc_Proto);
                }
                break;
                case 203:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_RpcConnect_Proto);
                }
                break;
                case 204:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_RpcEstablish_Proto);
                }
                break;
                case 205:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_GetConsistentDigest_Proto);
                }
                break;
                case 206:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_NodesReady_Proto);
                }
                break;
                case 207:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_NodeHeartbeat_Req);
                }
                break;
                case 208:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_NodeHeartbeat_Ack);
                }
                break;
                case 209:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_NodeInfoList_Proto);
                }
                break;
                case 210:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_NodeJoin_Ntf);
                }
                break;
                case 211:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_NodeLeave_Ntf);
                }
                break;
                case 212:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_NodeReady_Proto);
                }
                break;
                case 213:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_RegisterMailBox_Req);
                }
                break;
                case 214:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_RegisterMailBox_Ack);
                }
                break;
                case 215:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_RpcByMailBox_Proto);
                }
                break;
                case 216:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_Rpc_Req);
                }
                break;
                case 217:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_Rpc_Ack);
                }
                break;
                case 218:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_RpcToNode_Proto);
                }
                break;
                case 219:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_RpcToPlayer_Proto);
                }
                break;
                case 220:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_RpcToPlayerRouter_Proto);
                }
                break;
                case 221:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_ServerNodeInfo_Proto);
                }
                break;
                case 222:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_TransUpdateConsistentHash_Req);
                }
                break;
                case 223:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_TransUpdateConsistentHash_Ack);
                }
                break;
                case 224:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_UnregisterMailBox_Req);
                }
                break;
                case 225:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_UnregisterMailBox_Ack);
                }
                break;
                case 226:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_UpdateConsistentHash_Proto);
                }
                break;
                case 227:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_BroadcastData_Proto);
                }
                break;
                case 401:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_C_CallBasePlayerMethod_Proto);
                }
                break;
                case 402:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_C_CallEntityMethod_Proto);
                }
                break;
                case 403:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_BasicEntityCreate_Proto);
                }
                break;
                case 404:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_CallClientMethod_Proto);
                }
                break;
                case 405:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_EntityCreawte_Proto);
                }
                break;
                case 406:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_EntityDestroy_Proto);
                }
                break;
                case 407:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_EntityMigrate_Proto);
                }
                break;
                case 408:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_EntityMigrateReply_Proto);
                }
                break;
                case 409:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_ForwardEntityRpc_Proto);
                }
                break;
                case 603:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_ForwardClientMsgBroadcast_Proto);
                }
                break;
                case 604:
                {
                    MessagePackSerializer.Serialize(stream, data as ForwardClientMessageToServerNtf);
                }
                break;
                case 605:
                {
                    MessagePackSerializer.Serialize(stream, data as ForwardMessageToClientNtf);
                }
                break;
                case 806:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Logout_Req);
                }
                break;
                case 807:
                {
                    MessagePackSerializer.Serialize(stream, data as LoginByAuthTokenReq);
                }
                break;
                case 808:
                {
                    MessagePackSerializer.Serialize(stream, data as LoginByAuthTokenAck);
                }
                break;
                case 809:
                {
                    MessagePackSerializer.Serialize(stream, data as LoginBySessionTokenReq);
                }
                break;
                case 810:
                {
                    MessagePackSerializer.Serialize(stream, data as LoginBySessionTokenAck);
                }
                break;
                case 811:
                {
                    MessagePackSerializer.Serialize(stream, data as ServerDisconnectNtf);
                }
                break;
                case 1007:
                {
                    MessagePackSerializer.Serialize(stream, data as PlayerBasicCompDataNtf);
                }
                break;
                case 1008:
                {
                    MessagePackSerializer.Serialize(stream, data as PlayerDataSectionSyncEndNtf);
                }
                break;
                case 1009:
                {
                    MessagePackSerializer.Serialize(stream, data as PlayerInfoInitReq);
                }
                break;
                case 1010:
                {
                    MessagePackSerializer.Serialize(stream, data as PlayerInfoInitAck);
                }
                break;
                case 1011:
                {
                    MessagePackSerializer.Serialize(stream, data as CharacterCreateReq);
                }
                break;
                case 1012:
                {
                    MessagePackSerializer.Serialize(stream, data as CharacterCreateAck);
                }
                break;
                case 2002:
                {
                    MessagePackSerializer.Serialize(stream, data as PlayerWorldCompDataNtf);
                }
                break;
                case 3001:
                {
                    MessagePackSerializer.Serialize(stream, data as PlayerHakoniwaCompDataNtf);
                }
                break;
                case 3002:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaEnterReq);
                }
                break;
                case 3003:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaEnterAck);
                }
                break;
                case 3006:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaGiveUpReq);
                }
                break;
                case 3007:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaGiveUpAck);
                }
                break;
                case 3008:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaCreateReq);
                }
                break;
                case 3009:
                {
                    MessagePackSerializer.Serialize(stream, data as HakoniwaCreateAck);
                }
                break;
                case 4001:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_BattleVerify_Req);
                }
                break;
                case 4002:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_BattleVerify_Ack);
                }
                break;
                case 5001:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Duel_Req);
                }
                break;
                case 5002:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Duel_Ack);
                }
                break;
                case 5003:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_Duel_Ntf);
                }
                break;
                case 5004:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_DuelReply_Req);
                }
                break;
                case 5005:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_DuelReply_Ack);
                }
                break;
                case 5006:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_DuelReply_Ntf);
                }
                break;
                case 5007:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_DuelBattleSessionInit_Ntf);
                }
                break;
                case 5008:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_DuelBattleSessionEnd_Ntf);
                }
                break;
                case 5009:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_DuelReply_Ntf);
                }
                break;
                case 5010:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_S_Duel_Ntf);
                }
                break;
                case 6001:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_BattleCommand_Req);
                }
                break;
                case 6002:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_BattleCommand_Ack);
                }
                break;
                case 6003:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_BattleCommand_Ntf);
                }
                break;
                case 7001:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_BagInfo_Req);
                }
                break;
                case 7002:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_BagInfo_Ack);
                }
                break;
                case 65002:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_ServerTime_Req);
                }
                break;
                case 65003:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_ServerTime_Ack);
                }
                break;
                case 65004:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_HeartBeat_Req);
                }
                break;
                case 65005:
                {
                    MessagePackSerializer.Serialize(stream, data as MsgPack_HeartBeat_Ack);
                }
                break;
                default:
                {
                    return;
                }
            }
        }

        public static byte[]? Serialize(MsgPackStructBase data)
        {
            switch ((int)data.ProtoCode)
            {
                case 1:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Example_Req);
                }
                case 2:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Example_Ack);
                }
                case 3:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_ExampleData_Ntf);
                }
                case 4:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_ExampleExternal_Req);
                }
                case 5:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_ExampleExternal_Ack);
                }
                case 51:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Test_Req);
                }
                case 52:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Test_Ack);
                }
                case 53:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Echo_Req);
                }
                case 54:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Echo_Ack);
                }
                case 201:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_CheckConsistentDigest_Proto);
                }
                case 202:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_EngineRpc_Proto);
                }
                case 203:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_RpcConnect_Proto);
                }
                case 204:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_RpcEstablish_Proto);
                }
                case 205:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_GetConsistentDigest_Proto);
                }
                case 206:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_NodesReady_Proto);
                }
                case 207:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_NodeHeartbeat_Req);
                }
                case 208:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_NodeHeartbeat_Ack);
                }
                case 209:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_NodeInfoList_Proto);
                }
                case 210:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_NodeJoin_Ntf);
                }
                case 211:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_NodeLeave_Ntf);
                }
                case 212:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_NodeReady_Proto);
                }
                case 213:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_RegisterMailBox_Req);
                }
                case 214:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_RegisterMailBox_Ack);
                }
                case 215:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_RpcByMailBox_Proto);
                }
                case 216:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_Rpc_Req);
                }
                case 217:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_Rpc_Ack);
                }
                case 218:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_RpcToNode_Proto);
                }
                case 219:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_RpcToPlayer_Proto);
                }
                case 220:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_RpcToPlayerRouter_Proto);
                }
                case 221:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_ServerNodeInfo_Proto);
                }
                case 222:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_TransUpdateConsistentHash_Req);
                }
                case 223:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_TransUpdateConsistentHash_Ack);
                }
                case 224:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_UnregisterMailBox_Req);
                }
                case 225:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_UnregisterMailBox_Ack);
                }
                case 226:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_UpdateConsistentHash_Proto);
                }
                case 227:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_BroadcastData_Proto);
                }
                case 401:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_C_CallBasePlayerMethod_Proto);
                }
                case 402:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_C_CallEntityMethod_Proto);
                }
                case 403:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_BasicEntityCreate_Proto);
                }
                case 404:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_CallClientMethod_Proto);
                }
                case 405:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_EntityCreawte_Proto);
                }
                case 406:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_EntityDestroy_Proto);
                }
                case 407:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_EntityMigrate_Proto);
                }
                case 408:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_EntityMigrateReply_Proto);
                }
                case 409:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_ForwardEntityRpc_Proto);
                }
                case 603:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_ForwardClientMsgBroadcast_Proto);
                }
                case 604:
                {
                    return MessagePackSerializer.Serialize(data as ForwardClientMessageToServerNtf);
                }
                case 605:
                {
                    return MessagePackSerializer.Serialize(data as ForwardMessageToClientNtf);
                }
                case 806:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Logout_Req);
                }
                case 807:
                {
                    return MessagePackSerializer.Serialize(data as LoginByAuthTokenReq);
                }
                case 808:
                {
                    return MessagePackSerializer.Serialize(data as LoginByAuthTokenAck);
                }
                case 809:
                {
                    return MessagePackSerializer.Serialize(data as LoginBySessionTokenReq);
                }
                case 810:
                {
                    return MessagePackSerializer.Serialize(data as LoginBySessionTokenAck);
                }
                case 811:
                {
                    return MessagePackSerializer.Serialize(data as ServerDisconnectNtf);
                }
                case 1007:
                {
                    return MessagePackSerializer.Serialize(data as PlayerBasicCompDataNtf);
                }
                case 1008:
                {
                    return MessagePackSerializer.Serialize(data as PlayerDataSectionSyncEndNtf);
                }
                case 1009:
                {
                    return MessagePackSerializer.Serialize(data as PlayerInfoInitReq);
                }
                case 1010:
                {
                    return MessagePackSerializer.Serialize(data as PlayerInfoInitAck);
                }
                case 1011:
                {
                    return MessagePackSerializer.Serialize(data as CharacterCreateReq);
                }
                case 1012:
                {
                    return MessagePackSerializer.Serialize(data as CharacterCreateAck);
                }
                case 2002:
                {
                    return MessagePackSerializer.Serialize(data as PlayerWorldCompDataNtf);
                }
                case 3001:
                {
                    return MessagePackSerializer.Serialize(data as PlayerHakoniwaCompDataNtf);
                }
                case 3002:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaEnterReq);
                }
                case 3003:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaEnterAck);
                }
                case 3006:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaGiveUpReq);
                }
                case 3007:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaGiveUpAck);
                }
                case 3008:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaCreateReq);
                }
                case 3009:
                {
                    return MessagePackSerializer.Serialize(data as HakoniwaCreateAck);
                }
                case 4001:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_BattleVerify_Req);
                }
                case 4002:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_BattleVerify_Ack);
                }
                case 5001:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Duel_Req);
                }
                case 5002:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Duel_Ack);
                }
                case 5003:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_Duel_Ntf);
                }
                case 5004:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_DuelReply_Req);
                }
                case 5005:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_DuelReply_Ack);
                }
                case 5006:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_DuelReply_Ntf);
                }
                case 5007:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_DuelBattleSessionInit_Ntf);
                }
                case 5008:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_DuelBattleSessionEnd_Ntf);
                }
                case 5009:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_DuelReply_Ntf);
                }
                case 5010:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_S_Duel_Ntf);
                }
                case 6001:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_BattleCommand_Req);
                }
                case 6002:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_BattleCommand_Ack);
                }
                case 6003:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_BattleCommand_Ntf);
                }
                case 7001:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_BagInfo_Req);
                }
                case 7002:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_BagInfo_Ack);
                }
                case 65002:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_ServerTime_Req);
                }
                case 65003:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_ServerTime_Ack);
                }
                case 65004:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_HeartBeat_Req);
                }
                case 65005:
                {
                    return MessagePackSerializer.Serialize(data as MsgPack_HeartBeat_Ack);
                }
                default:
                {
                    return null;
                }
            }
        }

		public static Type? GetTypeByProtoId(int protoId)
		{
			return s_protoId2Type.TryGetValue(protoId, out Type? value) ? value : null;
		}

		public static int GetProtoIdByType(Type type)
		{
			return s_type2ProtoId.TryGetValue(type, out int value) ? value : 0;
		}

        public static bool IsClientProtocol(int protoId)
        {
            return s_clientProtoId2Type.ContainsKey(protoId);
        }

        public static IEnumerable<KeyValuePair<int, Type>> GetClientProtoId2Types()
        {
            return s_clientProtoId2Type;
        }

        public static IEnumerable<KeyValuePair<int, Type>> GetProtoId2Types()
        {
            return s_protoId2Type;
        }

        public static bool IsProtocolFromClient(int protoId)
        {
            return s_protoIdsFromClient.Contains(protoId);
        }

        private static readonly Dictionary<Type, int> s_type2ProtoId = new();
        private static readonly Dictionary<int, Type> s_protoId2Type = new();
        private static readonly Dictionary<int, Type> s_clientProtoId2Type = new();
        private static readonly HashSet<int> s_protoIdsFromClient = new();
     }
}
