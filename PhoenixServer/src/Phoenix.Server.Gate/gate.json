{"Server": {"ZoneId": 6666, "NodeId": 4001, "Name": "Gate_1", "Role": "gate", "Ip": "127.0.0.1", "Port": 8556, "Tags": "gate", "ServerHeartbeatInterval": 10, "ServerHeartbeatTimeout": 60}, "Client": {"Port": 6666, "IdleTime": {"Reader": 0, "Writer": 0, "All": 0}, "RsaKeyPairs": 100, "ClientHeartbeatTimeout": 60}, "Elasticsearch": {"Uri": "http://elastic:changeme@*********:9200", "DataStream": {"Type": "logs", "DataSet": "gate"}}, "DatabaseSettings": {"Game": {"ConnectionString": "***************************************", "DatabaseName": "GameDB"}}, "ActorStat": {"Enable": true, "IntervalSeconds": 60}}