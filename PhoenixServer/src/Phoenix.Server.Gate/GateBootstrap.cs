// Copyright (c) Phoenix.All Rights Reserved.

using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Common;
using Phoenix.Server.Common.Actor;
using Phoenix.Server.Utils;
using InternalLoggerFactory = Phoenix.Server.Common.InternalLoggerFactory;

namespace Phoenix.Server.Gate;

public class GateBootstrap : SingletonBase<GateBootstrap>, IHostedService
{
    private void BindDelegates()
    {
        RpcService.Instance.DelegateOnRpcMessageRequest += OnRpcMessageRequest;
        RpcService.Instance.DelegateOnRpcRequest += OnRpcRequest;
        RpcService.Instance.SetHandleServerForwardClientMsgFunc ((playerId, msg) =>
        {
            var session = GateClientSessionManager.Instance.GetSessionByPlayerId(playerId);
            if (session != null)
            {
                session.SendMessageToClient(msg!).ContinueWith((t) =>
                {
                    s_logger.LogError("SendToClientFunc Exception={Exception}", t.Exception);
                }, TaskContinuationOptions.OnlyOnFaulted);
            }
        });
        RpcService.Instance.DelegateOnNodeLeave += OnNodeLeave;
    }

    private void OnNodeLeave(uint nodeId, string role)
    {
        GateClientSessionManager.Instance.OnNodeLeave(nodeId, role);
    }

    private void OnRpcRequest(string uri, string funcName, object[] paramList, RpcRequestInfo reqInfo)
    {
        object[] args = new object[paramList.Length + 1];
        args[0] = reqInfo;
        for (int i = 0; i < paramList.Length; ++i)
        {
            args[i + 1] = paramList[i];
        }
        if (uri == RpcUriConstants.GATE_CLIENT_SESSION_MGR_URI)
        {
            var actor = ActorRegistry.Instance.GetActor("ClientSessionManager");
            if (actor != null)
            {
                actor.PostMessage(new RpcReqMessage(reqInfo.CorrelationId, reqInfo, "ClientSessionManager", funcName, args, null));
            }
        }
    }

    private void OnRpcMessageRequest(string uri, string funcName, MsgPackStructBase message, RpcRequestInfo reqInfo)
    {
        if (uri == RpcUriConstants.GATE_CLIENT_SESSION_MGR_URI)
        {
            var actor = ActorRegistry.Instance.GetActor("ClientSessionManager");
            if (actor != null)
            {
                actor.PostMessage(new RpcReqMessage(reqInfo.CorrelationId, reqInfo, "ClientSessionManager", funcName, null, message));
            }
        }
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        BindDelegates();
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    private static readonly ILogger<GateBootstrap> s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<GateBootstrap>();
}
