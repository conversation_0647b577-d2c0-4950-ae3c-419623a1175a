// Copyright (c) Phoenix.  All Rights Reserved.

using System.Runtime;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Options;
using Phoenix.Server.Common;
using Serilog;
using Serilog.Extensions.Logging;
using ILogger = Microsoft.Extensions.Logging.ILogger;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.CommonDataStructure;

// Gate server namespace
namespace Phoenix.Server.Gate;

public static class Program
{
    /// <summary>
    ///     Main entry point of the application.
    /// </summary>
    /// <param name="args">Command line arguments passed to the application.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains the exit code of the application:
    ///     0 for success, 1 for failure.
    /// </returns>
    public static async Task<int> Main(string[] args)
    {
        // Phoenix.Server.Log unhandled exceptions
        AppDomain.CurrentDomain.UnhandledException += (_, e)
            => s_logger.LogCritical((Exception)e.ExceptionObject, "Unhandled Exception");

        // Phoenix.Server.Log unobserved task exceptions
        TaskScheduler.UnobservedTaskException += (_, e) =>
        {
            s_logger.LogCritical(e.Exception, "Unobserved Task Exception");
            e.SetObserved();
        };

        try
        {
            // Start the login service
            await CreateHostBuilder(args).Build().RunAsync();
            return 0;
        }
        catch (Exception ex)
        {
            // Phoenix.Server.Log any exceptions that cause the server to terminate unexpectedly
            s_logger.LogError(ex, "server terminated unexpectedly");
            return 1;
        }
        finally
        {
            // Ensure all logs are flushed before the application exits
            await Serilog.Log.CloseAndFlushAsync();
        }
    }

    /// <summary>
    ///     Creates a host builder for the application.
    /// </summary>
    /// <param name="args">Command line arguments passed to the application.</param>
    /// <returns>A HostApplicationBuilder configured with the necessary services and configuration for the application.</returns>
    private static HostApplicationBuilder CreateHostBuilder(string[] args)
    {
        // MsgPackProtoHelper.Instance.Init();
        var settings = new HostApplicationBuilderSettings
        {
            DisableDefaults = true,
            Configuration = new ConfigurationManager()
        };

        foreach (string arg in args)
        {
            settings.Configuration.AddJsonFile(Path.Combine(AppContext.BaseDirectory, arg), optional: false);
        }

        // Create a new host builder
        var builder = Host.CreateApplicationBuilder(settings);
        builder.InitSerilog();

        // Configure options from the configuration
        builder.Services.Configure<DatabaseSettingsOptions>(
            builder.Configuration.GetSection(DatabaseSettingsOptions.StoreKeyGame));
        builder.Services.Configure<ServerOptions>(builder.Configuration.GetSection(ServerOptions.StoreKey));
        builder.Services.Configure<ClientOptions>(builder.Configuration.GetSection(ClientOptions.StoreKey));
        builder.Services.Configure<ClusterOptions>(builder.Configuration.GetSection(ClusterOptions.StoreKey));
        builder.Services.Configure<ElasticsearchOptions>(
            builder.Configuration.GetSection(ElasticsearchOptions.StoreKey));
        builder.Services.Configure<ActorStatOptions>(builder.Configuration.GetSection(ActorStatOptions.StoreKey));

        builder.Services.AddSingleton<RASInitializer>();
        builder.Services.BuildServiceProvider().GetRequiredService<RASInitializer>();

        // Register services with the DI container
        builder.Services.AddPhoenixCommonServices();
        builder.Services.AddSingleton<IUniqueIdentifierGenerator, UniqueIdentifierGenerator>();
        if (RpcService.UseConsul())
        {
            builder.Services.AddConsulLeaderDiscovery();
        }
        builder.Services.AddHostedService<RpcService>();
        builder.Services.AddHostedService<GateBootstrap>();
        builder.Services.AddSingleton<NettyForMessagePackCodecFactory>();
        builder.Services.AddSingleton<NettyHostedService>();
        builder.Services.AddHostedService(provider => provider.GetRequiredService<NettyHostedService>());
        builder.Services.AddSingleton<GateClientSessionManager>();
        builder.Services.BuildServiceProvider().GetRequiredService<GateClientSessionManager>();

        return builder;
    }

    // Logger for this class
    private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger("Main");
}
