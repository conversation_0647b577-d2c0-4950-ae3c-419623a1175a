// Copyright (c) Phoenix.  All Rights Reserved.

using DotNetty.Common.Utilities;

namespace Phoenix.Server.Gate;

/// <summary>
/// Defines the constants for keys used to store values in a Netty channel.
/// </summary>
public static class NettyChannelKeyConstants
{
    /// <summary>
    /// Key used to store the session ID.
    /// </summary>
    public static readonly AttributeKey<string> SessionId =
        AttributeKey<string>.ValueOf("SessionId");

    /// <summary>
    /// Key used to store the name of the logged-in game server.
    /// </summary>
    public static readonly AttributeKey<string> LoggedInGameServerName =
        AttributeKey<string>.ValueOf("LoggedInGameServerName");

    /// <summary>
    /// Key used to store the XORCode.
    /// </summary>
    public static readonly AttributeKey<string> XORCode =
        AttributeKey<string>.ValueOf("XORCode");
}
