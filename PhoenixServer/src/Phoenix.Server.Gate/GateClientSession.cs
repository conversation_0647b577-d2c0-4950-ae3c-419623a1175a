// Copyright (c) Phoenix.All Rights Reserved.

using System.Buffers;
using System.Diagnostics;
using DotNetty.Transport.Channels;
using Microsoft.Extensions.Logging;
using Phoenix.GameModel.Constants;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Common;
using Phoenix.Server.Common.Actor;
using Phoenix.Server.CommonDataStructure;
using InternalLoggerFactory = Phoenix.Server.Common.InternalLoggerFactory;

namespace Phoenix.Server.Gate;

public class GateClientSession : Actor<string>
{
    public GateClientSession(string sessionId, IChannelHandlerContext ctx, byte xorCode)
    {
        SessionId = sessionId;
        ChannelId = ctx.Channel.Id;
        m_channelCtx = ctx;
        XORCode = xorCode;
        TickInternal = 5000;
    }

    public override string GetActorId()
    {
        return SessionId;
    }

    protected override Task Tick()
    {
        CheckHeartBeat();
        return Task.CompletedTask;
    }

    private void CheckHeartBeat()
    {
        if (State == GateClientSessionState.CLI_LOGIN_DONE && CheckHeartBeatTime > 0)
        {
            Int64 iTimeTemp = global::System.Math.Abs(RpcService.Instance.CurrentMilliseconds - CheckHeartBeatTime);
            if (iTimeTemp > HeartBeatTimeout)
            {
                s_logger.LogInformation("CheckHeartBeat SessionId={SessionId} State={State} HeartBeatTimeout", SessionId, State);
                OnClientDisconnect();
            }
            else
            {
                s_logger.LogInformation("CheckHeartBeat SessionId={SessionId}, State={State}, HeartBeatTimeout={HeartBeatTimeout}, iTimeTemp={iTimeTemp}", SessionId, State, HeartBeatTimeout, iTimeTemp);
            }
        }
    }

    protected override Task HandleClientMessage(ClientMessage clientMessage)
    {
        // s_logger.LogInformation("GateClientSession ProcessMessage starting {Message}", clientMessage);
        switch (clientMessage.Message.ProtoCode)
        {
            case EProtoCode.LOGIN_LOGINBYAUTHTOKENREQ:
                {
                    return OnClientMessage((clientMessage.Message as LoginByAuthTokenReq)!);
                }
            case EProtoCode.LOGIN_LOGINBYSESSIONTOKENREQ:
                {
                    return OnClientMessage((clientMessage.Message as LoginBySessionTokenReq)!);
                }
            case EProtoCode.RPC_FORWARDCLIENTMESSAGETOSERVERNTF:
                {
                    return OnClientMessage((clientMessage.Message as ForwardClientMessageToServerNtf)!);
                }
            case EProtoCode.GLOBALSYNC_HEARTBEAT_REQ:
                {
                    return OnClientMessage((clientMessage.Message as MsgPack_HeartBeat_Req)!);
                }
            /*case EProtoCode.TEST_ECHO_REQ:
                {
                    var req = clientMessage.Message as MsgPack_Echo_Req;
                    return SendMessageToClient(new MsgPack_Echo_Ack {Token = req!.Token, Content = req!.Content});
                }*/
            default:
                {
                    return OnClientMessageDefault(clientMessage.Message);
                }
        }
    }


    public void OnClientDisconnect()
    {
        PostAction(async () =>
        {
            s_logger.LogInformation("OnClientDisconnect SessionId={SessionId} State={State}", SessionId, State);
            if (State == GateClientSessionState.CLI_DISCONNECT)
            {
                return;
            }

            State = GateClientSessionState.CLI_DISCONNECT;
            if (PlayerId != 0 && GameNodeId != 0)
            {
                var rpcTarget = RpcManager.CreateNodeTarget(RpcUriConstants.BASE_PLAYER_MGR_URI, GameNodeId);
                s_logger.LogInformation(
                    "OnClientDisconnect, ReqClientDisconnect Req PlayerId={PlayerId}, SessionId={SessionId}, GameNodeId={GameNodeId}, State={State}",
                    PlayerId, SessionId, GameNodeId, State);
                var resp = await RpcManager.Instance.AsyncCall(rpcTarget, "ReqClientDisconnect",
                    [PlayerId, SessionId]);
                s_logger.LogInformation(
                    "OnClientDisconnect, ReqClientDisconnect Resp PlayerId={PlayerId}, SessionId={SessionId}, GameNodeId={GameNodeId}, State={State}, RetCode={RetCode}",
                    PlayerId, SessionId, GameNodeId, State, resp.RetCode);
            }
            await Inactive();
            GateClientSessionManager.Instance.RemoveSession(SessionId);
        });
    }

    private async Task OnClientMessage(LoginByAuthTokenReq req)
    {
        s_logger.LogInformation("HandleMessageLoginByAuthTokenReq is starting");
        if (State != GateClientSessionState.CLI_INIT)
        {
            await DisconnectClientAndNotify(ServerDisconnectReasonCode.ClientStateCheckInvalid);
            return;
        }

        State = GateClientSessionState.CLI_LOGIN_BY_AUTH_TOKEN;

        // 角色已登录游戏服
        if (m_channelCtx.HasAttribute(NettyChannelKeyConstants.LoggedInGameServerName))
        {
            s_logger.LogWarning("HandleMessageLoginByAuthTokenReq has login");
            return;
        }

        LoginByAuthTokenAck ack;
        RpcTarget rpcTarget = RpcManager.CreateTarget(RpcUriConstants.LOGIN_MGR_URI, RpcNodeSelectStrategy.RANDOM_NODE);
        var resp = await RpcManager.Instance.AsyncCall(rpcTarget, "LoginByAuthTokenReq", req);
        if (resp.RetCode == RpcErrCode.RPC_NO_ERROR)
        {
            ack = (LoginByAuthTokenAck)resp.Res!;
            ack.Token = req.Token;
            await SendMessageToClient(ack);
        }
        else
        {
            s_logger.LogError("HandleMessageLoginByAuthTokenReq RpcErrCode={RpcErrCode}", resp.RetCode);
            ack = new LoginByAuthTokenAck
            {
                Token = req.Token, ErrCode = (int)ErrCode.ErrCodeInternalServerErr
            };
            await SendMessageToClient(ack);
            await DisconnectClientAndNotify(ServerDisconnectReasonCode.GracefulTermination);
        }
    }

    private async Task OnClientMessage(LoginBySessionTokenReq req)
    {
        s_logger.LogInformation("HandleMessageLoginBySessionTokenReq is starting");

        if (State != GateClientSessionState.CLI_LOGIN_BY_AUTH_TOKEN &&
            State != GateClientSessionState.CLI_INIT)
        {
            s_logger.LogError("HandleMessageLoginBySessionTokenReq is not CLI_LOGIN_BY_AUTH_TOKEN or CLI_INIT");
            await m_channelCtx.CloseAsync();
            return;
        }

        State = GateClientSessionState.CLI_LOGIN_BY_SESSION_TOKEN;

        LoginBySessionTokenAck ack = new() { Token = req.Token };
        if (SessionTokenHelper.IsValid(req.SessionToken, out var token) != ErrCode.ErrCodeOk)
        {
            ack.ErrCode = (int)ErrCode.ErrCodeLoginErrSessionTokenFormatInvalid;
            await SendMessageToClient(ack);
            await DisconnectClientAndNotify(ServerDisconnectReasonCode.ClientProtocolInvalid);
            return;
        }

        long playerId = token!.PlayerId;
        var target = RpcManager.CreateTarget(RpcUriConstants.WORLD_PLAYER_REGISTRY_URI,
            RpcNodeSelectStrategy.CONSISTENT_HASH);
        var resp = await RpcManager.Instance.AsyncCall(target, "ReqWizardPlayerLogin", [playerId, "name" + playerId],
            playerId);
        if (resp.RetCode != RpcErrCode.RPC_NO_ERROR)
        {
            ack.ErrCode = (int)ErrCode.ErrCodeLoginErrSessionTokenFormatInvalid;
            await SendMessageToClient(ack);
            await DisconnectClientAndNotify(ServerDisconnectReasonCode.ServerInternalError);
            return;
        }

        resp = await RpcManager.Instance.AsyncCall(target, "ReqPlayerLogin", [playerId, (uint)0],
            playerId);
        if (resp.RetCode == RpcErrCode.RPC_NO_ERROR)
        {
            // req game bind gate
            var res = resp.Res as Dictionary<object, object>;
            if (res != null && (int)res[RpcConstants.ERR_CODE] == 0)
            {
                string loginKey = (res["login_key"] as string)!;
                // self.login_keys[uid] = login_key;
                s_logger.LogInformation("update login key PlayerId={PlayerId}, LoginKey={LoginKey}", playerId,
                    loginKey);

                // if player is online in world_router registry
                bool relogin = (bool)res["relogin"];
                uint gameNodeId = (uint)res["game_node"];
                var rpcTarget = RpcManager.CreateNodeTarget(RpcUriConstants.BASE_PLAYER_MGR_URI, gameNodeId);
                resp = await RpcManager.Instance.AsyncCall(rpcTarget, "ReqBindGate",
                    [playerId, SessionId, RpcService.Instance.MyNode.NodeInfo.Ip, loginKey, relogin]);

                if (resp.RetCode == RpcErrCode.RPC_NO_ERROR)
                {
                    PlayerId = playerId;
                    GameNodeId = gameNodeId;

                    GateClientSession? oldSession = GateClientSessionManager.Instance.GetSessionByPlayerId(playerId);
                    if (oldSession != null)
                    {
                        await oldSession.UnbindPlayerClient();
                    }

                    GateClientSessionManager.Instance.AddSession(this);
                    State = GateClientSessionState.CLI_LOGIN_DONE;
                    ack.ErrCode = (int)ErrCode.ErrCodeOk;
                    ack.XORCode = XORCode;
                    await SendMessageToClient(ack);
                    return;
                }
            }
        }

        ack.ErrCode = (int)ErrCode.ErrCodeInternalServerErr;
        await SendMessageToClient(ack);
        await DisconnectClientAndNotify(ServerDisconnectReasonCode.ServerInternalError);
    }

    private async Task OnClientMessage(ForwardClientMessageToServerNtf clientMsg)
    {
        if (State != GateClientSessionState.CLI_LOGIN_DONE)
        {
            s_logger.LogError("OnClientMessageNormal is not login done, State={State}", State);
            await DisconnectClientAndNotify(ServerDisconnectReasonCode.ClientStateCheckInvalid);
            return;
        }

        clientMsg.FromGate = RpcService.Instance.MyNode.NodeInfo.NodeId;
        clientMsg.PlayerId = PlayerId;
        RpcService.Instance.SendMessageToNode(GameNodeId, clientMsg);
    }

    private async Task OnClientMessage(MsgPack_HeartBeat_Req req)
    {
        s_logger.LogInformation("OnClientMessageHeartBeat is starting");
        if (State != GateClientSessionState.CLI_LOGIN_DONE)
        {
            s_logger.LogError("OnClientMessageHeartBeat is not login done, State={State}", State);
            await m_channelCtx.CloseAsync();
            return;
        }

        CheckHeartBeatTime = RpcService.Instance.CurrentMilliseconds;
        await SendMessageToClient(new MsgPack_HeartBeat_Ack { CurrentServerTime = CheckHeartBeatTime });
    }



    private Task OnClientMessageDefault(MsgPackStructBase msg)
    {
        if (State != GateClientSessionState.CLI_LOGIN_DONE)
        {
            s_logger.LogError("OnClientMessageNormal is not login done, State={State}", State);
            return DisconnectClientAndNotify(ServerDisconnectReasonCode.ClientStateCheckInvalid);
        }

        byte[]? forwardMsg = MsgPackProtoHelper.Serialize(msg);
        if (forwardMsg == null)
        {
            s_logger.LogError("OnClientMessageNormal forwardMsg serialize fail, State={State}", State);
            return DisconnectClientAndNotify(ServerDisconnectReasonCode.ServerInternalError);
        }

        ForwardClientMessageToServerNtf clientMsg = new ForwardClientMessageToServerNtf();
        clientMsg.MsgProtoCode = (int)msg.ProtoCode;
        clientMsg.Data = forwardMsg;
        clientMsg.FromGate = RpcService.Instance.MyNode.NodeInfo.NodeId;
        clientMsg.PlayerId = PlayerId;
        RpcService.Instance.SendMessageToNode(GameNodeId, clientMsg);
        return Task.CompletedTask;
    }

    public Task SendMessageToClient(MsgPackStructBase msg)
    {
        if (msg.HasToken() && (msg.GetToken() == null || msg.GetToken() == 0))
        {
            s_logger.LogError("SendMessageToClient token is null ProtoCode={ProtoCode}", msg.ProtoCode);
            Debug.Assert(false, $"SendMessageToClient token is null ProtoCode={msg.ProtoCode}");
        }

        try
        {
            return m_channelCtx.WriteAndFlushAsync(msg);
        }
        catch (Exception e)
        {
            s_logger.LogInformation(e, "SendMessageToClient Exception={Exception}", e.Message);
            return Task.CompletedTask;
        }
    }

    public Task UnbindPlayerClient()
    {
        TaskCompletionSource tcs = new();
        PostAction(async () =>
        {
            s_logger.LogInformation("UnbindPlayerClient SessionId={SessionId} State={State}", SessionId, State);
            switch (State)
            {
                case GateClientSessionState.CLI_DISCONNECT:
                    return;
                //return Task.CompletedTask;
                case GateClientSessionState.CLI_INIT:
                case GateClientSessionState.CLI_LOGIN_BY_AUTH_TOKEN:
                case GateClientSessionState.CLI_LOGIN_BY_SESSION_TOKEN:
                    await DisconnectClientAndNotify(ServerDisconnectReasonCode.ServerUnbind);
                    await Inactive();
                    GateClientSessionManager.Instance.RemoveSession(SessionId);
                    break;
                case GateClientSessionState.CLI_LOGIN_DONE:
                    // TODO notify game world_router
                    await DisconnectClientAndNotify(ServerDisconnectReasonCode.ServerUnbind);
                    await Inactive();
                    GateClientSessionManager.Instance.RemoveSession(SessionId);
                    break;
                default:
                    return;
            }
            tcs.SetResult();
        });
        return tcs.Task;
    }

    private async Task DisconnectClientAndNotify(ServerDisconnectReasonCode reasonCode)
    {
        await SendMessageToClient(new ServerDisconnectNtf { ReasonCode = (int)reasonCode });
        try
        {
            await m_channelCtx.CloseAsync();
        }
        catch (Exception e)
        {
            s_logger.LogInformation("DisconnectClientAndNotify Exception={Exception}", e.Message);
        }
    }

    private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<GateClientSession>();

    public long PlayerId
    {
        get;
        private set;
    }

    public string SessionId
    {
        get;
        private set;
    }

    public IChannelId ChannelId
    {
        get;
        private set;
    }

    private IChannelHandlerContext m_channelCtx;

    private GateClientSessionState State
    {
        get;
        set;
    }

    public nodeid_t GameNodeId
    {
        get;
        private set;
    }

    public byte XORCode
    {
        get;
        private set;
    }

    public Int64 CheckHeartBeatTime
    {
        get;
        private set;
    }

    private Int32 HeartBeatTimeout
    {
        get => GateClientSessionManager.Instance.HeartbeatTimeout;
    }


}
