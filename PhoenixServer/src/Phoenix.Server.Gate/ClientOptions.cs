// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.Gate;

/// <summary>
///     Defines the configuration options for the client.
/// </summary>
/// <param name="Port">The listening port for client connections, e.g., 6666.</param>
/// <param name="IdleTime">The configuration for idle connection state.</param>
/// <param name="RasKeyPair">The configuration for RASKeyPair count.</param>
public sealed record ClientOptions(int Port, ClientOptions.IdleTimeOptions IdleTime, int RasKeyPair, int ClientHeartbeatTimeout)
{
    /// <summary>
    ///     Key name for the configuration storage.
    /// </summary>
    public const string StoreKey = "Client";

    /// <summary>
    ///     Default constructor for ClientOptions.
    /// </summary>
    // Resharper disable once UnusedMember.Global
    public ClientOptions() : this(6666, new IdleTimeOptions(), 100, 60)
    {
    }

    /// <summary>
    ///     Defines the configuration options for idle connection state.
    /// </summary>
    /// <param name="Reader">
    ///     The number of idle seconds without a read operation, after which the connection is considered
    ///     idle. 0 means no limit, e.g., 60.
    /// </param>
    /// <param name="Writer">
    ///     The number of idle seconds without a write operation, after which the connection is considered
    ///     idle. 0 means no limit, e.g., 60.
    /// </param>
    /// <param name="All">
    ///     The number of idle seconds without any operation, after which the connection is considered idle. 0
    ///     means no limit, e.g., 60.
    /// </param>
    public sealed record IdleTimeOptions(int Reader, int Writer, int All)
    {
        /// <summary>
        ///     Default constructor for IdleTimeOptions.
        /// </summary>
        public IdleTimeOptions() : this(0, 0, 60)
        {
        }
    }
}
