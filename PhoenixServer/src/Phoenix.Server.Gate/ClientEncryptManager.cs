// Copyright (c) Phoenix All Rights Reserved.

using Phoenix.Codecs;
using Phoenix.ConfigData;
using Serilog;
using Phoenix.MsgPackLogic.Protocol;
using System.Collections.Concurrent;

namespace Phoenix.Server.Gate;

public class ClientEncrypt
{
    public byte XORCode { get; set; }
}

public class ClientEncryptManager : IClientEncryptManager
{
    private static ClientEncryptManager? m_instance;

    public static IClientEncryptManager Instance {
        get
        {
            CreateInstance();
            return m_instance!;
        }
    }

    private readonly ConcurrentDictionary<string, byte> m_dictClientEncryptCode = new();

    private List<Int32> listXORExcept = new List<Int32>()
        {
            (Int32)EProtoCode.LOGIN_LOGINBYAUTHTOKENREQ,
            (Int32)EProtoCode.LOGIN_LOGINBYAUTHTOKENACK,
            (Int32)EProtoCode.LOGIN_LOGINBYSESSIONTOKENREQ,
            (Int32)EProtoCode.LOGIN_LOGINBYSESSIONTOKENACK
        };


    private static readonly ILogger s_logger = Log.ForContext<ClientEncryptManager>();

    public ClientEncryptManager() { }

    public static void CreateInstance()
    {
        if (m_instance == null)
        {
            m_instance = new ClientEncryptManager();
        }
    }

    public byte GenerateXORCode()
    {
        byte xorCode = (byte)new Random().Next(0, 256);
        s_logger.Information("GenerateXORCode {XORCode} not found.", xorCode);
        return xorCode;
    }

    public byte GenerateXORCode(string strSource)
    {
        if (string.IsNullOrEmpty(strSource))
        {
            throw new ArgumentException("Source string cannot be null or empty.", nameof(strSource));
        }

        int hash = strSource.GetHashCode();
        byte xorCode = (byte)(hash & 0xFF);

        s_logger.Information("GenerateXORCode by source {Source} is {XORCode}.", strSource, xorCode);

        return xorCode; 
    }

    public void SetXORCode(string id, byte rCode)
    {
        if (string.IsNullOrEmpty(id))
        {
            throw new ArgumentException("ID cannot be null or empty.", nameof(id));
        }

        m_dictClientEncryptCode[id] = rCode;
    }

    public byte GetXORCode(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            throw new ArgumentException("ID cannot be null or empty.", nameof(id));
        }

        if (m_dictClientEncryptCode.TryGetValue(id, out byte rCode))
        {
            return rCode;
        }
        else
        {
            s_logger.Warning("XOR code for ID {Id} not found.", id);
            return 0;
        }
    }

    public void RemoveXORCode(string id)
    {
        if (string.IsNullOrEmpty(id))
        {
            throw new ArgumentException("ID cannot be null or empty.", nameof(id));
        }

        if (m_dictClientEncryptCode.ContainsKey(id))
        {
            m_dictClientEncryptCode.Remove(id, out _);

            s_logger.Information("Removed XOR code for ID {Id}.", id);
        }
    }

    public bool IsExceptProtoCode(int iProtoCode)
    {
        return listXORExcept.Contains(iProtoCode);
    }
}
