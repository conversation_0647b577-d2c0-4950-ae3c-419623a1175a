// Copyright (c) Phoenix.  All Rights Reserved.

using System.Security.Cryptography;
using Phoenix.Codecs;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Phoenix.Server.Gate;

internal struct ClientSecret
{
    /// <summary>
    /// Client session id
    /// </summary>
    public string Id { get; set; }
    /// <summary>
    /// AES key, 256bit
    /// </summary>
    public string Aes<PERSON>ey { get; set; }
    /// <summary>
    /// RSA key, xml format
    /// </summary>
    public string RsaKeyXml { get; set; }
}

internal sealed class ClientSecretsManager : IClientSecretsManager
{
    public static IClientSecretsManager Instance { get; private set; } = default!;

    private readonly ILogger m_logger;
    private readonly Dictionary<string, ClientSecret> m_clientSecretsMap = new();
    private readonly ReaderWriterLockSlim m_secretsLock = new();
    private readonly List<string> m_listXmlRsaKeys = new();

    public ClientSecretsManager(ILogger<ClientSecretsManager> logger, IOptions<ClientOptions> clientOptions)
    {
        Instance = this;

        m_logger = logger;

        PrepareRsaKeys(clientOptions.Value.RasKeyPair);
    }

    private static byte[] ConvertEndian(byte[] data)
    {
        // Reverse the byte array to change the endianness
        Array.Reverse(data);
        return data;
    }

    public void GenerateRsaSecrets(string id)
    {
        try
        {
            m_secretsLock.EnterWriteLock();
            if (m_clientSecretsMap.ContainsKey(id))
            {
                m_logger.LogDebug($"{id} should not exist.");
                m_clientSecretsMap.Remove(id);
            }

            Random rand = new Random();
            int index = rand.Next(m_listXmlRsaKeys.Count);
            ClientSecret secret = new() { Id = id, AesKey = string.Empty, RsaKeyXml = m_listXmlRsaKeys[index] };
            m_logger.LogDebug($"{id} generate rsa key, index:{index}, key:{secret.RsaKeyXml}");
            m_clientSecretsMap.Add(id, secret);
        }
        finally
        {
            m_secretsLock.ExitWriteLock();
        }
    }

    public void GetLittleEndianPubKey(string id, ref string pubExp, ref string modulus)
    {
        try
        {
            m_secretsLock.EnterReadLock();
            if (!m_clientSecretsMap.TryGetValue(id, out ClientSecret secret))
            {
                m_logger.LogWarning($"{id} secret should exists.");
                return;
            }

            RSACryptoServiceProvider rsa = new RSACryptoServiceProvider();
            rsa.FromXmlString(secret.RsaKeyXml);
            var parameters = rsa.ExportParameters(true);
            modulus = Convert.ToBase64String(ConvertEndian(parameters.Modulus!));
            pubExp = Convert.ToBase64String(ConvertEndian(parameters.Exponent!));
            m_logger.LogDebug($"{id} return pubExp:{pubExp}, modulus:{modulus}");
        }
        finally
        {
            m_secretsLock.ExitReadLock();
        }
    }

    public string GetPriKeyXml(string id)
    {
        try
        {
            m_secretsLock.EnterReadLock();
            if (m_clientSecretsMap.TryGetValue(id, out ClientSecret secret))
            {
                return secret.RsaKeyXml;
            }

            m_logger.LogWarning($"{id} secret should exists.");
            return string.Empty;
        }
        finally
        {
            m_secretsLock.ExitReadLock();
        }

    }

    public void SetAesKey(string id, string inAesKey)
    {
        try
        {
            m_secretsLock.EnterWriteLock();
            if (!m_clientSecretsMap.TryGetValue(id, out ClientSecret secret))
            {
                m_logger.LogWarning($"{id} secret should exists.");
                return;
            }

            secret.AesKey = inAesKey;
            m_clientSecretsMap[id] = secret;
            m_logger.LogDebug($"{id} set aesKey:{inAesKey}.");
        }
        finally
        {
            m_secretsLock.ExitWriteLock();
        }
    }

    public string GetAesKey(string id)
    {
        try
        {
            m_secretsLock.EnterReadLock();
            if (m_clientSecretsMap.TryGetValue(id, out ClientSecret secret) && secret.AesKey != string.Empty)
            {
                return secret.AesKey;
            }

            m_logger.LogWarning($"{id} secret should exists.");
            return string.Empty;
        }
        finally
        {
            m_secretsLock.ExitReadLock();
        }
    }

    public void RemoveSecret(string id)
    {
        try
        {
            m_secretsLock.EnterWriteLock();
            m_clientSecretsMap.Remove(id);
        }
        finally
        {
            m_secretsLock.ExitWriteLock();
        }
    }

    public void PrepareRsaKeys(int keyPairs)
    {
        for (int i = 0; i < keyPairs; ++i)
        {
            RSACryptoServiceProvider rsa = new();
            m_listXmlRsaKeys.Add(rsa.ToXmlString(true));
        }
        m_logger.LogDebug($"generate {keyPairs} rsa keys");
    }
}
