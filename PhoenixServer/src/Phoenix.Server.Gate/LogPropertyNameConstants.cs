// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.Gate;

/// <summary>
///     Constants for log property names.
/// </summary>
public static class LogPropertyNameConstants
{
    /// <summary>
    ///     Unique identifier for distributed logs.
    /// </summary>
    public const string CorrelationId = "CorrelationId";

    /// <summary>
    ///     Unique identifier for a player.
    /// </summary>
    public const string PlayerId = "PlayerId";

    /// <summary>
    ///     Unique identifier for a session.
    /// </summary>
    public const string SessionId = "SessionId";
}
