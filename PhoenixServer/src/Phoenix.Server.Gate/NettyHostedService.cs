// Copyright (c) Phoenix.  All Rights Reserved.

using DotNetty.Buffers;
using DotNetty.Transport.Bootstrapping;
using DotNetty.Transport.Channels;
using DotNetty.Transport.Libuv;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Phoenix.Server.Common;

namespace Phoenix.Server.Gate;

/// <summary>
///     Netty hosted service for providing network services.
/// </summary>
public sealed class NettyHostedService : IHostedService
{
    /// <summary>
    ///     Constructor for the Netty hosted service.
    /// </summary>
    public NettyHostedService(IOptions<ClientOptions> clientOptions, ILogger<NettyHostedService> logger, NettyForMessagePackCodecFactory nettyForProtocolCodecFactory)
    {
        m_clientOptions = clientOptions.Value;
        m_logger = logger;
        m_nettyForProtocolCodecFactory = nettyForProtocolCodecFactory;

        var dispatcher = new DispatcherEventLoopGroup();
        m_bossGroup = dispatcher;
        m_workerGroup = new WorkerEventLoopGroup(dispatcher);
    }

    /// <summary>
    ///     Starts the Netty service.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token for the task.</param>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        int port = m_clientOptions.Port;
        m_logger.LogInformation("-------- gateway netty is starting,listen client port={port} -------", port);

        var bootstrap = new ServerBootstrap();
        bootstrap
            .Group(m_bossGroup, m_workerGroup)
            .Channel<TcpServerChannel>()
            .Option(ChannelOption.SoReuseaddr, true)
            .Option(ChannelOption.Allocator, PooledByteBufferAllocatorInitializer.Instance.Allocator)
            .ChildOption(ChannelOption.Allocator, PooledByteBufferAllocatorInitializer.Instance.Allocator)
            .ChildHandler(m_nettyForProtocolCodecFactory);
        m_boundChannel = await bootstrap.BindAsync(port);

        m_logger.LogInformation("-------- gateway netty is started -------");
    }

    /// <summary>
    ///     Stops the Netty service.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token for the task.</param>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        m_logger.LogInformation("-------- gateway netty is stopping -------");

        // Close the bound channel if it exists.
        if (m_boundChannel != null)
        {
            await m_boundChannel.CloseAsync();
        }

        // Shutdown the event loop groups gracefully.
        await Task.WhenAll(
            m_bossGroup.ShutdownGracefullyAsync(TimeSpan.FromMilliseconds(100), TimeSpan.FromSeconds(1)),
            m_workerGroup.ShutdownGracefullyAsync(TimeSpan.FromMilliseconds(100), TimeSpan.FromSeconds(1)));

        m_logger.LogInformation("-------- gateway netty is stopped -------");
    }
    /// <summary>
    ///     Event loop group for accepting incoming connections.
    /// </summary>
    private readonly IEventLoopGroup m_bossGroup;

    /// <summary>
    ///     Client options for the service.
    /// </summary>
    private readonly ClientOptions m_clientOptions;

    /// <summary>
    ///     Logger for logging events and errors.
    /// </summary>
    private readonly ILogger m_logger;

    /// <summary>
    ///     Event loop group for handling the traffic of the accepted connections.
    /// </summary>
    private readonly IEventLoopGroup m_workerGroup;

    /// <summary>
    ///     The bound channel for the service.
    /// </summary>
    private IChannel? m_boundChannel;

    private readonly NettyForMessagePackCodecFactory m_nettyForProtocolCodecFactory;
}
