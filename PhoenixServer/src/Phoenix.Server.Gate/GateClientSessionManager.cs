// Copyright (c) Phoenix.All Rights Reserved.

using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Phoenix.Server.Common;
using Phoenix.Server.Common.Actor;
using InternalLoggerFactory = Phoenix.Server.Common.InternalLoggerFactory;

namespace Phoenix.Server.Gate;

[RpcModule(Name = "ClientSessionManager")]
public sealed class GateClientSessionManager : Actor<string>
{
    public static GateClientSessionManager Instance;

    public GateClientSessionManager(IOptions<ClientOptions> clientOptions)
    {
        RpcModuleMethodFinder.Instance.RegModule(this);
        _ = Active();
        HeartbeatTimeout = clientOptions.Value.ClientHeartbeatTimeout;
        Instance = this;

    }

    public void AddSession(GateClientSession session)
    {
        m_sessionLock.EnterWriteLock();
        m_sessionId2Session[session.SessionId] = session;
        // m_channelId2Session[session.ChannelId] = session;
        if (session.PlayerId != 0)
        {
            m_playerId2Session.Add(session.PlayerId, session);
        }
        m_sessionLock.ExitWriteLock();
    }

    public void RemoveSession(string sessionId)
    {
        m_sessionLock.EnterWriteLock();
        if (m_sessionId2Session.Remove(sessionId, out GateClientSession? session))
        {
            m_playerId2Session.Remove(session.PlayerId);
        }
        m_sessionLock.ExitWriteLock();
    }

    [RpcMethod(Name = "UnbindPlayerClient", Role = RoleName.ROLE_ROUTER)]
    private async Task UnbindPlayerClient(RpcRequestInfo reqInfo, long playerId, bool isSpaceChannel = false)
    {
        s_logger.LogInformation("UnbindPlayerClient PlayerId={PlayerId}, IsSpaceChannel={isSpaceChannel}", playerId, isSpaceChannel);
        // notify player logout
        var session = GetSessionByPlayerId(playerId);
        if (session != null)
        {
            await session.UnbindPlayerClient();
        }
        string sessionId = session == null ? "" : session.SessionId;
        s_logger.LogInformation(
            "UnbindPlayerClient PlayerId={PlayerId}, sessionId={sessionId}, IsSpaceChannel={isSpaceChannel}",
            playerId, sessionId, isSpaceChannel);

        reqInfo.SendResponse(new Dictionary<object, object> { { RpcConstants.ERR_CODE, LoginErrorCode.OK } });
    }

    public GateClientSession? GetSession(string sessionId)
    {
        m_sessionLock.EnterReadLock();
        if (m_sessionId2Session.TryGetValue(sessionId, out GateClientSession? session))
        {
            m_sessionLock.ExitReadLock();
            return session;
        }
        m_sessionLock.ExitReadLock();
        return null;
    }

    public GateClientSession? GetSessionByPlayerId(long playerId)
    {
        m_sessionLock.EnterReadLock();
        if (m_playerId2Session.TryGetValue(playerId, out GateClientSession? session))
        {
            m_sessionLock.ExitReadLock();
            return session;
        }
        m_sessionLock.ExitReadLock();
        return null;
    }

    public override string GetActorId() => "ClientSessionManager";
    internal void OnNodeLeave(uint nodeId, string role)
    {
        PostAction(async () =>
        {
            s_logger.LogInformation("OnNodeLeave. NodeId={NodeId}, Role={Role}", nodeId, role);
            if (role == RoleName.ROLE_GAME)
            {
                List<GateClientSession> sessions = m_sessionId2Session.Where(e => e.Value.GameNodeId == nodeId).Select(e => e.Value).ToList();
                foreach (GateClientSession session in sessions)
                {
                    await session.UnbindPlayerClient();
                }
            }
        });
    }

    private static readonly ILogger s_logger =
        InternalLoggerFactory.LoggerFactory.CreateLogger<GateClientSessionManager>();

    private readonly ReaderWriterLockSlim m_sessionLock = new();
    private readonly Dictionary<string, GateClientSession> m_sessionId2Session = new();
    // private readonly Dictionary<IChannelId, GateClientSession> m_channelId2Session = new();
    private readonly Dictionary<long, GateClientSession> m_playerId2Session = new();

    public Int32 HeartbeatTimeout { get; set; } // Client heartbeat timeout in seconds
}
