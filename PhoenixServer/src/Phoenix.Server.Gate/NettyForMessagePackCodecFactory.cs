// Copyright (c) Phoenix.  All Rights Reserved.

using DotNetty.Codecs.Protobuf;
using DotNetty.Handlers.Timeout;
using DotNetty.Transport.Channels;
using Microsoft.Extensions.Options;
using Phoenix.Codecs;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Server.Gate;

/// <summary>
///     Factory for creating and initializing channels with the necessary handlers for the Netty network protocol.
/// </summary>
public sealed class NettyForMessagePackCodecFactory : ChannelInitializer<IChannel>
{
    /// <summary>
    ///     Client options for configuring the channel.
    /// </summary>
    private readonly ClientOptions m_clientOptions;

    /// <summary>
    ///     Manager for handling client secrets.
    /// </summary>
    // private readonly IClientSecretsManager m_clientSecretsManager;


/*    /// <summary>
    ///     Identifier for the message registry.
    /// </summary>
    private readonly IMessageRegistryId m_messageRegistryId*/

    /// <summary>
    ///     Constructor for the factory.
    /// </summary>
    public NettyForMessagePackCodecFactory(IOptions<ClientOptions> clientOptions)
    {
        m_clientOptions = clientOptions.Value;
    }

    /// <summary>
    ///     Initializes the channel with the necessary handlers.
    /// </summary>
    protected override void InitChannel(IChannel channel)
    {
        IChannelPipeline pipeline = channel.Pipeline;

        // Add a handler for checking the idle state of the channel.
        pipeline.AddLast("idle-check",
            new IdleStateHandler(m_clientOptions.IdleTime.Reader, m_clientOptions.IdleTime.Writer,
                m_clientOptions.IdleTime.All));

        // Add a handler for decoding the length of the data packet.
        /*pipeline.AddLast("framing-size-dec",
            new LengthFieldBasedFrameDecoder(ByteOrder.LittleEndian, int.MaxValue, 0,
                NetworkPackageConstants.LengthFiledSize, -NetworkPackageConstants.LengthFiledSize,
                NetworkPackageConstants.LengthFiledSize, true));*/

        pipeline.AddLast("framing-size-dec", new ProtobufVarint32FrameDecoder());
        pipeline.AddLast("framing-size-enc", new ProtobufVarint32LengthFieldPrepender());


        // Add a handler for encoding the length of the data packet.
        /*pipeline.AddLast("framing-size-enc",
            new LengthFieldPrepender(ByteOrder.LittleEndian, NetworkPackageConstants.LengthFiledSize,
                NetworkPackageConstants.LengthFiledSize, false));*/


        /*      // Add a handler for encrypting the data.
                pipeline.AddLast("framing-encrypt-enc", new EncryptionEncode(m_clientSecretsManager));

                // Add a handler for decrypting the data.
                pipeline.AddLast("framing-encrypt-dec", new EncryptionDecode(m_clientSecretsManager));

                // Add a handler for compressing the data packet.
                pipeline.AddLast("framing-compress-enc", new CompressEncoder(filePath));

                // Add a handler for decompressing the data packet.
                pipeline.AddLast("framing-compress-dec", new CompressDecoder(filePath));*/


        // Add a handler for decrypting the data.
        pipeline.AddLast("framing-encrypt-dec", new EncryptionDecodeXOR(ClientEncryptManager.Instance));

        // Add a handler for decoding the data packet with msgpack.
        pipeline.AddLast("framing-pb-dec", new MessagePackDecoder4Gate<MsgPackStructBase>());

        // Add a handler for encrypting the data.
        pipeline.AddLast("framing-encrypt-enc", new EncryptionEncodeXOR(ClientEncryptManager.Instance));

        // Add a handler for encoding the data packet with msgpack.
        pipeline.AddLast("framing-pb-enc", new MessagePackEncoder4Gate<MsgPackStructBase>());

        // Add a handler for processing the business logic.
        pipeline.AddLast("gate-service-handler", new GateServerHandler());
    }
}
