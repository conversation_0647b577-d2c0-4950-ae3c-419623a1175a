// Copyright (c) Phoenix.  All Rights Reserved.

using DotNetty.Transport.Channels;
using Phoenix.MsgPackLogic.Protocol;
using Serilog;
using Serilog.Context;
using Phoenix.Server.Common;

namespace Phoenix.Server.Gate;

/// <summary>
///     Handler for the gateway server, responsible for processing client requests.
/// </summary>
public sealed class GateServerHandler : SimpleChannelInboundHandler<MsgPackStructBase>
{
    /// <summary>
    ///     Constructor for the handler.
    /// </summary>
    public GateServerHandler()
    {
    }

    /// <summary>
    ///     Indicates if the handler is sharable.
    /// </summary>
    public override bool IsSharable => true;

    /// <summary>
    ///     Triggered when a user event occurs.
    /// </summary>
    public override void UserEventTriggered(IChannelHandlerContext ctx, object evt)
    {
        s_logger.Error("userEventTriggered,close the channel");
    }

    /// <summary>
    ///     Triggered when the channel is activated.
    /// </summary>
    public override void ChannelActive(IChannelHandlerContext ctx)
    {
        string sessionId = Guid.NewGuid().ToString();
        ctx.GetAttribute(NettyChannelKeyConstants.SessionId).SetIfAbsent(sessionId);

        byte xorCode = 0; // ClientEncryptManager.Instance.GenerateXORCode(sessionId);
        ctx.GetAttribute(NettyChannelKeyConstants.XORCode).SetIfAbsent(xorCode.ToString());

        using (LogContext.PushProperty(LogPropertyNameConstants.SessionId, sessionId))
        {
            s_logger.Debug("channel active ok, sessionId={SessionId} channelId={Id} remoteAddress={Address}", sessionId,
                ctx.Channel.Id, ctx.Channel.RemoteAddress);
        }

        var session = new GateClientSession(sessionId, ctx, xorCode);
        session.Active().ContinueWith((t) =>
        {
            if (t.IsCompleted)
            {
                GateClientSessionManager.Instance.AddSession(session);
            }
            else
            {
                _ = session.Inactive();
            }
        });
    }

    /// <summary>
    ///     Triggered when the channel is closed.1
    /// </summary>
    public override void ChannelInactive(IChannelHandlerContext ctx)
    {
        string sessionId = ctx.GetAttribute(NettyChannelKeyConstants.SessionId).Get();
        s_logger.Debug("channel inactive SessionId={SessionId}", sessionId);
        OnClientDisconnectOrException(sessionId);
    }

    /// <summary>
    ///     Triggered when there is a readable message in the channel.
    /// </summary>
    protected override void ChannelRead0(IChannelHandlerContext ctx, MsgPackStructBase msg)
    {
        string sessionId = ctx.GetAttribute(NettyChannelKeyConstants.SessionId).Get();
        string correlationId = Guid.NewGuid().ToString();

        var clientMessage = new ClientMessage(correlationId, msg);
        var session = GateClientSessionManager.Instance.GetSession(sessionId);
        if (session != null)
        {
            session.PostMessage(clientMessage);
        }
    }

    /// <summary>
    ///     Triggered when an exception is caught in the channel.
    /// </summary>
    public override void ExceptionCaught(IChannelHandlerContext ctx, Exception exception)
    {
        string sessionId = ctx.GetAttribute(NettyChannelKeyConstants.SessionId).Get();
        ctx.CloseAsync().ContinueWith((t) =>
        {
            s_logger.Debug(t.Exception,
                "CloseAsync cause of exceptionCaught, SessionId={SessionId} channelId={Id} remoteAddress={Address}",
                sessionId, ctx.Channel.Id, ctx.Channel.RemoteAddress);
        }, TaskContinuationOptions.OnlyOnFaulted);

        s_logger.Information(exception, "exceptionCaught close the channel,channelId={Id} remoteAddress={Address}",
            ctx.Channel.Id, ctx.Channel.RemoteAddress);

        OnClientDisconnectOrException(sessionId);
    }

    private static void OnClientDisconnectOrException(string sessionId)
    {
        GateClientSession? session = GateClientSessionManager.Instance.GetSession(sessionId);
        if (session != null)
        {
            session.OnClientDisconnect();
        }
    }

    /// <summary>
    ///     Logger for logging events and errors.
    /// </summary>
    private static readonly ILogger s_logger = Log.ForContext<GateServerHandler>();
}
