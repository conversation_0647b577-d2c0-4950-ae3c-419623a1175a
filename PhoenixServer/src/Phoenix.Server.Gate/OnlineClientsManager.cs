/*
// Copyright (c) Phoenix.  All Rights Reserved.

using DotNetty.Buffers;
using DotNetty.Transport.Channels;
using MessagePack;
using Microsoft.Extensions.Logging;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Common.Network;

namespace Phoenix.Server.Gate;

/// <summary>
///     Interface for managing online clients.
/// </summary>
public interface IOnlineClientsManager
{
    /// <summary>
    ///     Adds an online client.
    /// </summary>
    /// <param name="id">The unique identifier of the client.</param>
    /// <param name="channel">The network channel of the client.</param>
    void AddClient(string id, IChannel channel);

    void SendMessageToClient(string id, MsgPackStructBase message);

    /// <summary>
    ///     Removes an online client.
    /// </summary>
    /// <param name="id">The unique identifier of the client to be removed.</param>
    void RemoveClient(string id);

    void SendMessageToClient(string sessionId, MessagePacket packet);
}

/// <summary>
///     Implementation of the online client manager interface.
/// </summary>
public sealed class OnlineClientsManager : IOnlineClientsManager
{
    public static IOnlineClientsManager Instance { get; private set; } = default!;

    /// <summary>
    ///     Logger for logging events and errors.
    /// </summary>
    private readonly ILogger m_logger;

    /// <summary>
    ///     Lock for synchronizing access to the client map.
    /// </summary>
    private readonly ReaderWriterLockSlim m_sessionIdToChannelLock = new();

    /// <summary>
    ///     Map of client identifiers to network channels.
    /// </summary>
    private readonly Dictionary<string, IChannel> m_sessionIdToChannelMap = new();

    /// <summary>
    ///     Constructor for the online client manager.
    /// </summary>
    /// <param name="logger">Logger for logging events and errors.</param>
    public OnlineClientsManager(ILogger<OnlineClientsManager> logger)
    {
        Instance = this;

        m_logger = logger;
    }

    /// <summary>
    ///     Adds an online client.
    /// </summary>
    /// <param name="id">The unique identifier of the client.</param>
    /// <param name="channel">The network channel of the client.</param>
    public void AddClient(string id, IChannel channel)
    {
        // Acquire the write lock before modifying the client map.
        m_sessionIdToChannelLock.EnterWriteLock();
        try
        {
            // Add the client to the map.
            if(!m_sessionIdToChannelMap.ContainsKey(id))
            {
                m_sessionIdToChannelMap.Add(id, channel);
            }
            m_logger.LogDebug("add online client ok,sessionId={sessionId} channelId={channelId}", id, channel.Id);
        }
        finally
        {
            // Always release the lock to avoid deadlocks.
            m_sessionIdToChannelLock.ExitWriteLock();
        }
    }

    /// <summary>
    ///     Removes an online client.
    /// </summary>
    /// <param name="id">The unique identifier of the client to be removed.</param>
    public void RemoveClient(string id)
    {
        // Acquire the write lock before modifying the client map.
        m_sessionIdToChannelLock.EnterWriteLock();
        try
        {
            // Remove the client from the map.
            m_sessionIdToChannelMap.Remove(id);
            m_logger.LogDebug("remove online client ok");
        }
        finally
        {
            // Always release the lock to avoid deadlocks.
            m_sessionIdToChannelLock.ExitWriteLock();
        }
    }

    /#1#// <summary>
    ///     Sends a message to the client.
    /// </summary>
    /// <param name="id">The unique identifier of the client.</param>
    /// <param name="message">The message to be sent.</param>
    private void DoSendMessageToClient(string id, IMessage message)
    {
        // Get the network channel for the client.
        IChannel? channel = GetChannel(id);
        if (channel == null)
        {
            m_logger.LogError("send message to client failed,channel not found");
            return;
        }

        // Check if the channel is active.
        if (!channel.Active)
        {
            m_logger.LogError("send message to client failed,channel is not active");
            return;
        }

        // Check if the channel is writable.
        if (!channel.IsWritable)
        {
            m_logger.LogError("send message to client failed,channel is not active");
            return;
        }

        // Write the message to the channel.
        channel.WriteAndFlushAsync(message);

        // If the message is a server disconnect notification, close the channel.
        if (message is MsgPack_ServerDisconnect_Ntf ntf)
        {
            m_logger.LogDebug("starting close the channel,received MsgPack_ServerDisconnect_Ntf,ntf={ntf}", ntf);
            channel.CloseAsync();
        }
    }
#1#
    /// <summary>
    ///     Gets the network channel for a client.
    /// </summary>
    /// <param name="id">The unique identifier of the client.</param>
    /// <returns>The network channel for the client.</returns>
    private IChannel? GetChannel(string id)
    {
        // Acquire the read lock before accessing the client map.
        m_sessionIdToChannelLock.EnterReadLock();
        try
        {
            // Get the network channel for the client.
            m_sessionIdToChannelMap.TryGetValue(id, out IChannel? result);
            return result;
        }
        finally
        {
            // Always release the lock to avoid deadlocks.
            m_sessionIdToChannelLock.ExitReadLock();
        }
    }

    public void SendMessageToClient(string id, MsgPackStructBase message)
    {
        // Get the network channel for the client.
        IChannel? channel = GetChannel(id);
        if (channel == null)
        {
            m_logger.LogError("send message to client failed,channel not found");
            return;
        }

        // Check if the channel is active.
        if (!channel.Active)
        {
            m_logger.LogError("send message to client failed,channel is not active");
            return;
        }

        // Check if the channel is writable.
        if (!channel.IsWritable)
        {
            m_logger.LogError("send message to client failed,channel is not active");
            return;
        }

        // Write the message to the channel.
        channel.WriteAndFlushAsync(message);

        // If the message is a server disconnect notification, close the channel.
        /*if (message is MsgPack_ServerDisconnect_Ntf ntf)
        {
            m_logger.LogDebug("starting close the channel,received MsgPack_ServerDisconnect_Ntf,ntf={ntf}", ntf);
            channel.CloseAsync();
        }#1#
    }

    public void SendMessageToClient(string sessionId, MessagePacket packet)
    {
        // Get the network channel for the client.
        IChannel? channel = GetChannel(sessionId);
        if (channel == null)
        {
            m_logger.LogError("send message to client failed,channel not found");
            return;
        }

        // Check if the channel is active.
        if (!channel.Active)
        {
            m_logger.LogError("send message to client failed,channel is not active");
            return;
        }

        // Check if the channel is writable.
        if (!channel.IsWritable)
        {

            m_logger.LogError("send message to client failed,channel is not active");
            return;
        }

        // Write the message to the channel.
        var buffer = Unpooled.WrappedBuffer(MessagePackSerializer.Serialize(packet));
        channel.WriteAndFlushAsync(buffer);
    }
}
*/
