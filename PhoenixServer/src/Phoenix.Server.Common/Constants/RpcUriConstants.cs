// Copyright (c) Phoenix.All Rights Reserved.

namespace Phoenix.Server.Common;

public static class RpcUriConstants
{
    public static readonly string BASE_PLAYER_MGR_URI = $"{RoleName.ROLE_GAME}.LocalPlayerManager";

    // client session manager in gate
    public static readonly string GATE_CLIENT_SESSION_MGR_URI = $"{RoleName.ROLE_GATE}.ClientSessionManager";

    public static readonly string WORLD_PLAYER_REGISTRY_URI = $"{RoleName.ROLE_ROUTER}.WorldPlayerRegistry";

    public static readonly string LOGIN_MGR_URI = $"{RoleName.ROLE_LOGIN}.LoginMgr";
}
