// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.Common;

/// <summary>
///     Helper class for generating Redis keys.
/// </summary>
public static class RedisKeyHelper
{
    /// <summary>
    ///     Delimiter used in the key.
    /// </summary>
    private const char Delimiter = ':';

    /// <summary>
    ///     Generates a Redis key for mapping player ID to game service name.
    /// </summary>
    /// <param name="databaseName">The name of the database.</param>
    /// <param name="playerId">The unique identifier of the player.</param>
    /// <returns>A string representing the Redis key.</returns>
    public static string GetPlayerIdToGameServiceNameKey(string databaseName, long playerId) => string.Join(Delimiter,
        databaseName, RedisKeyConstants.PlayerIdToGameServiceName, playerId);
}
