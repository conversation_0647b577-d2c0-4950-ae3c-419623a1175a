namespace Phoenix.Server.Common
{
    public static class LoginErrorCode
    {
        // General error code definition for service
        // shared by all services and game client
        public const int OK = 0;

        public const int ERR_FAIL = 100;          // un categorized error
        public const int ERR_NOTFOUND = 101;      // data not found
        public const int ERR_DUPLICATE = 102;     // data duplicate error
        public const int ERR_DB_FAIL = 103;       // generic database error
        public const int ERR_INVALID_ARGS = 104;  // invalid args
        public const int ERR_AUTH_FORBID = 105;   // auth failed

        public const int ERR_PLAYER_IS_LOADING = 106;  // player on the game is loading when another login request comes in
        public const int ERR_BE_KICK_OUT = 107;        // Player is kicked out by the server.
        public const int ERR_BE_RELOGIN = 108;         // player relogin by other client session
        public const int ERR_NO_GAME_NODE_AVAILABLE = 109;  // player_stub has no available game node to select during login.
        public const int ERR_REQ_IN_ZONE_SHUTDOWN = 110;  // RPC request received when the cluster is preparing to shut down, not processed.
        public const int ERR_PLAYER_IS_REBINDING = 111;  // player is taking over the logged-in session.
        public const int ERR_WORLD_PLAYER_REGISTRY_NOT_FOUND = 112;  // world cannot find player proxy
        public const int ERR_PLAYER_IS_DESTROYING = 113;  // player on the game is being destroyed
        public const int ERR_PLAYER_IS_MIGRATING = 114;  // player on the game is migrating
        public const int ERR_PLAYER_STATE = 115;  // player state on the world is incorrect
        public const int ERR_MAINTENANCE = 116;  // server cluster in maintenance status
        public const int ERR_WORLD_RESHARDING = 117;  // the world is in resharding status
        public const int ERR_NO_AVAILABLE_DS = 118;  // no available DS for players, kick out
        public const int ERR_REQUEST_TOO_FAST = 119;  // client request in an unacceptable frequency
        public const int ERR_PLAYER_ENTITY_NOTFOUND = 120;  // player entity not found in game
        public const int ERR_CLIENT_VERSION_NOT_MATCH = 121;  // client version not match
        public const int ERR_NODE_NOT_MATCH = 122;  // node not match
        public const int ERR_PLAYER_ALREADY_EXISTS = 123;  // player already exists on game
        public const int ERR_PLAYER_INIT_FAILED = 124;  // player init failed on game

        // error code of player login procedure
        public const int LOGIN_VALIDATE_FAIL = 201;
        public const int LOGIN_VALIDATE_TIMEOUT = 202;
        public const int LOGIN_NO_SDK_USERNAME = 203;
        public const int LOGIN_NAME_INVALID = 204;
        public const int LOGIN_NAME_OCCUPIED = 205;
        public const int LOGIN_ZONE_ROLE_INSERT_FAIL = 206;  // unable to insert zone_role to DB for newborn player
        public const int LOGIN_NO_TICKET = 207;  // no available ticket for login
        public const int LOGIN_DOING_GIVE_TICKET = 208;  // currently distributing login tickets
        public const int LOGIN_QUEUE_TOO_LONG = 209;  // login queue is too long, unable to queue
        public const int LOGIN_ZONE_ROLE_TIMEOUT = 210;  // login timeout
        public const int LOGIN_CREATE_PLAYER_TIMEOUT = 211;  // create newborn player timeout
        public const int LOGIN_LOAD_PLAYER_TIMEOUT = 212;  // login process timeout
        public const int LOGIN_FORBID_WIZARD = 213;  // forbid inner-dev wizard login
        public const int LOGIN_LOAD_PLAYER_FAIL = 214;  // game load player from DB failed
        public const int LOGIN_CREATE_PLAYER_FAIL = 215;  // failed to insert player data into the database on the world
        public const int LOGIN_CREATE_REMOTE_PLAYER_PROXY_FAIL = 216;  // failed to create player proxy on the space
        public const int LOGIN_ACCOUNT_ZONE_INFO_FAIL = 217;  // login Load account info fail
        public const int LOGIN_ALLOCATE_UID_FAIL = 218;  // fail to allocate a uid
        

        // login service error code
        public const int LOGIN_ERR_FAIL = 251;  // login failed for unknown reasons
        public const int LOGIN_ERR_SYS_BUSY = 252;  // system busy, login rejected
        public const int LOGIN_ERR_AUTH_FAIL = 253;  // authentication failed
        public const int LOGIN_ERR_REAUTH = 254;  // refuse continuous login
        public const int LOGIN_ERR_SAUTH_TIMEOUT = 255;  // sauth timeout
        public const int LOGIN_ERR_INVALID_ARG = 256;  // invalid argument
        public const int LOGIN_ERR_CREATE_ROLE_FAIL = 257;  // failed to create role
        public const int LOGIN_ERR_GAME_PLAYER_NOT_EXIST = 258;  // player on the game does not exist when gate binds to the game
        public const int LOGIN_ERR_AUTH_BAD_RESPONSE = 259;  // authentication service bad response

        // client reconnect error code
        public const int RECONNECT_ERR_WORLD_PLAYER_PROXY_NOT_EXIST = 301;  // world player proxy does not exist during reconnection
        public const int RECONNECT_ERR_LOGIN_KEY = 302;  // login key verification failed during reconnection
        public const int RECONNECT_ERR_DIFFERENT_GATE = 303;  // connecting to a different gate during reconnection
        public const int RECONNECT_ERR_MIGRATING = 304;  // player is migrating during reconnection
        public const int RECONNECT_ERR_PLAYER_NOT_EXIST = 305;  // player on the game does not exist during reconnection
        public const int RECONNECT_ERR_PLAYER_IS_DESTROYING = 306;  // player on the game is being destroyed during reconnection
        public const int RECONNECT_ERR_KICK_OLD_GATE_FAIL = 307;  // kick old gate fail

        // RPC related error codes
        public const int MAILBOX_ERR_REGISTER_FAIL = 351;  // Register mailbox error

        // kick out related error codes
        public const int KICK_OUT_GM = 450;  // Kicked out by GM
        public const int KICK_OUT_TELEPORT_FAIL = 451;  // Kicked out due to teleport fail
        public const int KICK_OUT_SPACE_DESTROY = 452;  // Kicked out due to space destroy
        public const int KICK_OUT_RETRY_ZOMBIE_DESTROY = 453;  // Kicked out due to zombie inconsistent entity

        // Error messages mapping for error code
        public static readonly Dictionary<int, string> ERR_CODE_MSG = new Dictionary<int, string>
        {
            { OK, "Success"},
            { ERR_FAIL, "un categorized error" },
            { ERR_NOTFOUND, "data not found" },
            { ERR_DUPLICATE, "data duplicate error" },
            { ERR_DB_FAIL, "generic database error" },
            { ERR_INVALID_ARGS, "invalid args" },
            { ERR_AUTH_FORBID, "auth failed" },
            { ERR_PLAYER_IS_LOADING, "player on the game is loading when another login request comes in" },
            { ERR_BE_KICK_OUT, "player is kicked out by the server actively" },
            { ERR_BE_RELOGIN, "player relogin by other client session" },
            { ERR_NO_GAME_NODE_AVAILABLE, "player_stub has no available game node to select during login" },
            { ERR_REQ_IN_ZONE_SHUTDOWN, "RPC request received when the cluster is preparing to shut down, not processed" },
            { ERR_PLAYER_IS_REBINDING, "player is taking over the logged-in session." },
            { ERR_WORLD_PLAYER_REGISTRY_NOT_FOUND, "world cannot find player proxy" },
            { ERR_PLAYER_IS_DESTROYING, "player on the game is being destroyed" },
            { ERR_PLAYER_IS_MIGRATING, "player on the game is migrating" },
            { ERR_PLAYER_STATE, "player state on the world is incorrect" },
            { ERR_MAINTENANCE, "server cluster in maintenance status" },
            { ERR_WORLD_RESHARDING, "the world is in resharding status" },
            { ERR_NO_AVAILABLE_DS, "no available DS for players" },
            { LOGIN_VALIDATE_FAIL, "error code of player login procedure" },
            { LOGIN_VALIDATE_TIMEOUT, "login validation timeout" },
            { LOGIN_NO_SDK_USERNAME, "no SDK username for login" },
            { LOGIN_NAME_INVALID, "invalid login name" },
            { LOGIN_NAME_OCCUPIED, "login name is already occupied" },
            { LOGIN_ZONE_ROLE_INSERT_FAIL, "unable to insert zone_role to DB for newborn player" },
            { LOGIN_NO_TICKET, "no available ticket for login" },
            { LOGIN_DOING_GIVE_TICKET, "currently distributing login tickets" },
            { LOGIN_QUEUE_TOO_LONG, "login queue is too long, unable to queue" },
            { LOGIN_ZONE_ROLE_TIMEOUT, "login timeout" },
            { LOGIN_CREATE_PLAYER_TIMEOUT, "create newborn player timeout" },
            { LOGIN_LOAD_PLAYER_TIMEOUT, "login process timeout" },
            { LOGIN_FORBID_WIZARD, "forbid inner-dev wizard login" },
            { LOGIN_LOAD_PLAYER_FAIL, "game load player from DB failed" },
            { LOGIN_CREATE_PLAYER_FAIL, "failed to insert player data into the database on the world" },
            { LOGIN_CREATE_REMOTE_PLAYER_PROXY_FAIL, "failed to create player proxy on the space" },
            { LOGIN_ERR_FAIL, "login failed for unknown reasons" },
            { LOGIN_ERR_SYS_BUSY, "system busy, login rejected" },
            { LOGIN_ERR_AUTH_FAIL, "authentication failed" },
            { LOGIN_ERR_REAUTH, "refuse continuous login" },
            { LOGIN_ERR_SAUTH_TIMEOUT, "sauth timeout" },
            { LOGIN_ERR_INVALID_ARG, "invalid argument" },
            { LOGIN_ERR_CREATE_ROLE_FAIL, "failed to create role" },
            { LOGIN_ERR_GAME_PLAYER_NOT_EXIST, "player on the game does not exist when gate binds to the game" },
            { LOGIN_ERR_AUTH_BAD_RESPONSE, "authentication service bad response" },
            { RECONNECT_ERR_WORLD_PLAYER_PROXY_NOT_EXIST, "world player proxy does not exist during reconnection" },
            { RECONNECT_ERR_LOGIN_KEY, "login key verification failed during reconnection" },
            { RECONNECT_ERR_DIFFERENT_GATE, "connecting to a different gate during reconnection" },
            { RECONNECT_ERR_MIGRATING, "player is migrating during reconnection" },
            { RECONNECT_ERR_PLAYER_NOT_EXIST, "player on the game does not exist during reconnection" },
            { RECONNECT_ERR_PLAYER_IS_DESTROYING, "player on the game is being destroyed during reconnection" },
            { MAILBOX_ERR_REGISTER_FAIL, "Register mailbox error" },
            { KICK_OUT_GM, "Kicked out by GM" },
            { KICK_OUT_TELEPORT_FAIL, "Kicked out due to teleport fail" },
            { KICK_OUT_SPACE_DESTROY, "Kicked out due to space destroy" },
            { KICK_OUT_RETRY_ZOMBIE_DESTROY, "Kicked out due to zombie inconsistent entity" },
            { ERR_REQUEST_TOO_FAST, "login request too fast" },
            { LOGIN_ALLOCATE_UID_FAIL, "login allocated uid failed" }
        };
    }
}
