// Copyright (c) Phoenix.  All Rights Reserved.

using Phoenix.Server.CommonDataStructure;
using Phoenix.Server.Utils;
using Phoenix.Server.Utils.Security;

namespace Phoenix.Server.Common;

/// <summary>
///     Helper class for handling session tokens.
/// </summary>
public static class SessionTokenHelper
{
    /// <summary>
    ///     Validates a session token.
    /// </summary>
    /// <param name="sessionToken">The session token to validate.</param>
    /// <param name="token">Output parameter that receives the parsed session token if it is valid.</param>
    /// <returns>An error code indicating the result of the validation.</returns>
    public static ErrCode IsValid(string sessionToken, out SessionToken? token)
    {
        token = SessionToken.TryParse(sessionToken);
        if (token == null)
        {
            return ErrCode.ErrCodeLoginErrSessionTokenFormatInvalid;
        }

        if (!RSA.VerifyData(token.GetTokenWithoutSignature(sessionToken), token.Signature))
        {
            return ErrCode.ErrCodeSignVerifyFail;
        }

        DateTime expiredTime = DateTimeHelper.GetUtcDateTimeFromTimestampSeconds(long.Parse(token.TimeStampSeconds));
        //TODO 是否需要使用的统一的 DateTimeService 获取到的当前时间
        if (DateTime.UtcNow > expiredTime)
        {
            return ErrCode.ErrCodeSessionTokenExpired;
        }

        return ErrCode.ErrCodeOk;
    }
}
