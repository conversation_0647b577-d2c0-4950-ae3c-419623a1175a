// Copyright (c) Phoenix All Rights Reserved.

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Phoenix.Server.Common.HostedServices;

namespace Phoenix.Server.Common;

public static partial class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加Consul Leader发现服务
    /// </summary>
    public static IServiceCollection AddConsulLeaderDiscovery(this IServiceCollection services)
    {
        services.AddSingleton<ConsulLeaderDiscoveryService>();
        services.AddHostedService(sp => sp.GetRequiredService<ConsulLeaderDiscoveryService>());
        return services;
    }

    public static IServiceCollection AddConsulLeaderElection(this IServiceCollection services)
    {
        services.AddSingleton<ConsulLeaderElectionService>();
        services.AddHostedService(sp => sp.GetRequiredService<ConsulLeaderElectionService>());
        return services;
    }
}

/// <summary>
/// IServiceCollection的扩展方法
/// </summary>
public static partial class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加Phoenix服务器通用服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddPhoenixCommonServices(this IServiceCollection services)
    {
        // 添加进程标识服务
        services.AddHostedService<ProcessIdentityService>();

        // 添加Actor统计服务
        services.AddActorStatisticsService();

        /*// 在这里添加其他通用服务
        services.AddSerilog((serviceProvider, loggerConfiguration) =>
        {
            /*ElasticsearchOptions elasticsearchOptions =
                serviceProvider.GetRequiredService<IOptions<ElasticsearchOptions>>().Value;#1#

            loggerConfiguration.ReadFrom.Configuration(serviceProvider.GetRequiredService<IConfiguration>());
            /*.WriteTo.Elasticsearch(new[] { new Uri(elasticsearchOptions.Uri) }, opts =>
            {
                opts.DataStream = new DataStreamName(elasticsearchOptions.DataStream.Type,
                    elasticsearchOptions.DataStream.DataSet, elasticsearchOptions.DataStream.Namespace);
                opts.BootstrapMethod = BootstrapMethod.Failure;
                opts.ConfigureChannel = channelOpts =>
                {
                    channelOpts.BufferOptions = new BufferOptions { ExportMaxConcurrency = 10 };
                };
            });#1#
        });*/

        return services;
    }

    public static void InitSerilog(this IHostApplicationBuilder builder)
    {
        // 从配置文件读取Serilog配置
        Log.Logger = new LoggerConfiguration()
            .ReadFrom.Configuration(builder.Configuration)
            .Enrich.FromLogContext()
            .CreateBootstrapLogger();

        builder.Logging.ClearProviders();
        builder.Logging.AddSerilog(dispose:true);

        // 使用Serilog作为日志提供程序
        builder.Logging.ClearProviders();
        builder.Logging.AddSerilog(dispose: true);

        InternalLoggerFactory.LoggerFactory = builder.Services.BuildServiceProvider().GetRequiredService<ILoggerFactory>();
    }

    /// <summary>
    /// Add actor statistics service to the service collection
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddActorStatisticsService(this IServiceCollection services)
    {
        services.AddHostedService<ActorStatisticsService>();
        return services;
    }
}
