/*using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;
using Microsoft.Extensions.Logging;
using Serilog.Core;

namespace Phoenix.Service
{
    public class NexLengthFieldBasedFrameDecoder : ByteToMessageDecoder
    {
        readonly ByteOrder byteOrder;
        readonly int maxFrameLength;
        readonly int lengthFieldOffset;
        readonly int lengthFieldLength;
        readonly int lengthFieldEndOffset;
        readonly int lengthAdjustment;
        readonly int initialBytesToStrip;
        readonly bool failFast;
        bool discardingTooLongFrame;
        long tooLongFrameLength;
        readonly long bytesToDiscard;

        public NexLengthFieldBasedFrameDecoder()
        {
        }

        protected override void Decode(IChannelHandlerContext context, IByteBuffer input, List<object?> output)
        {
            object? decoded = this.Decode(context, input);
            if (decoded != null)
            {
                output.Add(decoded);
            }
        }

        /// <summary>
        ///     Create a frame out of the <see cref="IByteBuffer" /> and return it.
        /// </summary>
        /// <param name="context">
        ///     The <see cref="IChannelHandlerContext" /> which this <see cref="ByteToMessageDecoder" /> belongs
        ///     to.
        /// </param>
        /// <param name="input">The <see cref="IByteBuffer" /> from which to read data.</param>
        /// <returns>The <see cref="IByteBuffer" /> which represents the frame or <c>null</c> if no frame could be created.</returns>
        protected object? Decode(IChannelHandlerContext context, IByteBuffer input)
        {
            if (input.ReadableBytes < NexProtoConstants.kMsgLenFieldSize)
            {
                return null;
            }

            int actualLengthFieldOffset = input.ReaderIndex + 0;
            int lenField = input.GetIntLE(actualLengthFieldOffset);
            ProtoMessagePacker.PeekLengthAdTag(lenField, out int frameLength, out byte flag);
            System.Diagnostics.Debug.Assert(flag == 0);
            if (frameLength < 0)
            {
                input.SkipBytes(this.lengthFieldEndOffset);
                throw new CorruptedFrameException("negative pre-adjustment length field: " + frameLength);
            }

            int frameLengthInt = (int)frameLength;
            if (input.ReadableBytes < frameLengthInt)
            {
                return null;
            }

            // extract frame
            int readerIndex = input.ReaderIndex;
            int actualFrameLength = frameLengthInt;
            // int actualFrameLength = frameLengthInt - this.initialBytesToStrip;
            IByteBuffer frame = this.ExtractFrame(context, input, readerIndex, actualFrameLength);
            try
            {
                input.SetReaderIndex(readerIndex + actualFrameLength);
                ushort mainCmd = (ushort)frame.GetShortLE(4);
                byte subCmd = frame.GetByte(6);
                var cmdIndex = ProtoMessagePacker.GetProtoCmdIndex(mainCmd, subCmd);
                var type = NexProtoCmdInspector.GetTypeByCmdIndex(cmdIndex);
                if (type == null)
                {
                    throw new CorruptedFrameException($"NexProtoCmdInspector.GetTypeByCmdIndex exception cmdIndex={cmdIndex}");
                }
                var protoMessage = Activator.CreateInstance(type) as INexProtoMessage;
                if (protoMessage == null)
                {
                    throw new CorruptedFrameException($"protoMessage is null type={type}");
                }
                ByteBuffer buffer = new(frame.Array, frame.ArrayOffset + 7, frame.Capacity - 7);
                protoMessage.ParseFromBuffer(buffer, frame.Capacity - 7);
                return protoMessage;
            }
            catch (Exception e)
            {
                Logger.LogError("Decode exception:{exp}", e);
                return null;
            }
            finally
            {
                frame.Release();
            }
        }

        protected virtual IByteBuffer ExtractFrame(IChannelHandlerContext context, IByteBuffer buffer, int index, int length)
        {
            IByteBuffer buff = buffer.Slice(index, length);
            buff.Retain();
            return buff;
        }

        void FailIfNecessary(bool firstDetectionOfTooLongFrame)
        {
            if (this.bytesToDiscard == 0)
            {
                // Reset to the initial state and tell the handlers that
                // the frame was too large.
                long tooLongFrameLength = this.tooLongFrameLength;
                this.tooLongFrameLength = 0;
                this.discardingTooLongFrame = false;
                if (!this.failFast ||
                    this.failFast && firstDetectionOfTooLongFrame)
                {
                    this.Fail(tooLongFrameLength);
                }
            }
            else
            {
                // Keep discarding and notify handlers if necessary.
                if (this.failFast && firstDetectionOfTooLongFrame)
                {
                    this.Fail(this.tooLongFrameLength);
                }
            }
        }

        void Fail(long frameLength)
        {
            if (frameLength > 0)
            {
                throw new TooLongFrameException("Adjusted frame length exceeds " + this.maxFrameLength +
                    ": " + frameLength + " - discarded");
            }
            else
            {
                throw new TooLongFrameException(
                    "Adjusted frame length exceeds " + this.maxFrameLength +
                        " - discarding");
            }
        }

        private ILogger Logger = Phoenix.Server.Log.InternalLoggerFactory.LoggerFactory.CreateLogger("NexLengthFieldBasedFrameDecoder");
    }
}
*/
