// Copyright (c) Phoenix.All Rights Reserved.

using System.Threading.Channels;
using Microsoft.Extensions.Logging;

namespace Phoenix.Server.Common
{
    public sealed class AsyncDispatcher // : SynchronizationContext, IDisposable
    {
        public AsyncDispatcher(int stopTimeoutMilliseconds = 60000)
        {
            m_stopTimeoutMilliseconds = stopTimeoutMilliseconds;
            UnboundedChannelOptions options = new()
            {
                SingleWriter = false, SingleReader = true,
            };
            m_actionList = Channel.CreateUnbounded<Action>(options);
            m_workTask = Task.Factory.StartNew(Run, CancellationToken.None,
                TaskCreationOptions.DenyChildAttach | TaskCreationOptions.LongRunning, TaskScheduler.Default);
        }

        private readonly Task m_workTask;

        public void Start()
        {
            if (Interlocked.CompareExchange(ref m_state, State.Running, State.Created) != State.Created)
            {
                s_logger.LogWarning("Attempted to start already running dispatcher");
            }
        }

        public void Post<T0>(Action<T0> action, T0 arg0)
        {
            var actionInvoker = new ActionInvoker<T0>(action, arg0);
            Post(actionInvoker.Invoke);
        }

        public void Post<T0, T1>(Action<T0, T1> action, T0 arg0, T1 arg1)
        {
            var actionInvoker = new ActionInvoker<T0, T1>(action, arg0, arg1);
            Post(actionInvoker.Invoke);
        }

        public void Post<T0, T1, T2>(Action<T0, T1, T2> action, T0 arg0, T1 arg1, T2 arg2)
        {
            var actionInvoker = new ActionInvoker<T0, T1, T2>(action, arg0, arg1, arg2);
            Post(actionInvoker.Invoke);
        }

        public void Post<T0, T1, T2, T3>(Action<T0, T1, T2, T3> action, T0 arg0, T1 arg1, T2 arg2, T3 arg3)
        {
            var actionInvoker = new ActionInvoker<T0, T1, T2, T3>(action, arg0, arg1, arg2, arg3);
            Post(actionInvoker.Invoke);
        }

        public void Post<T0, T1, T2, T3, T4>(Action<T0, T1, T2, T3, T4> action, T0 arg0, T1 arg1, T2 arg2, T3 arg3, T4 arg4)
        {
            var actionInvoker = new ActionInvoker<T0, T1, T2, T3, T4>(action, arg0, arg1, arg2, arg3, arg4);
            Post(actionInvoker.Invoke);
        }

        public void Post<T0, T1, T2, T3, T4, T5>(Action<T0, T1, T2, T3, T4, T5> action, T0 arg0, T1 arg1, T2 arg2, T3 arg3, T4 arg4, T5 arg5)
        {
            var actionInvoker = new ActionInvoker<T0, T1, T2, T3, T4, T5>(action, arg0, arg1, arg2, arg3, arg4, arg5);
            Post(actionInvoker.Invoke);
        }

        public void Post<T0, T1, T2, T3, T4, T5, T6>(Action<T0, T1, T2, T3, T4, T5, T6> action, T0 arg0, T1 arg1, T2 arg2, T3 arg3, T4 arg4, T5 arg5, T6 arg6)
        {
            var actionInvoker = new ActionInvoker<T0, T1, T2, T3, T4, T5, T6>(action, arg0, arg1, arg2, arg3, arg4, arg5, arg6);
            Post(actionInvoker.Invoke);
        }

        public void Post(Action action)
        {
            PostInternal(action);
        }

        public async Task Stop()
        {
            if (Interlocked.CompareExchange(ref m_state, State.Stopping, State.Running) != State.Running)
                return;

            s_logger.LogInformation("Initiating shutdown...");
            m_actionList.Writer.TryComplete();
            var timeoutCts = new CancellationTokenSource(TimeSpan.FromSeconds(m_stopTimeoutMilliseconds));
            await m_actionList.Reader.Completion.WaitAsync(timeoutCts.Token);
            await m_cancellationTokenSource.CancelAsync();
        }

        public bool IsRunning => m_state == State.Running;

        private async Task Run()
        {
            try
            {
                while (!m_cancellationTokenSource.IsCancellationRequested)
                {
                    var action = await m_actionList.Reader.ReadAsync();
                    ExecuteWithErrorHandling(action);
                }
            }
            catch (OperationCanceledException)
            {
                // Expected during shutdown
            }
            finally
            {
                Interlocked.Exchange(ref m_state, State.Stopped);
                s_logger.LogInformation("Dispatcher thread exited");
            }
        }

        private void PostInternal(Action action)
        {
            if (m_cancellationTokenSource.IsCancellationRequested)
            {
                s_logger.LogWarning("Post rejected. State: {State}", m_state);
                return;
            }

            if(!m_actionList.Writer.TryWrite(action))
            {
                s_logger.LogWarning("Post rejected. Channel is full.");
            }
        }

        private void ExecuteImmediately(Action action)
        {
            try
            {
                action();
            }
            catch (Exception ex)
            {
                s_logger.LogError(ex, "Immediate action failed");
                if (++m_consecutiveErrors >= 10)
                {
                    s_logger.LogCritical("Critical error threshold reached. Initiating shutdown...");
                    Stop();
                }
            }
        }

        private void ExecuteWithErrorHandling(Action action)
        {
            try
            {
                action();
                m_consecutiveErrors = 0;
            }
            catch (Exception ex)
            {
                s_logger.LogError(ex, "Action execution failed");
                if (++m_consecutiveErrors >= 10)
                {
                    s_logger.LogCritical("Critical error threshold reached. Initiating shutdown...");
                    // Stop();
                }
            }
        }

        private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<AsyncDispatcher>();

        private readonly Channel<Action> m_actionList;
        private readonly CancellationTokenSource m_cancellationTokenSource = new();
        private readonly int m_stopTimeoutMilliseconds;
        private int m_consecutiveErrors;
        private int m_state = State.Created;

        #region 内部类

        private static class State
        {
            public const int Created = 0;
            public const int Running = 1;
            public const int Stopping = 2;
            public const int Stopped = 3;
        }

        private interface IActionInvoker
        {
            void Invoke();
        }

        private struct ActionInvoker<T0>(Action<T0> action, T0 arg0) : IActionInvoker
        {
            public void Invoke()
            {
                m_action(m_arg0);
            }

            private Action<T0> m_action = action;
            private T0 m_arg0 = arg0;
        }

        private struct ActionInvoker<T0, T1>(Action<T0, T1> action, T0 arg0, T1 arg1) : IActionInvoker
        {
            public void Invoke()
            {
                m_action(m_arg0, m_arg1);
            }

            private Action<T0, T1> m_action = action;
            private T0 m_arg0 = arg0;
            private T1 m_arg1 = arg1;
        }

        private struct ActionInvoker<T0, T1, T2>(Action<T0, T1, T2> action, T0 arg0, T1 arg1, T2 arg2) : IActionInvoker
        {
            public void Invoke()
            {
                m_action(m_arg0, m_arg1, m_arg2);
            }

            private Action<T0, T1, T2> m_action = action;
            private T0 m_arg0 = arg0;
            private T1 m_arg1 = arg1;
            private T2 m_arg2 = arg2;
        }

        private struct ActionInvoker<T0, T1, T2, T3>(Action<T0, T1, T2, T3> action, T0 arg0, T1 arg1, T2 arg2, T3 arg3)
            : IActionInvoker
        {
            public void Invoke()
            {
                m_action(m_arg0, m_arg1, m_arg2, m_arg3);
            }

            private Action<T0, T1, T2, T3> m_action = action;
            private T0 m_arg0 = arg0;
            private T1 m_arg1 = arg1;
            private T2 m_arg2 = arg2;
            private T3 m_arg3 = arg3;
        }

        private struct ActionInvoker<T0, T1, T2, T3, T4>(
            Action<T0, T1, T2, T3, T4> action,
            T0 arg0,
            T1 arg1,
            T2 arg2,
            T3 arg3,
            T4 arg4) : IActionInvoker
        {
            public void Invoke()
            {
                m_action(m_arg0, m_arg1, m_arg2, m_arg3, m_arg4);
            }

            private Action<T0, T1, T2, T3, T4> m_action = action;
            private T0 m_arg0 = arg0;
            private T1 m_arg1 = arg1;
            private T2 m_arg2 = arg2;
            private T3 m_arg3 = arg3;
            private T4 m_arg4 = arg4;
        }

        private struct ActionInvoker<T0, T1, T2, T3, T4, T5>(
            Action<T0, T1, T2, T3, T4, T5> action,
            T0 arg0,
            T1 arg1,
            T2 arg2,
            T3 arg3,
            T4 arg4,
            T5 arg5) : IActionInvoker
        {
            public void Invoke()
            {
                m_action(m_arg0, m_arg1, m_arg2, m_arg3, m_arg4, m_arg5);
            }

            private Action<T0, T1, T2, T3, T4, T5> m_action = action;
            private T0 m_arg0 = arg0;
            private T1 m_arg1 = arg1;
            private T2 m_arg2 = arg2;
            private T3 m_arg3 = arg3;
            private T4 m_arg4 = arg4;
            private T5 m_arg5 = arg5;
        }

        private struct ActionInvoker<T0, T1, T2, T3, T4, T5, T6>(
            Action<T0, T1, T2, T3, T4, T5, T6> action,
            T0 arg0,
            T1 arg1,
            T2 arg2,
            T3 arg3,
            T4 arg4,
            T5 arg5,
            T6 arg6) : IActionInvoker
        {
            public void Invoke()
            {
                m_action(m_arg0, m_arg1, m_arg2, m_arg3, m_arg4, m_arg5, m_arg6);
            }

            private Action<T0, T1, T2, T3, T4, T5, T6> m_action = action;
            private T0 m_arg0 = arg0;
            private T1 m_arg1 = arg1;
            private T2 m_arg2 = arg2;
            private T3 m_arg3 = arg3;
            private T4 m_arg4 = arg4;
            private T5 m_arg5 = arg5;
            private T6 m_arg6 = arg6;
        }

        #endregion
    }
}
