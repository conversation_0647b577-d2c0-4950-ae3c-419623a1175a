/*
// TypeRegistry.cs
using MessagePack;
using MessagePack.Formatters;

namespace Phoenix.Server.Common
{
    // ObjectListFormatter.cs


    public class ObjectListFormatter : IMessagePackFormatter<List<object>>
    {
        public void Serialize(ref MessagePackWriter writer, List<object> value, MessagePackSerializerOptions options)
        {
            if (value == null)
            {
                writer.WriteNil();
                return;
            }

            writer.WriteArrayHeader(value.Count);
            foreach (var item in value)
                MessagePackSerializer.Serialize(ref writer, item, options);
        }

        public List<object> Deserialize(ref MessagePackReader reader, MessagePackSerializerOptions options)
        {
            if (reader.TryReadNil()) return null;

            var count = reader.ReadArrayHeader();
            var list = new List<object>(count);
            for (int i = 0; i < count; i++)
                list.Add(MessagePackSerializer.Deserialize<object>(ref reader, options));
            return list;
        }
    }
}
*/
