using MessagePack;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Server.Common
{
    public static class RpcRequestInfoExtensions
    {
        public static void SendResponse(this RpcRequestInfo rpcReq, MsgPackStructBase message, int retCode = 0)
        {
            RpcService.Instance.SendRpcResponse(rpcReq, message, retCode);
        }
        public static void SendResponse(this RpcRequestInfo rpcReq, object parameters, RpcErrCode retCode = RpcErrCode.RPC_NO_ERROR)
        {
            byte[] packedData = MessagePackSerializer.Serialize(parameters);
            RpcService.Instance.SendRpcResponse(rpcReq, packedData, retCode);
        }
    }

    public class RpcRequestInfo
    {
        public string CorrelationId = "";
        public uint NodeId;
        public uint HiddenNodeId;
        public request_id_t ReqId;
        public bool NeedResponse;
        public bool IsMailboxRequest;

        public override string ToString()
        {
            return $"RpcRequestInfo[NodeId={NodeId} HiddenNodeId={HiddenNodeId} ReqId={ReqId} NeedResponse={NeedResponse} IsMailboxRequest={IsMailboxRequest}]";
        }
    }
}
