using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Utils;

namespace Phoenix.Server.Common
{
    public class ConnectionManager
    {
        public void Start() { }

        public void Stop() { }

        private int Count()
        {
            return this.ConnId2Connection.Count;
        }

        public void RegConnection(NodeTcpConnection conn, connid_t assigned_id)
        {
            if (conn.ConnId == 0)
            {
                connid_t conn_id = assigned_id == 0 ? NextConnectionId() : assigned_id;
                conn.SetConnId(conn_id);
            }

            if (ConnId2Connection.ContainsKey(conn.GetConnId()))
            {
                ConnId2Connection.Remove(conn.GetConnId(), out _);
            }

            ConnId2Connection.TryAdd(conn.GetConnId(), conn);

            conn.SetManager(this);
        }

        public void UnRegConnection(NodeTcpConnection conn)
        {
            if (ConnId2Connection.ContainsKey(conn.GetConnId()))
            {
                ConnId2Connection.Remove(conn.GetConnId(), out _);
            }
        }

        public Connection? FindConnection(connid_t connId)
        {
            ConnId2Connection.TryGetValue(connId, out var conn);
            return conn;
        }

        internal static uint NextConnectionId()
        {
            return (uint)ConnIdGenerator.GetNextSequenceId();
        }

        internal void NewConnect(string ip, uint port, uint connId, uint nodeId)
        {
            var conntion = new NodeTcpConnection(ip, (ushort)port, connId)
            {
                NodeId = nodeId
            };
            ConnId2Connection.TryAdd(connId, conntion);
            try
            {
                Logger.LogInformation("ConnectNodeBegin, ConnId={ConnId}, TargetNodeId={TargetNodeId}, Ip={TargetIp}, Port={TargetPort}", connId, nodeId, ip, port);
                RegConnection(conntion, connId);
                conntion.Connect();
            }
            catch (Exception ex)
            {
                Logger.LogError("ConnectNodeBegin failed exception={ex}", ex);
            }
        }

        internal void DoCloseConnection(uint connId)
        {
            if (ConnId2Connection.TryGetValue(connId, out var conn))
            {
                conn.Disconnect(ConnDisconnectReason.DoCloseConnection);
            }
        }

        public void RegisterReadCallback(Action<NodeTcpConnection, MsgPackStructBase> func)
        {
            cbHandleRead = func;
        }

        public void RegisterCloseCallback(Action<NodeTcpConnection> func)
        {
            cbHandleClose = func;
        }

        public void RegisterConnectedCallback(Action<NodeTcpConnection> func)
        {
            cbHandleConnected = func;
        }

        public void RegisterConnectFailCallback(Action<NodeTcpConnection> func)
        {
            cbHandleConnectFail = func;
        }

        public void RegisterAcceptedCallback(Action<NodeTcpConnection> func)
        {
            cbHandleAccepted = func;
        }

        public ConnManagerStatus Status { get; private set; } = ConnManagerStatus.INITED;

        private static readonly ILogger Logger = InternalLoggerFactory.LoggerFactory.CreateLogger<ConnectionManager>();

        private readonly ConcurrentDictionary<connid_t, NodeTcpConnection> ConnId2Connection = new();

        private static readonly SequenceIntGenerator ConnIdGenerator = new(0);

        public Action<NodeTcpConnection, MsgPackStructBase>? cbHandleRead;
        public Action<NodeTcpConnection>? cbHandleClose;
        public Action<NodeTcpConnection>? cbHandleConnected;
        public Action<NodeTcpConnection>? cbHandleConnectFail;
        public Action<NodeTcpConnection>? cbHandleAccepted;

        public enum ConnManagerStatus
        {
            INITED = 0,
            STARTED,
            STOPED
        };
    }
}
