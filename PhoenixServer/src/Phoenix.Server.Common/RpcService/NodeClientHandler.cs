using DotNetty.Transport.Channels;
using Microsoft.Extensions.Logging;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Server.Common
{
    public class NodeClientHandler : ChannelHandlerAdapter
    {
        public override void ChannelActive(IChannelHandlerContext context)
        {
            // conn = context.GetAttribute(NetWorkManager.KeyNodeTcpConnection).Get();
            // Logger.LogInformation("NodeClientHandler.ChannelActive");
        }

        public override void ChannelInactive(IChannelHandlerContext context)
        {
            if (conn != null)
            {
                conn.OnDisconnect();
            }
        }

        public override void ChannelRead(IChannelHandlerContext context, object message)
        {
            if(conn == null)
            {
                conn = context.GetAttribute(NetWorkManager.KeyNodeTcpConnection).Get();
            }

            if (conn != null)
            {
                conn.OnRead((MsgPackStructBase)message);
            }
        }

        public override void ChannelReadComplete(IChannelHandlerContext context) => context.Flush();

        public override void ExceptionCaught(IChannelHandlerContext context, Exception exception)
        {
            context.CloseAsync();
            if (conn != null)
            {
                conn.OnException(exception);
            }
        }

        private static readonly ILogger Logger = InternalLoggerFactory.LoggerFactory.CreateLogger<NodeClientHandler>();
        private NodeTcpConnection? conn = null;
    }
}
