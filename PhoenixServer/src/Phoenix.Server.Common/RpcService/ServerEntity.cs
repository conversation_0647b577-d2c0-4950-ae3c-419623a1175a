using Microsoft.Extensions.Logging;

namespace Phoenix.Server.Common
{
    public class ServerEntity : Entity
    {

        public ServerEntity() : base()
        {

        }
        // DBCollectionBasePlayer
        // CollectionName
        public virtual void InitFromDB(object playerData) { }

        public void AddTypeTag(EObjectTypeTag typeTag)
        {
            TypeTag |= (int64_t)typeTag;
        }

        public bool IsBasePlayer()
        {
            return (TypeTag & (long)EObjectTypeTag.ETAG_BASE_PLAYER) != 0;
        }

        public virtual void Destroy()
        {
            if (IsDestroy())
            {
                Logger.LogError("entity already destroyed eid={eid}", EntityId);
                return;
            }

            ent_state = EntityState.ENT_ST_DESTROYING;

            if(EventOnPreDestroy != null)
            {
                EventOnPreDestroy.Invoke(this);
            }

            PreDestroy();

            // unregister mailbox
            if (!string.IsNullOrEmpty(MailboxName))
            {
                RpcManager.Instance.UnRegisterMailBox(MailboxName);
            }

            /*SyncComponent.flush_dirty_prop();
            enable_prop_persist(false);
            enable_prop_sync(false);

            ent_state = EntityState.ENT_ST_DESTROYING;

            EventPreDestroy.Invoke();




            // clear all pending timer in current entity instance
            EntityAdmin.Instance.entityTimer.clear_object_timer(EntityId);
            if(!is_migrating && !is_ghost)
            {
                persist
            }*/
        }

        private void PreDestroy()
        {
        }

        public bool IsDestroy()
        {
            return IsDestroying() || IsDestroyed();
        }

        private bool IsDestroyed()
        {
            return ent_state == EntityState.ENT_ST_DESTROYED;
        }

        private bool IsDestroying()
        {
            return ent_state == EntityState.ENT_ST_DESTROYING;
        }

        private static ILogger Logger = InternalLoggerFactory.LoggerFactory.CreateLogger<ServerEntity>();

        public EntityState ent_state;

        public long TypeTag { get; set; }

        private string? MailboxName;

        public event Action<ServerEntity>? EventOnPreDestroy;
        public event Action<ServerEntity>? EventOnDestroy;
    }
}
