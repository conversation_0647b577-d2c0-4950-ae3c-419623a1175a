using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Utils;

namespace Phoenix.Server.Common
{
    public delegate void NodeJoinCallback(MsgPack_S_ServerNodeInfo_Proto nodeInfo);

    public delegate void NodeLeaveCallback(MsgPack_S_ServerNodeInfo_Proto nodeInfo);

    public class ServerNodeManager
    {
        public ServerNodeManager(IOptions<ServerOptions> serverOptions, IOptions<ClusterOptions> clusterOptions)
        {
            ServerOptions = serverOptions.Value;
            ClusterOptions = clusterOptions.Value;

            // Init MyNode
            var nodeInfo = new MsgPack_S_ServerNodeInfo_Proto
            {
                NodeId = ServerOptions.NodeId,
                Role = ServerOptions.Role,
                Ip = ServerOptions.Ip,
                Port = ServerOptions.Port,
                Tag = ServerOptions.Tag
            };
            MyNode = AddNode(nodeInfo);

            m_mailboxManager = new(this);
            m_roleRequireNum = ClusterOptions.RoleRequireNum;
        }

        public int Init(AsyncDispatcher dispatcher)
        {
            m_asyncDispatcher = dispatcher;
            m_lastTickTime = CurrentMilliseconds;

            if (ServerOptions.ServerHeartbeatInterval != 0 &&
                ServerOptions.ServerHeartbeatTimeout != 0)
            {
                ServerNode.kHeartbeatInterval = ServerOptions.ServerHeartbeatInterval * 1000;
                ServerNode.kHeartbeatTimeout = ServerOptions.ServerHeartbeatTimeout * 1000;

                Logger.LogInformation(
                    "SetHeartbeatArgs, ServerHeartbeatInterval={ServerHeartbeatInterval}s, ServerHeartbeatTimeout={ServerHeartbeatTimeout}s",
                    ServerOptions.ServerHeartbeatInterval, ServerOptions.ServerHeartbeatTimeout);
            }

            if (ServerOptions.Role != RoleName.ROLE_ROUTER)
            {
                m_roleNeedConnect.Add(RoleName.ROLE_ROUTER);
            }

            if (ServerOptions.Role == RoleName.ROLE_GAME)
            {
                m_roleNeedConnect.Add(RoleName.ROLE_GATE);
            }
            else if (ServerOptions.Role == RoleName.ROLE_LOGIN)
            {
                m_roleNeedConnect.Add(RoleName.ROLE_GATE);
            }

            // TODO move SecureParamHash to config
            // SecureParamHash = 0;
            Logger.LogInformation("InitMyNodeInfo, Ip={Ip}, Port={port}, Role={Role}, NodeId={NodeId}",
                MyNode.NodeInfo.Ip, MyNode.NodeInfo.Port, MyNode.NodeInfo.Role, MyNode.NodeInfo.NodeId);

            OnNodeJoin(MyNode.NodeInfo);

            Logger.LogInformation("InitServerNodeMgr, MyRole={Role}", ServerOptions.Role);

            InitNetWorkers();
            InitMasterCommander();
            RegisterMessageHandlers();
            InitConsistentHash();
            /*ENGINE_ENV.RegisterCallbackOnEnvChange("watch_commander_change", [this](){
                InitMasterCommander();
            });*/
            // Engine.Instance.ConfigLoader.EventOnConfigChange += InitMasterCommander;
            IsInited = true;
            return 0;
        }

        private void InitNetWorkers()
        {
            m_callbacks.OnHandleConnected = (conn) =>
            {
                Logger.LogTrace("OnConnected, ConnId={ConnId}, Ip={Ip}, Port={Port}", conn.ConnId, conn.PeerIp,
                    conn.PeerPort);
                m_asyncDispatcher.Post(OnConnected, conn.NodeId, true);
            };

            m_callbacks.OnHandleConnectFail = (conn) =>
            {
                Logger.LogError("OnConnectFail, ConnId={ConnId}, NodeId={NodeId} Ip={Ip}, Port={Port}", conn.ConnId, conn.NodeId, conn.PeerIp,
                    conn.PeerPort);
                m_asyncDispatcher.Post(OnConnected, conn.NodeId, false);
            };

            m_callbacks.OnHandleRead = (conn, message) =>
            {
                if (m_messageHandlers.TryGetValue((int)message.ProtoCode, out var messageHandler))
                {
                    m_asyncDispatcher.Post(messageHandler, conn, message);
                }
            };

            m_callbacks.OnHandleClose = (conn) =>
            {
                Logger.LogDebug("OnConnClose, ConnId={ConnId}, Ip={Ip}, Port={Port}", conn.ConnId, conn.PeerIp,
                    conn.PeerPort);
                m_asyncDispatcher.Post(OnConnClose, conn.ConnId, conn.NodeId);
            };

            m_callbacks.OnHandleAccepted = (conn) =>
            {
                m_asyncDispatcher.Post(OnAcceptNewConnection, conn.ConnId);
            };

            ConnectionManager.RegisterConnectedCallback(m_callbacks.OnHandleConnected);
            ConnectionManager.RegisterConnectFailCallback(m_callbacks.OnHandleConnectFail);
            ConnectionManager.RegisterReadCallback(m_callbacks.OnHandleRead);
            ConnectionManager.RegisterCloseCallback(m_callbacks.OnHandleClose);
            ConnectionManager.RegisterAcceptedCallback(m_callbacks.OnHandleAccepted);
            NetWorkManager.Instance.Init();
            if (ServerOptions.Port != 0)
            {
                NetWorkManager.Instance.StartListen(ServerOptions.Port, ConnectionManager);
            }
        }

        public virtual void OnMasterSwitch(MsgPack_S_ServerNodeInfo_Proto masterNodeInfo)
        {
            var oldMaster = MasterCommander;
            MasterCommander = AddNode(masterNodeInfo);
            if (oldMaster != null && EventOnCommanderSwitch != null)
            {
                EventOnCommanderSwitch.Invoke(MasterCommander.NodeInfo.NodeId);
            }
        }

        private void OnAcceptNewConnection(uint connId)
        {
            Logger.LogTrace("OnAcceptNewConnection, ConnId={ConnId}", connId);
        }

        protected virtual void RegisterMessageHandlers()
        {
            void handle_RPC_NODE_CONNECT(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_RpcConnect_Proto? rpcConnect = message as MsgPack_S_RpcConnect_Proto;
                if (rpcConnect == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                connid_t connId = conn.ConnId;
                var nodeInfo = rpcConnect.NodeInfo;
                if (!VerifyNodeConnectRequest(rpcConnect, nodeInfo))
                {
                    Logger.LogError("VerifyNodeConnectRequest failed Conn={ConnId}", connId);
                    return;
                }

                if (conn.NodeId == 0)
                {
                    conn.NodeId = nodeInfo.NodeId;
                }

                HandleConnectRequest(connId, nodeInfo);
            }

            void handle_NODE_READY(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_NodeReady_Proto? nodeReady = message as MsgPack_S_NodeReady_Proto;

                if (nodeReady == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                Logger.LogInformation("handle_NODE_READY ConnId={ConnId} NodeId={NodeId}", conn.ConnId, nodeReady.NodeId);

                connid_t connId = conn.ConnId;
                HandleNodeReady(connId, nodeReady.NodeId);
            }


            Action<NodeTcpConnection, MsgPackStructBase> handle_NODES_READY = (conn, message) =>
            {
                MsgPack_S_NodesReady_Proto? nodesReady = message as MsgPack_S_NodesReady_Proto;
                if (nodesReady == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                connid_t connId = conn.ConnId;
                if (nodesReady.ReadyNodeList.Count == 0)
                {
                    return;
                }

                Logger.LogInformation("handle_NODES_READY ConnId={ConnId} NodeIds={NodeIds}", connId, nodesReady.ReadyNodeList.JoinWithSeparator());

                var nodeIdList = nodesReady.ReadyNodeList;
                foreach (connid_t nodeId in nodeIdList)
                {
                    HandleNodeReady(connId, nodeId);
                }
            };

            void handle_RPC_ESTABLISH(NodeTcpConnection conn, MsgPackStructBase message)
            {
                var msg = message as MsgPack_S_RpcEstablish_Proto;

                if (msg == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                uint nodeId = msg.NodeId;
                uint connId = conn.ConnId;
                if (msg.ErrorCode != 0)
                {
                    Logger.LogWarning(
                        "RpcConnectRejected, ConnId={ConnId}, NodeId={NodeId}, Ip={Ip}, Port={Port}, errCode={errCode}",
                        connId, nodeId, conn.RemoteIp(), conn.RemotePort(), msg.ErrorCode);
                    conn.Disconnect(ConnDisconnectReason.RpcConnectRejected);
                    return;
                }

                if (conn.NodeId != 0 && conn.NodeId != nodeId)
                {
                    Logger.LogWarning(
                        "RecvRpcEstablish failed, node_id not match. ConnId={ConnId}, require={require}, recv={recv}",
                        connId, conn.NodeId, nodeId);
                    conn.Disconnect(ConnDisconnectReason.RecvRpcEstablishNodeIdNotMatch);
                    return;
                }

                if (conn.NodeId == 0)
                {
                    conn.NodeId = nodeId;
                }

                m_asyncDispatcher.Post(() =>
                {
                    // move NodeInfo
                    HandleNodeEstablish(connId, nodeId);
                });
            }

            void handle_RPC_NODE_LIST(NodeTcpConnection conn, MsgPackStructBase message)
            {
                var msg = message as MsgPack_S_NodeInfoList_Proto;

                if (msg == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                var nodeInfoList = msg.NodeInfoList;
                Logger.LogInformation("handle_RPC_NODE_LIST, NodeListSize={NodeListSize}", nodeInfoList.Count);
                foreach (var node in nodeInfoList)
                {
                    HandleNodeJoin(conn.ConnId, node);
                }
            }

            void handle_RPC_NODE_JOIN(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_NodeJoin_Ntf? msg = message as MsgPack_S_NodeJoin_Ntf;
                if (msg == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                HandleNodeJoin(conn.ConnId, msg.NodeInfo);
            }

            void handle_RPC_NODE_LEAVE(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_NodeLeave_Ntf? msg = message as MsgPack_S_NodeLeave_Ntf;

                if (msg == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                HandleNodeLeave(conn.ConnId, msg.NodeId);
            }

            void handle_RPC_TO_NODE(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_RpcToNode_Proto? msg = message as MsgPack_S_RpcToNode_Proto;

                if (msg == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                if (msg.DestNodeId != MyNode.NodeInfo.NodeId)
                {
                    Logger.LogWarning("ForwardRpcError, MyRole={role} DestNodeId={DestNodeId} MyNodeId={NodeId}",
                        MyNode.NodeInfo.Role, msg.DestNodeId, MyNode.NodeInfo.NodeId);
                    return;
                }

                // handle rpc request
                MsgPack_S_Rpc_Req req = msg.RpcReq;
                if (ServerOptions.Role == RoleName.ROLE_GATE && (req.Flag & RpcFlag.RPC_FOWRARD_TO_CLIENT) != 0)
                {
                    var msg2Client = MsgPackProtoHelper.Deserialize(req.MsgProtoCode, req.Data) as ForwardMessageToClientNtf;
                    if (msg2Client != null)
                    {
                        if (HandleServerForwardClientMsgFunc == null)
                        {
                            Logger.LogError("SendToClientFunc is null");
                            return;
                        }

                        /*var clientMsg = MsgPackProtoHelper.Deserialize(msg2Client.MsgProtoCode, msg2Client.Data);
                        if (clientMsg == null)
                        {
                            Logger.LogError("clientMsg is null MsgProtoCode={ProtoCode}", msg2Client.MsgProtoCode);
                            return;
                        }*/

                        HandleServerForwardClientMsgFunc(msg2Client.PlayerId, msg2Client);
                        return;
                    }
                }

                // main_dispatcher_->Post([this, req=std::move(req)]() mutable {
                HandleRpcRequest(req);
                //});
            }

            void handle_RPC_RESPONSE(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_Rpc_Ack? msg = message as MsgPack_S_Rpc_Ack;

                if (msg == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                if (msg.ReqNode != MyNode.NodeInfo.NodeId)
                {
                    Logger.LogWarning("ForwardRpcResponseError, MyRole={Role}", MyNode.NodeInfo.NodeId);
                    return;
                }

                // handle rpc response
                uint respNodeId = msg.ReqNode;
                HandleRpcResponse(respNodeId, msg);
            }

            void handle_RPC_TO_PLAYER(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_RpcToPlayer_Proto? msg = message as MsgPack_S_RpcToPlayer_Proto;

                if (msg == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                HandleRpcRequestToPlayer(msg);
            }

            void handle_S_UPDATE_CONSISTENT_HASH(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_UpdateConsistentHash_Proto? msg = message as MsgPack_S_UpdateConsistentHash_Proto;

                if (msg == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                Logger.LogInformation("handle_S_UPDATE_CONSISTENT_HASH, Role={Role}", msg.Role);

                HandleConsistentHashUpdate(msg);
            }

            void handle_RPC_HEARTBEAT(NodeTcpConnection conn, MsgPackStructBase message)
            {
                //sendResponse
                ServerNode? node = FindNode(conn.NodeId);
                if (node == null)
                {
                    Logger.LogError("handle_RPC_HEARTBEAT node is null Conn={ConnId} NodeId={NodeId}", conn.ConnId,
                        conn.NodeId);
                    conn.Disconnect(ConnDisconnectReason.HeartbeatDotFindNode);
                    return;
                }

                MsgPack_S_NodeHeartbeat_Ack hb = new() { Timestamp = CurrentMilliseconds };
                DirectSendMsg(conn.ConnId, hb);

                // Logger.LogTrace("handle_RPC_HEARTBEAT conn={ConnId}", conn.NodeId);
                node.SetLastHeartbeatTime(CurrentMilliseconds);
            }

            // TODO heartbeat
            void handle_RPC_HEARTBEAT_RESP(NodeTcpConnection conn, MsgPackStructBase message)
            {
                ServerNode? node = FindNode(conn.NodeId);
                if (node == null)
                {
                    Logger.LogError("handle_RPC_HEARTBEAT_RESP node is null Conn={ConnId} NodeId={NodeId}", conn.ConnId,
                        conn.NodeId);
                    conn.Disconnect(ConnDisconnectReason.HeartbeatRespDotFindNode);
                    return;
                }

                // Logger.LogTrace("handle_RPC_HEARTBEAT_RESP conn={ConnId}", conn.NodeId);
                node.SetLastHeartbeatTime(CurrentMilliseconds);
            }

            void handle_S_CONSISTENT_HASH_CHECK(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_CheckConsistentDigest_Proto? msg = message as MsgPack_S_CheckConsistentDigest_Proto;

                if (msg == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                // Logger.LogTrace("[RPC]handle_S_CONSISTENT_HASH_CHECK CheckConsistentHash, NodeId={NodeId} PeerDigest={PeerDigest}",
                //    conn.NodeId, msg.Digests.Select(e => e.Digest).JoinWithSeparator());

                var nodeDigests = msg.Digests;
                if (nodeDigests.Count == 0)
                {
                    Logger.LogError("handle_S_CONSISTENT_HASH_CHECK nodeDigests is empty");
                    return;
                }

                foreach (var chashDigest in nodeDigests)
                {
                    var consistentHash = GetConsistentHashByRole(chashDigest.Role);
                    if (consistentHash == null)
                    {
                        continue;
                    }

                    if (consistentHash.Digest != chashDigest.Digest)
                    {
                        //consistent hash digest error, need to update consistent hash
                        Logger.LogWarning(
                            "ConsistentHash Digest Error! NodeId={NodeId} Role={role}, MyDigest={Digest}, PeerDigest={peerDigest}",
                            conn.NodeId, chashDigest.Role, consistentHash.Digest, chashDigest.Digest);
                        MsgPack_S_GetConsistentDigest_Proto getChash = new() { Role = chashDigest.Role };
                        DirectSendMsg(conn.ConnId, getChash);
                    }
                }
            }

            void handle_S_TRANS_UPDATE_CONSISTENT_HASH(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_TransUpdateConsistentHash_Req? msg = message as MsgPack_S_TransUpdateConsistentHash_Req;

                if (msg == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                Logger.LogInformation(
                    "[RPC]handle_S_TRANS_UPDATE_CONSISTENT_HASH ConnId={ConnId} NodeId={NodeId} Role={Role}, TransId={TransId}, State={State}",
                    conn.ConnId, conn.NodeId, msg.Role, msg.TransId, (ConsistentHashSyncState)msg.TransState);
                HandleConsistentHashTransUpdate(msg, conn.NodeId);
            }

            // TODO MailBox
            void handle_RESP_REGISTER_MAILBOX(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_RegisterMailBox_Ack? mailboxResp = message as MsgPack_S_RegisterMailBox_Ack;

                if (mailboxResp == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                m_mailboxManager.HandleRegisterResp(mailboxResp);
            }

            void handle_RESP_UNREGISTER_MAILBOX(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_UnregisterMailBox_Ack? mailboxResp = message as MsgPack_S_UnregisterMailBox_Ack;

                if (mailboxResp == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                m_mailboxManager.HandleUnRegisterRes(mailboxResp, conn.NodeId);
            }

            void handle_BROADCAST_DATA(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_BroadcastData_Proto? msg = message as MsgPack_S_BroadcastData_Proto;
                if (msg == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                if (RpcLogicCallback.OnRecvBroadcastData != null)
                {
                    RpcLogicCallback.OnRecvBroadcastData(msg);
                }
            }

            RegisterMessageHandler((int)EProtoCode.CORE_S_RPCCONNECT_PROTO, handle_RPC_NODE_CONNECT);
            RegisterMessageHandler((int)EProtoCode.CORE_S_NODEREADY_PROTO, handle_NODE_READY);
            RegisterMessageHandler((int)EProtoCode.CORE_S_NODESREADY_PROTO, handle_NODES_READY);
            RegisterMessageHandler((int)EProtoCode.CORE_S_RPCESTABLISH_PROTO, handle_RPC_ESTABLISH);
            RegisterMessageHandler((int)EProtoCode.CORE_S_NODEINFOLIST_PROTO, handle_RPC_NODE_LIST);
            RegisterMessageHandler((int)EProtoCode.CORE_S_NODEJOIN_NTF, handle_RPC_NODE_JOIN);
            RegisterMessageHandler((int)EProtoCode.CORE_S_NODELEAVE_NTF, handle_RPC_NODE_LEAVE);
            RegisterMessageHandler((int)EProtoCode.CORE_S_RPCTONODE_PROTO, handle_RPC_TO_NODE);
            RegisterMessageHandler((int)EProtoCode.CORE_S_RPC_ACK, handle_RPC_RESPONSE);
            RegisterMessageHandler((int)EProtoCode.CORE_S_RPCTOPLAYER_PROTO, handle_RPC_TO_PLAYER);
            RegisterMessageHandler((int)EProtoCode.CORE_S_UPDATECONSISTENTHASH_PROTO, handle_S_UPDATE_CONSISTENT_HASH);
            RegisterMessageHandler((int)EProtoCode.CORE_S_NODEHEARTBEAT_REQ, handle_RPC_HEARTBEAT);
            RegisterMessageHandler((int)EProtoCode.CORE_S_NODEHEARTBEAT_ACK, handle_RPC_HEARTBEAT_RESP);

            RegisterMessageHandler(MsgPackProtoHelper.GetProtoIdByType(typeof(MsgPack_S_CheckConsistentDigest_Proto)),
                handle_S_CONSISTENT_HASH_CHECK);
            RegisterMessageHandler(MsgPackProtoHelper.GetProtoIdByType(typeof(MsgPack_S_TransUpdateConsistentHash_Req)),
                handle_S_TRANS_UPDATE_CONSISTENT_HASH);

            RegisterMessageHandler(MsgPackProtoHelper.GetProtoIdByType(typeof(MsgPack_S_BroadcastData_Proto)),
                handle_BROADCAST_DATA);

            RegisterMessageHandler(MsgPackProtoHelper.GetProtoIdByType(typeof(MsgPack_S_RegisterMailBox_Ack)),
                handle_RESP_REGISTER_MAILBOX);
            RegisterMessageHandler(MsgPackProtoHelper.GetProtoIdByType(typeof(MsgPack_S_UnregisterMailBox_Ack)),
                handle_RESP_UNREGISTER_MAILBOX);
        }

        private void HandleRpcRequestToPlayer(MsgPack_S_RpcToPlayer_Proto msg)
        {
            RpcLogicCallback.OnRpcRequestToPlayer?.Invoke(msg);
        }

        protected virtual void HandleConnectRequest(uint connId, MsgPack_S_ServerNodeInfo_Proto nodeInfo)
        {
            uint myNodeId = MyNode.NodeInfo.NodeId;
            Action<int, string> respESTABLISH = (errorCode, errMsg) =>
            {
                MsgPack_S_RpcEstablish_Proto respMsg = new() { NodeId = myNodeId, ErrorCode = errorCode, };
                DirectSendMsg(connId, respMsg);
            };

            uint nodeId = nodeInfo.NodeId;
            string role = nodeInfo.Role;
            var node = AddNode(nodeInfo);
            if (node.ConnId != 0 && node.ConnId != connId)
            {
                if (node.State == ENodeConnState.CONNECTED || node.State == ENodeConnState.CONNECTING ||
                    node.IsActive())
                {
                    //do disconnect
                    respESTABLISH((int)ENodeErrno.REJECT_CONNECT_STATE,
                        $"Node state error!, reject connect, NodeState={node.State}");
                    Logger.LogWarning("CloseNodeConnect, PeerNode={PeerNode}", nodeId);
                    CloseNodeConnect(nodeId);
                    return;
                }
            }

            node.SetLastHeartbeatTime(CurrentMilliseconds); // set last_heartbeat_time to avoid disconnect
            //check can accept connect by topology rules
            string myRole = ServerOptions.Role;
            if (!AllowAcceptConnect(node))
            {
                respESTABLISH((int)ENodeErrno.REJECT_ROLE_CONNECT, "node_not_allowed_to_connect " + myRole);
                Logger.LogWarning(
                    "HandleConnectRequest reject. node_should_not_connect_me, MyRole={MyRole}, NodeId={NodeId}, Role={Role}",
                    myRole, nodeId, role);
                return;
            }

            node.ConnId = connId;
            AddNodeConnId(nodeId, connId);
            var oldState = node.SetState(ENodeConnState.ACTIVE);
            Logger.LogInformation(
                "HandleNodeConnect success, ConnId={ConnId}, NodeId={NodeId}, Role={Role}, OldState={OldState}", connId,
                nodeId, role, oldState);
            //send establish ack back
            respESTABLISH(0, "");
            //send pending send message
            FlushCache(node);

            if (RpcLogicCallback.OnNodeConnected != null)
            {
                RpcLogicCallback.OnNodeConnected(nodeId, role);
            }

            AddKeepAliveCheck(nodeId);
        }

        public void HandleRpcRequest(MsgPack_S_Rpc_Req req)
        {
            if (RpcLogicCallback.OnRpcRequest != null)
            {
                RpcLogicCallback.OnRpcRequest(req.SrcNode, req);
            }
        }

        protected void HandleRpcResponse(nodeid_t nodeId, MsgPack_S_Rpc_Ack resp)
        {
            if (RpcLogicCallback.OnRpcResponse != null)
            {
                RpcLogicCallback.OnRpcResponse(nodeId, resp);
            }
        }

        private void AddKeepAliveCheck(uint nodeId)
        {
            m_nodesFromAccept.Add(nodeId);
        }

        private bool AllowAcceptConnect(ServerNode node)
        {
            //TODO: commander can verify the server list or not
            return true;
        }

        private bool VerifyNodeConnectRequest(MsgPack_S_RpcConnect_Proto connMsg, MsgPack_S_ServerNodeInfo_Proto nodeInfo)
        {
            uint32_t nodeId = nodeInfo.NodeId;
            // verify magic number
            if (connMsg.Magic != kConnectMagic)
            {
                Logger.LogError(
                    "VerfifyNodeConnectRequest magic number not match. RecvMagic={RecvMagic}, ConfMagic={ConfMagic} NodeId={NodeId}",
                    connMsg.Magic, kConnectMagic, nodeId);
                return false;
            }

            // verify authentication token
            byte[] desiredAuthToken = GenerateNodeAuthToken(nodeId);
            if (!desiredAuthToken.SequenceEqual(connMsg.AuthToken))
            {
                Logger.LogWarning("VerifyNodeConnectRequest auth token not match. NodeId={NodeId}", nodeId);
                return false;
            }

            return true;
        }

        protected virtual void HandleNodeLeave(uint connId, uint nodeId)
        {
            Logger.LogInformation("HandleNodeLeave, ConnId={ConnId}, NodeId={NodeId}", connId, nodeId);
            if (MyNode.NodeInfo.NodeId == nodeId)
            {
                return;
            }

            ServerNode? node = FindNode(nodeId);
            if (node == null)
            {
                return;
            }

            if (RoleName2ServerNodeIds.TryGetValue(node.NodeInfo.Role, out var nodeIds))
            {
                nodeIds.Remove(nodeId);
            }

            // call leave callback
            if (Role2LeaveCallback.TryGetValue(node.NodeInfo.Role, out var roleLeaveCallback))
            {
                foreach (var pair in roleLeaveCallback)
                {
                    pair.Value(node.NodeInfo);
                }
            }

            if (RpcLogicCallback.OnNodeLeave != null)
            {
                RpcLogicCallback.OnNodeLeave(node.NodeInfo);
            }

            // clear node
            DelNode(nodeId);
            var conn = GetConnection(nodeId);
            if (conn != null)
            {
                conn.Disconnect(ConnDisconnectReason.NodeLeave);
            }

            DelNodeConnId(nodeId);
            Logger.LogInformation("[RPC]HandleNodeLeave, NodeId={NodeId} Role={Role}", nodeId, node.NodeInfo.Role);
        }

        private void DelNodeConnId(uint nodeId)
        {
            m_nodeId2ConnId.Remove(nodeId);
        }

        protected virtual void HandleNodeReady(uint connId, uint nodeId)
        {
            ServerNode? node = FindNode(nodeId);
            if (node == null)
            {
                Logger.LogWarning("HandleNodeReadyFailed, can't find node info, ConnId={ConnId} NodeId={NodeId}",
                    connId, nodeId);
                return;
            }

            node.SetLogicReady(true);
            // when node ready, we can send rpc to it
            Logger.LogDebug("HandleNodeReady, ConnId={ConnId} NodeId={NodeId}", connId, nodeId);
            // call on_node_ready callback
            if (RpcLogicCallback.OnNodeJoin != null)
            {
                RpcLogicCallback.OnNodeJoin(node.NodeInfo);
            }

            if (Role2JoinCallback.TryGetValue(node.NodeInfo.Role, out var roleJoinCallback))
            {
                foreach (var pair in roleJoinCallback)
                {
                    pair.Value(node.NodeInfo);
                }
            }
        }

        private void HandleConsistentHashTransUpdate(MsgPack_S_TransUpdateConsistentHash_Req msg, uint commanderNodeId)
        {
            if (msg.Role != MyNode.NodeInfo.Role)
            {
                return;
            }

            var transState = (ConsistentHashSyncState)msg.TransState;
            if (m_chashSyncData.m_commanderNodeId != commanderNodeId || m_chashSyncData.m_transId != msg.TransId)
            {
                if (transState != ConsistentHashSyncState.SYNC_HASH)
                {
                    return;
                }
            }

            switch (transState)
            {
                case ConsistentHashSyncState.INIT:
                    {
                        return;
                    }
                case ConsistentHashSyncState.SYNC_HASH:
                    {
                        m_chashSyncData.m_commanderNodeId = commanderNodeId;
                        m_chashSyncData.m_transId = msg.TransId;
                        m_chashSyncData.m_packedMsg = msg.ConsistentHashData;
                        break;
                    }
                case ConsistentHashSyncState.COMMIT:
                    {
                        var consistentHash = GetConsistentHashByRole(msg.Role);
                        if (consistentHash == null)
                        {
                            consistentHash = CreateConsistentHash(msg.Role);
                        }

                        System.Diagnostics.Debug.Assert(m_chashSyncData.m_packedMsg != null);
                        consistentHash.ParseSlotsFromString(m_chashSyncData.m_packedMsg, 0,
                            m_chashSyncData.m_packedMsg.Length);
                        break;
                    }
            }

            m_chashSyncData.m_transState = transState;

            MsgPack_S_TransUpdateConsistentHash_Ack ackMsg = new()
            {
                TransId = m_chashSyncData.m_transId, TransState = msg.TransState, Role = msg.Role
            };
            SendNode(commanderNodeId, ackMsg);
        }

        private void HandleNodeJoin(uint connId, MsgPack_S_ServerNodeInfo_Proto nodeInfo)
        {
            Logger.LogInformation("HandleNodeJoin, ConnId={ConnId}, NodeId={NodeId}", connId, nodeInfo.NodeId);
            if (FindNode(nodeInfo.NodeId) == null)
            {
                var node = AddNode(nodeInfo);
                OnNodeJoin(node.NodeInfo);
            }
        }

        protected virtual void HandleConsistentHashUpdate(MsgPack_S_UpdateConsistentHash_Proto msg)
        {
            ConsistentHash? consistentHash = GetConsistentHashByRole(msg.Role);
            if (consistentHash == null)
            {
                consistentHash = CreateConsistentHash(msg.Role);
            }

            consistentHash.ParseSlotsFromString(msg.ConsistentHashData, 0, msg.ConsistentHashData.Length);
        }

        private void HandleNodeEstablish(uint connId, uint nodeId)
        {
            ServerNode? node = FindNode(nodeId);
            if (node == null)
            {
                Logger.LogWarning("HandleNodeEstablish failed, node info not found, NodeId={NodeId}, ConnId={ConnId}",
                    nodeId, connId);
                return;
            }

            AddNodeConnId(nodeId, connId);
            node.SetState(ENodeConnState.ACTIVE);
            FlushCache(node);
            string role = node.NodeInfo.Role;
            Logger.LogInformation("HandleNodeEstablish success, ConnId={ConnId}, NodeId={NodeId}, Role={Role}", connId,
                nodeId, role);
            if (RpcLogicCallback.OnNodeConnected != null)
            {
                RpcLogicCallback.OnNodeConnected(nodeId, role);
            }
        }

        private void FlushCache(ServerNode node)
        {
            connid_t connId = node.ConnId;
            if (!node.IsSendCacheEmpty())
            {
                Logger.LogInformation("[RPC]SendPendingMsg, NodeId={NodeId}", node.NodeInfo.NodeId);
                foreach (var pendingMsg in node.GetSendCache())
                {
                    DirectSendMsg(connId, pendingMsg);
                }

                node.ClearSendCache();
            }
        }

        public void RegisterMessageHandler(int protoCode, Action<NodeTcpConnection, MsgPackStructBase> handler)
        {
            m_messageHandlers[protoCode] = handler;
        }

        private readonly Dictionary<int, Action<NodeTcpConnection, MsgPackStructBase>> m_messageHandlers = new();

        protected virtual void OnConnClose(uint connId, uint nodeId)
        {
            ServerNode? node = FindNode(nodeId);
            if (node == null)
            {
                Logger.LogDebug("OnConnClose, node is not found, NodeId={NodeId}, ConnId={ConnId}", nodeId, connId);
                return;
            }

            node.SetState(ENodeConnState.DISCONNECT);
            node.StopSendHeartBeat();
            Logger.LogDebug("OnConnClose, ConnId={ConnId} NodeId={NodeId}", connId, nodeId);
            m_nodesFromAccept.Remove(nodeId);
        }

        private void OnConnected(nodeid_t nodeId, bool isSuccess)
        {
            ServerNode? node = FindNode(nodeId);
            if (node == null)
            {
                return;
            }

            MsgPack_S_ServerNodeInfo_Proto nodeInfo = node.NodeInfo;
            if (!isSuccess)
            {
                Logger.LogWarning("RpcNodeConnect failed, NodeId={NodeId}, Role={Role}, Ip={Ip}, Port={Port}", nodeId,
                    nodeInfo.Role, nodeInfo.Ip, nodeInfo.Port);
                return;
            }

            node.SetState(ENodeConnState.CONNECTED);
            ConnectingNodes.Remove(nodeId);

            //send RPC_NODE_CONNECT
            MsgPack_S_RpcConnect_Proto connMsg = new()
            {
                Magic = kConnectMagic, AuthToken = GenerateNodeAuthToken(ServerOptions.NodeId)
            };

            connMsg.NodeInfo = MyNode.NodeInfo;
            connMsg.ProcId = 0;
            connMsg.IsReady = MyNode.NodeInfo.IsReady;
            DirectSendMsg(node.ConnId, connMsg);
            Logger.LogInformation("RpcNodeConnected, ConnId={ConnId}, NodeId={NodeId},", node.ConnId, nodeId);
            // begin to send heartbeat
            BeginSendHeartBeat(node);
        }

        private void BeginSendHeartBeat(ServerNode node)
        {
            Task.Run(async () =>
            {
                while (node.IsKeepAlive)
                {
                    RpcService.Instance.AsyncDispatcher.Post(() =>
                    {
                        // Logger.LogTrace("send heartbeat, connId={ConnId}, nodeId={NodeId}", ConnId, NodeInfo.NodeId);
                        try
                        {
                            MsgPack_S_NodeHeartbeat_Req hb = new() { Timestamp = RpcService.Instance.CurrentMilliseconds };
                            var conn = ConnectionManager.FindConnection(node.ConnId);
                            if (conn == null)
                            {
                                Logger.LogInformation(
                                    "conn is null, connId={ConnId}, NodeId={NodeId} Role={Role} StopKeepAlive", node.ConnId,
                                    node.NodeInfo.NodeId, node.NodeInfo.Role);
                                node.IsKeepAlive = false;
                                return;
                            }

                            conn.WriteData(hb);
                        }
                        catch (Exception e)
                        {
                            Logger.LogError(e, "send heartbeat error, connId={ConnId}, nodeId={NodeId}", node.ConnId,
                                node.NodeInfo.NodeId);
                            node.IsKeepAlive = false;
                        }
                    });
                    await Task.Delay((int)ServerNode.kHeartbeatInterval);
                }
            });
        }

        protected void DirectSendMsg(uint connId, MsgPackStructBase connMsg)
        {
            if (!IsInited)
            {
                return;
            }

            var conn = ConnectionManager.FindConnection(connId);
            if (conn != null)
            {
                conn.WriteData(connMsg);
            }
            else
            {
                Logger.LogWarning("[RPC]DirectSendMsg fail conn not found, ConnId={ConnId} Msg={Msg},", connId,
                    connMsg);
            }
        }

        private byte[] GenerateNodeAuthToken(uint nodeId)
        {
            uint32_t authNum = (uint)((SecureParamHash ^ kConnectMagic ^ (nodeId * 83)) + 1018);
            byte[] authNumBytes = BitConverter.GetBytes(authNum);
            return authNumBytes;
        }

        private void InitConsistentHash()
        {
            //init empty table for roles that need consistent hash
            string roleStr = ClusterOptions.ConsistentHashRole;
            string[] roles = roleStr.Split(',');
            foreach (string role in roles.Select(x => x.Trim()))
            {
                CreateConsistentHash(role);
            }
        }

        protected ConsistentHash CreateConsistentHash(string role)
        {
            Logger.LogInformation("[RPC]CreateConsistentHash, Role={Role}", role);
            var consistentHash = new ConsistentHash(role);
            Role2ConsistentHash.Add(role, consistentHash);
            return consistentHash;
        }

        protected virtual void InitMasterCommander()
        {
            if (RpcService.UseConsul())
            {
                // get commander info from consul
                return;
            }

            nodeid_t commanderNodeId = ClusterOptions.CommanderNodeId;
            string commanderIp = ClusterOptions.CommanderIp;
            // TODO check if commander node valid
            if (commanderNodeId == 0 && string.IsNullOrEmpty(commanderIp))
            {
                return;
            }

            ushort commanderPort = ClusterOptions.CommanderPort;
            if (MasterCommander != null)
            {
                // check if has commander and commander ip/port changed
                var nodeInfo = MasterCommander.NodeInfo;
                if (nodeInfo.Ip == commanderIp && nodeInfo.Port == commanderPort)
                {
                    Logger.LogInformation(
                        "InitCommanderInfo, commander_no_change, NodeId={NodeId}, Ip={Ip}, Port={Port}",
                        commanderNodeId, commanderIp, commanderPort);
                    return;
                }

                if (MasterCommander.IsConnected())
                {
                    CloseNodeConnect(MasterCommander.NodeInfo.NodeId);
                }
            }

            var oldCommander = MasterCommander;
            AddNode(commanderNodeId, RoleName.ROLE_COMMANDER, commanderIp, commanderPort, ServerOptions.Tag);
            MasterCommander = FindNode(commanderNodeId)!;
            Logger.LogInformation("InitCommanderInfo, NodeId={commanderNodeId}, Ip={commanderIp}, Port={commanderPort}",
                commanderNodeId, commanderIp, commanderPort);
            OnNodeJoin(MasterCommander.NodeInfo);
            if (oldCommander != null && EventOnCommanderSwitch != null)
            {
                EventOnCommanderSwitch.Invoke(MasterCommander.NodeInfo.NodeId);
            }
        }

        protected void CloseNodeConnect(uint nodeId)
        {
            ConnectingNodes.Remove(nodeId);
            var node = FindNode(nodeId);
            if (node != null)
            {
                CloseConnection(node.ConnId);
                Logger.LogInformation("Connecting CloseNodeConnection, NodeId={nodeId}", nodeId);
            }
        }

        protected void OnNodeJoin(MsgPack_S_ServerNodeInfo_Proto nodeInfo)
        {
            Logger.LogInformation("OnNodeJoin, NodeId={NodeId}, Role={Role}, Ip={Ip}, Port={Port}",
                nodeInfo.NodeId, nodeInfo.Role, nodeInfo.Ip, nodeInfo.Port);
            InsertNodeInList(nodeInfo.Role, nodeInfo.NodeId);
        }

        private void InsertNodeInList(string role, uint nodeId)
        {
            if (!RoleName2ServerNodeIds.ContainsKey(role))
            {
                RoleName2ServerNodeIds.Add(role, new List<uint> { nodeId });
            }
            else
            {
                RoleName2ServerNodeIds[role].Add(nodeId);
                RoleName2ServerNodeIds[role].Sort();
            }
        }

        public int Shutdown()
        {
            if (IsShutdown)
            {
                return 0;
            }

            IsShutdown = true;
            // send node_leave
            MsgPack_S_NodeLeave_Ntf nodeLeave = new();

            nodeLeave.NodeId = MyNode.NodeInfo.NodeId;
            if (MasterCommander != null)
            {
                SendNode(MasterCommander, nodeLeave);
            }

            // stop all timers
            ShutdownNetWorkers();
            return 0;
        }

        private void ShutdownNetWorkers()
        {
            NetWorkManager.Instance.Stop().Wait();
        }

        protected void SendNode(ServerNode node, MsgPackStructBase msg)
        {
            if (node.IsConnected())
            {
                DirectSendNode(node, msg);
            }
            else
            {
                SendNode(node.NodeInfo.NodeId, msg);
            }
        }

        private void DirectSendNode(ServerNode node, MsgPackStructBase msg)
        {
            if (node.IsConnected())
            {
                DirectSendMsg(node.ConnId, msg);
                return;
            }

            //cache for a while
            if (!node.AppendToSendCache(msg))
            {
                Logger.LogWarning("SendNodeFailed, cache_is_full, TargetNodeId={TargetNodeId}", node.NodeInfo.NodeId);
                node.ClearSendCache();
            }
        }

        public bool SendNode(nodeid_t nodeId, MsgPackStructBase msg)
        {
            nodeid_t myNodeId = ServerOptions.NodeId;
            if (nodeId == myNodeId)
            {
                Logger.LogWarning("SendToMyself, ProtoCode={ProtoCode}", msg.ProtoCode);
                return true;
            }

            var node = FindNode(nodeId);
            if (node == null)
            {
                Logger.LogWarning("SendNodeFailed, can't_find_node_info, NodeId={NodeId}", nodeId);
                return false;
            }

            if (IsNeedDirectConnected(node))
            {
                DirectSendNode(node, msg);
            }
            else
            {
                // if not directly connected, find a router to forward rpc
                var router = PickRouter(node);
                if (router == null)
                {
                    Logger.LogWarning("SendNodeFailed, can't_find_router_to_forward_rpc, NodeId={NodeId}", nodeId);
                    return false;
                }

                DirectSendNode(router, msg);
            }

            return true;
        }

        protected bool IsNeedDirectConnected(ServerNode node)
        {
            if (node.IsConnected())
            {
                return true;
            }

            string MyRole = ServerOptions.Role;
            if (MyRole == RoleName.ROLE_ROUTER)
            {
                return true;
            }

            nodeid_t myNodeId = ServerOptions.NodeId;
            nodeid_t masterCommanderNodeId = GetMasterCommanderNodeId();
            nodeid_t peerNodeId = node.NodeInfo != null ? node.NodeInfo.NodeId : 0;
            if (myNodeId == peerNodeId)
            {
                return true;
            }

            // if my node is master commander, or other node is master commander, is direct connected
            if (myNodeId == masterCommanderNodeId || peerNodeId == masterCommanderNodeId)
            {
                return true;
            }

            // if role of this node is in role_need_connect, is direct connected
            return m_roleNeedConnect.Contains(node.NodeInfo!.Role);
        }

        internal ServerNode? PickRouter(ServerNode node, RpcPickStrategy strategy = RpcPickStrategy.RPC_FIX_HASH)
        {
            nodeid_t routerId = node.GetRouterId();
            var router = FindNode(routerId);
            if (router != null)
            {
                return router;
            }

            nodeid_t myNodeId = ServerOptions.NodeId;
            uint peerNodeId = node.NodeInfo.NodeId;
            string pairKey = myNodeId < peerNodeId ? $"{myNodeId}-{peerNodeId}" : $"{peerNodeId}-{myNodeId}";
            nodeid_t routerNodeId = 0;

            if (strategy == RpcPickStrategy.RPC_FIX_HASH)
            {
                if (RoleName2ServerNodeIds.TryGetValue(RoleName.ROLE_ROUTER, out var routerList))
                {
                    if (!routerList.Any())
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }

                uint32_t hash_key = MurmurHash.MurmurHash3_32(pairKey);
                routerNodeId = routerList[(int)(hash_key % routerList.Count)];
            }
            else
            {
                var consistentHash = GetConsistentHashByRole(RoleName.ROLE_ROUTER);
                if (consistentHash == null)
                {
                    return null;
                }

                routerNodeId = consistentHash.PickNode(pairKey);
            }

            if (routerNodeId == 0)
            {
                return null;
            }

            router = FindNode(routerNodeId);
            node.SetRouterId(routerNodeId);
            // after pick router, we associate node with conn_id of router
            if (router != null)
            {
                System.Diagnostics.Debug.Assert(router != null);
                AddNodeConnId(node.NodeInfo.NodeId, router.ConnId);
            }

            return router;
        }

        private void AddNodeConnId(uint nodeId, uint connId)
        {
            m_nodeId2ConnId[nodeId] = connId;
            Logger.LogInformation("AddNodeConn Success, NodeId={NodeId}, ConnId={ConnId}", nodeId, connId);
        }

        private connid_t GetNodeConnId(uint nodeId)
        {
            if (m_nodeId2ConnId.TryGetValue(nodeId, out connid_t connId))
            {
                return connId;
            }

            return 0;
        }

        const int64_t kTickInterval = 1000;

        public virtual void Tick()
        {
            if (IsShutdown)
            {
                return;
            }


            if (CurrentMilliseconds - m_lastTickTime < kTickInterval)
            {
                return;
            }

            m_lastTickTime = CurrentMilliseconds;

            WatchNodeState();

            if (!IsRpcReady())
            {
                CheckRpcInitReady();
            }

            m_mailboxManager.Tick();
        }

        protected virtual bool CheckRpcInitReady()
        {
            if (m_checkRpcReadyStartTime == 0)
                m_checkRpcReadyStartTime = CurrentMilliseconds;

            if (RpcReady)
            {
                return true;
            }

            if (MasterCommander == null || !MasterCommander.IsActive())
            {
                return false;
            }

            foreach (string role in m_roleNeedConnect)
            {
                if (m_roleRequireNum.TryGetValue(role, out uint requireNum))
                {
                    if (requireNum == 0)
                    {
                        continue;
                    }

                    if (RoleName2ServerNodeIds.TryGetValue(role, out var roleNodes))
                    {
                        int readyNum = 0;
                        foreach (nodeid_t nodeId in roleNodes)
                        {
                            var node = FindNode(nodeId);
                            if (node == null)
                            {
                                continue;
                            }

                            if (node == MyNode)
                            {
                                ++readyNum;
                                continue;
                            }

                            if (node.IsActive())
                            {
                                ++readyNum;
                            }
                        }

                        Logger.LogDebug("CheckRpcInitReady, Role={Role}, ReadyNum={ReadyNum}, RequireNum={RequireNum}",
                            role, readyNum, requireNum);

                        // If reached max wait time for required nodes to join yet
                        if (CurrentMilliseconds - m_checkRpcReadyStartTime >= MaxRpcWaitTimeForRequiredNodesToJoin)
                        {
                            // fail the check if the number of nodes is less than the required percentage of the number of nodes
                            if (readyNum < Math.Ceiling(m_percentOnNodeStart * requireNum))
                            {
                                return false;
                            }
                        }
                        else
                        {
                            // If not reached max wait time and required number of nodes, fail the check
                            if (readyNum < requireNum)
                            {
                                return false;
                            }
                        }
                    }
                }
            }

            SendReadyToMasterCommander();
            SetRpcReady();
            return true;
        }

        protected void SetRpcReady()
        {
            RpcReady = true;
            RpcLogicCallback.OnRpcReady?.Invoke();
        }

        private bool IsRpcReady()
        {
            return RpcReady;
        }

        private void WatchNodeState()
        {
            WatchMasterCommander();
            // watch other role and connect it if needed
            foreach (string role in m_roleNeedConnect)
            {
                ConnectNodes(role);
            }

            // watch connecting nodes
            List<nodeid_t> connectExpired = new();
            foreach (nodeid_t node_id in ConnectingNodes)
            {
                ServerNode? node = FindNode(node_id);
                if (node == null)
                {
                    connectExpired.Add(node_id);
                    continue;
                }

                if (node.State == ENodeConnState.CONNECTING)
                {
                    if (CurrentMilliseconds - node.GetConnectBeginTime() > 5000)
                    {
                        Logger.LogWarning("[RPC]ConnectingTimeOut, NodeId={NodeId}, Ip={Ip} Port={Port}",
                            node.NodeInfo.NodeId, node.NodeInfo.Ip, node.NodeInfo.Port);
                        connectExpired.Add(node_id);
                    }
                }
            }

            foreach (nodeid_t nodeId in connectExpired)
            {
                CloseNodeConnect(nodeId);
            }
        }

        private void ConnectNodes(string role)
        {
            if (!RoleName2ServerNodeIds.TryGetValue(role, out var nodeList))
            {
                return;
            }

            bool isSameRole = MyNode.NodeInfo.Role == role;
            nodeid_t myNodeId = MyNode.NodeInfo.NodeId;

            foreach (nodeid_t nodeId in nodeList)
            {
                if (isSameRole && nodeId <= myNodeId)
                {
                    // only connect to node_id > my_node_id when my role is same as target role
                    continue;
                }

                ServerNode? node = FindNode(nodeId);
                if (node == null)
                {
                    continue;
                }

                if (node.State == ENodeConnState.DISCONNECT &&
                    CurrentMilliseconds - node.GetLastConnectTime() > kWatchInterval)
                {
                    node.SetLastConnectTime(CurrentMilliseconds);
                    Logger.LogInformation("[RPC] TryConnectToNode, Role={Role}, NodeId={NodeId}, Ip={Ip} Port={Port}",
                        role, node.NodeInfo.NodeId, node.NodeInfo.Ip, node.NodeInfo.Port);
                    ConnectNode(nodeId);
                }
            }
        }

        protected void WatchMasterCommander()
        {
            if (MasterCommander != null && MasterCommander.AddressEqual(MyNode))
            {
                return;
            }

            // watch commander state
            if (MasterCommander != null && MasterCommander.State == ENodeConnState.DISCONNECT)
            {
                if (CurrentMilliseconds - MasterCommander.GetLastConnectTime() > kWatchInterval)
                {
                    MasterCommander.SetLastConnectTime(CurrentMilliseconds);
                    MsgPack_S_ServerNodeInfo_Proto nodeInfo = MasterCommander.NodeInfo;
                    Logger.LogInformation("[RPC]TryConnectCommander, NodeId={NodeId}, Ip={Ip} Port={Port}",
                        nodeInfo.NodeId, nodeInfo.Ip, nodeInfo.Port);
                    ConnectNode(MasterCommander.NodeInfo.NodeId);
                }
            }
        }

        private void ConnectNode(uint nodeId)
        {
            nodeid_t myNodeId = ServerOptions.NodeId;
            if (myNodeId == nodeId)
            {
                return;
            }

            ServerNode? node = FindNode(nodeId);
            if (node == null)
            {
                Logger.LogWarning("ConnectNodeFailed, can not find node info, NodeId={NodeId}", nodeId);
                return;
            }

            MsgPack_S_ServerNodeInfo_Proto nodeInfo = node.NodeInfo;

            //check state
            if (node.State != ENodeConnState.DISCONNECT)
            {
                if (!node.IsActive())
                {
                    //warning
                    Logger.LogWarning(
                        "ConnectNode failed, target node state invalid, MyNode={MyNode}, TargetNode={TargetNode}, state={state}",
                        myNodeId, node.NodeInfo.NodeId, (int)node.State);
                    return;
                }

                Logger.LogWarning(
                    "ConnectNode duplicated, ignore connect request, NodeId={NodeId}, Role={Role}, Ip={Ip}, Port={Port}",
                    node.NodeInfo.NodeId, node.NodeInfo.Role, node.NodeInfo.Ip, node.NodeInfo.Port);
                return;
            }

            if (!IsAllowConnect(node))
            {
                Logger.LogWarning(
                    "ConnectNode failed, not allow connect to target role, MyNode={MyNode}, MyRole={MyRole}, TargetNode={TargetNode}, TargetRole={TargetRole}",
                    myNodeId,
                    ServerOptions.Role, nodeInfo.NodeId, nodeInfo.Role);
                return;
            }

            node.SetState(ENodeConnState.CONNECTING);
            node.SetConnectBeginTime(CurrentMilliseconds);
            ConnectingNodes.Add(nodeId);

            connid_t connId = DoConnect((connMgr, nodeConnId) =>
            {
                connMgr.NewConnect(nodeInfo.Ip, nodeInfo.Port, nodeConnId, nodeInfo.NodeId);
                node.ConnId = nodeConnId;
            }, ConnType.TCP);

            node.ConnId = connId;
            Logger.LogInformation(
                "ConnectNodeBegin, MyNode={MyNode}, MyRole={MyRole}, ConnId={ConnId}, TargetNode={TargetNode}, Role={Role}, Ip={Ip}, Port={Port}",
                myNodeId, ServerOptions.Role, connId, nodeInfo.NodeId, nodeInfo.Role, nodeInfo.Ip, nodeInfo.Port);
        }

        private bool IsAllowConnect(ServerNode node)
        {
            string myRole = ServerOptions.Role;
            nodeid_t myNodeId = ServerOptions.NodeId;
            nodeid_t masterCommanderNodeId = GetMasterCommanderNodeId();

            // not allow connect to myself
            nodeid_t peerNodeId = node.NodeInfo.NodeId;
            if (myNodeId == peerNodeId)
            {
                return false;
            }

            if (myNodeId == masterCommanderNodeId)
            {
                // master commander should not connect to other node
                return false;
            }
            else if (peerNodeId == masterCommanderNodeId)
            {
                // other node should connect to master commander
                return true;
            }
            else if (m_roleNeedConnect.Contains(node.NodeInfo.Role))
            {
                // allow connect to role_need_connect
                if (myRole != node.NodeInfo.Role)
                {
                    return true;
                }

                // if same role, connect to node_id < my_node_id
                return myNodeId < peerNodeId;
            }

            return false;
        }

        private void CloseConnection(uint connId)
        {
            if (!IsInited)
            {
                return;
            }

            ConnectionManager.DoCloseConnection(connId);
        }

        public delegate void DelegateConnectFunc(ConnectionManager connMgr, connid_t connId);

        private connid_t DoConnect(DelegateConnectFunc connFunc, ConnType connType)
        {
            if (!IsInited)
            {
                return 0;
            }

            connid_t connId = ConnectionManager.NextConnectionId();
            m_asyncDispatcher.Post(() =>
            {
                connFunc(ConnectionManager, connId);
            });

            return connId;
        }

        #region Node

        public ServerNode? GetMyNode()
        {
            return MyNode;
        }

        public ServerNode? GetNodeById(nodeid_t nodeId)
        {
            NodeId2ServerNodeInfo.TryGetValue(nodeId, out ServerNode? serverNode);
            return serverNode;
        }

        public List<nodeid_t> GetActiveNodesByRole(string role)
        {
            if (RoleName2ServerNodeIds.TryGetValue(role, out List<nodeid_t>? nodeIds))
            {
                return nodeIds;
            }

            return new List<nodeid_t>(0);
        }

        public Task<nodeid_t> GetRandomNodeByRole(string role)
        {
            TaskCompletionSource<nodeid_t> tcs = new();

            m_asyncDispatcher.Post(() =>
                {
                    if (RoleName2ServerNodeIds.TryGetValue(role, out List<nodeid_t>? nodeIds))
                    {
                        if (nodeIds.Any())
                        {
                            tcs.SetResult(nodeIds[m_random.Next(nodeIds.Count)]);
                        }
                    }
                }
            );
            return tcs.Task;
        }

        public IEnumerable<uint> GetAllActiveNodes()
        {
            List<uint> activeNodes = new();
            foreach (KeyValuePair<string, List<uint>> pair in RoleName2ServerNodeIds)
            {
                activeNodes.AddRange(pair.Value);
            }

            return activeNodes;
        }

        private void AddNode(nodeid_t nodeId, string role, string ip, ushort port, string tag)
        {
            var nodeInfo = new MsgPack_S_ServerNodeInfo_Proto
            {
                NodeId = nodeId,
                Role = role,
                Ip = ip,
                Port = port,
                Tag = tag
            };

            AddNode(nodeInfo);
        }

        private ServerNode AddNode(MsgPack_S_ServerNodeInfo_Proto nodeInfo)
        {
            if (NodeId2ServerNodeInfo.TryGetValue(nodeInfo.NodeId, out ServerNode? serverNode))
            {
                serverNode.UpdateNodeInfo(nodeInfo);
            }
            else
            {
                serverNode = new ServerNode(nodeInfo);
                NodeId2ServerNodeInfo.Add(nodeInfo.NodeId, serverNode);
                Logger.LogInformation("[RPC] AddNode, NodeId={NodeId}, Role={Role}, Ip={Ip}, Port={Port}",
                    nodeInfo.NodeId, nodeInfo.Role, nodeInfo.Ip, nodeInfo.Port);
            }

            return serverNode;
        }

        private void DelNode(nodeid_t nodeId)
        {
            if (NodeId2ServerNodeInfo.ContainsKey(nodeId))
            {
                NodeId2ServerNodeInfo.Remove(nodeId);
                Logger.LogInformation("[RPC] DelNode, NodeId={NodeId}", nodeId);
            }
        }

        protected ServerNode? FindNode(nodeid_t nodeId)
        {
            return NodeId2ServerNodeInfo.GetValueOrDefault(nodeId);
        }

        #endregion

        #region MailBox

        public void RegisterMailBox(string mailboxName, DelegateMailboxRegisterCallback callback, bool isOverride,
            bool isBroadcastRegister = false)
        {
            m_asyncDispatcher.Post(m_mailboxManager.RegisterMailbox, mailboxName, callback, isOverride,
                isBroadcastRegister);
        }

        public void UnRegisterMailbox(string mailboxName, DelegateMailboxUnregisterCallback callback)
        {
            m_asyncDispatcher.Post(m_mailboxManager.UnRegisterMailbox, mailboxName, callback);
        }

        #endregion

        #region Commander

        private ServerNode? GetMasterCommander()
        {
            return MasterCommander;
        }

        public virtual void RegMasterCommanderSwitchCallback(Action switch_to_master_cb, Action switch_to_slave_cb)
        {
            // do nothing only implemented in role commander
        }

        public virtual void RegCommanderChangeCallback(Action<nodeid_t> onCommanderSwitch)
        {
            EventOnCommanderSwitch += onCommanderSwitch;
        }

        #endregion

        // player_rpc
        //pause message route when player is migrating
        // void SetPlayerMsgPending(userid_t id, bool is_space_player);
        // void FlushPlayerMsg(userid_t id, bool is_space_player);

        public void SendReadyToMasterCommander()
        {
            if (MyNode.NodeInfo.IsReady)
            {
                return;
            }

            MyNode.NodeInfo.IsReady = true;
            if (MasterCommander == null)
            {
                return;
            }

            if (MasterCommander == MyNode && MasterCommander != null)
            {
                HandleNodeReady(MasterCommander.ConnId, MasterCommander.NodeInfo.NodeId);
                return;
            }

            if (MasterCommander!.State == ENodeConnState.DISCONNECT)
            {
                return;
            }

            MsgPack_S_NodeReady_Proto msg = new();
            msg.NodeId = ServerOptions.NodeId;
            DirectSendNode(MasterCommander, msg);
        }

        public void RpcToNode(nodeid_t nodeId, string uri, string func, byte[] packedArgs, bool needResp,
            bool isBroadcast, request_id_t reqId = 0)
        {
            RpcToNode(nodeId, uri, func, packedArgs, null, needResp, false, isBroadcast, reqId);
        }

        public void RpcToNode(nodeid_t nodeId, string uri, string func, MsgPackStructBase message,
            bool needResp, bool isBroadcast, request_id_t reqId)
        {
            RpcToNode(nodeId, uri, func, null, message, needResp, false, isBroadcast, reqId);
        }

        public void RpcToNode(nodeid_t nodeId, string uri, string func, byte[]? packedArgs,
            MsgPackStructBase? message, bool needResp, bool isInternal, bool isBroadcast, request_id_t reqId)
        {
            MsgPack_S_Rpc_Req req = new();
            int flag = 0;
            if (needResp)
            {
                flag |= RpcFlag.RPC_NEED_RESP;
            }

            if (isInternal)
            {
                flag |= RpcFlag.RPC_INTERNAL;
            }

            if (isBroadcast)
            {
                flag |= RpcFlag.RPC_BROADCAST;
            }

            FillRpcRequestData(req, uri, func, packedArgs, message, flag, reqId);
            if (nodeId == ServerOptions.NodeId)
            {
                HandleRpcRequest(req);
                return;
            }

            MsgPack_S_RpcToNode_Proto nodeRpc = new() { DestNodeId = nodeId, RpcReq = req };

            SendNode(nodeId, nodeRpc);
        }

        public request_id_t RpcToNode(nodeid_t nodeId, string func, MsgPackStructBase msg, bool needResp,
            bool isInternal, bool isBroadcast, int flags = 0, request_id_t reqId = 0)
        {
            MsgPack_S_Rpc_Req req = new();
            int flag = flags;
            if (needResp)
            {
                flag |= RpcFlag.RPC_NEED_RESP;
            }

            if (isInternal)
            {
                flag |= RpcFlag.RPC_INTERNAL;
            }

            if (isBroadcast)
            {
                flag |= RpcFlag.RPC_BROADCAST;
            }

            FillRpcRequestData(req, "", func, null, msg, flag, reqId);
            if (nodeId == ServerOptions.NodeId)
            {
                HandleRpcRequest(req);
                return reqId;
            }

            MsgPack_S_RpcToNode_Proto nodeRpc = new() { DestNodeId = nodeId };
            nodeRpc.RpcReq = req;
            SendNode(nodeId, nodeRpc);
            return reqId;
        }

        public virtual void RpcByMailbox(string mailboxName, string func, byte[] packedArgs, bool needResp, request_id_t reqId)
        {
            RpcByMailbox(mailboxName, func, packedArgs, null, needResp, false, reqId);
        }

        public virtual void RpcByMailbox(string mailboxName, MsgPackStructBase message, bool needResp, request_id_t reqId)
        {
            RpcByMailbox(mailboxName, "", null, message, needResp, false,  reqId);
        }

        protected virtual void RpcByMailbox(string mailboxName, string func, byte[]? packedArgs,
            MsgPackStructBase? message, bool needResp, bool isInternal, request_id_t reqId)
        {
            MsgPack_S_Rpc_Req req = new();
            int flag = RpcFlag.FLAG_RPC_BY_MAILBOX;
            if (needResp)
            {
                flag |= RpcFlag.RPC_NEED_RESP;
            }

            if (isInternal)
            {
                flag |= RpcFlag.RPC_INTERNAL;
            }

            FillRpcRequestData(req, mailboxName, func, packedArgs, message, flag, reqId);
            m_mailboxManager.Send(mailboxName, req);
            // TODO send fail
        }

        public virtual void RpcToPlayer(userid_t playerId, bool isSpacePlayer, string func, byte[]? packedArgs,
            MsgPackStructBase? message, bool needResp, bool isInternal, request_id_t reqId)
        {
            MsgPack_S_Rpc_Req req = new();
            int flag = 0;
            if (needResp)
            {
                flag |= RpcFlag.RPC_NEED_RESP;
            }

            if (isInternal)
            {
                flag |= RpcFlag.RPC_INTERNAL;
            }

            FillRpcRequestData(req, "", func, packedArgs, message, flag, reqId);
            MsgPack_S_RpcToPlayerRouter_Proto rpcToPlayerRouter = new() { IsSpacePlayer = isSpacePlayer };

            rpcToPlayerRouter.Uid = playerId;
            rpcToPlayerRouter.RpcReq = req;
            uint router = PickRouter(playerId);
            if (router == 0)
            {
                Logger.LogWarning("Can't_pick_router, uid={uid}", playerId);
                return;
            }

            if (router == ServerOptions.NodeId)
            {
                HandleRpcRequestToPlayerRouter(rpcToPlayerRouter);
                return;
            }

            SendNode(router, rpcToPlayerRouter);
        }

        protected virtual void HandleRpcRequestToPlayerRouter(MsgPack_S_RpcToPlayerRouter_Proto playerReq)
        {
            /*            if(MyNode.NodeInfo.Role == RoleName.ROLE_GAME)
                        {
                            MsgPack_S_RpcToPlayerProto rpcToPlayer = new();
                            rpcToPlayer.Uid = rpcToPlayerRouter.Uid;
                            rpcToPlayer.RpcReq = rpcToPlayerRouter.RpcReq;
                            HandleRpcRequestToPlayer(rpcToPlayer);
                        }*/
        }

        private void FillRpcRequestData(MsgPack_S_Rpc_Req rpcReq, string uri, string func, byte[]? packedArgs,
            MsgPackStructBase? msg, int flag, request_id_t reqId)
        {
            rpcReq.ReqId = reqId;
            rpcReq.SrcNode = ServerOptions.NodeId;
            rpcReq.Uri = uri;
            rpcReq.Func = func;
            rpcReq.Data = packedArgs == null ? Array.Empty<byte>() : packedArgs;
            if (msg != null)
            {
                rpcReq.MsgProtoCode = (int)msg.ProtoCode;
                rpcReq.Data = MsgPackProtoHelper.Serialize(msg)!;
            }

            rpcReq.Flag = flag;
        }

        internal uint GetMasterCommanderNodeId()
        {
            if (MasterCommander == null)
            {
                return 0;
            }

            return MasterCommander.NodeInfo.NodeId;
        }

        internal Connection? GetConnection(uint nodeId, ConnType type = ConnType.TCP)
        {
            var connId = GetNodeConnId(nodeId);
            if (connId != 0)
                return ConnectionManager.FindConnection(connId);
            return null;
        }

        internal void RegCallbackOnHashChange(string role, string callbackName, ConsistentHashChangeCallback cb)
        {
            var consistentHash = GetConsistentHashByRole(role);
            if (consistentHash == null)
            {
                Logger.LogWarning("RegCallbackOnHashChange_failed, can't_find_consistent_hash, Role={Role}", role);
                return;
            }

            consistentHash.RegCallbackOnHashChange(callbackName, cb);
        }

        public ConsistentHash? GetConsistentHashByRole(string role)
        {
            Role2ConsistentHash.TryGetValue(role, out ConsistentHash? consistentHash);
            return consistentHash;
        }

        internal uint PickRouter(string key)
        {
            var consistentHash = GetConsistentHashByRole(RoleName.ROLE_ROUTER);
            if (consistentHash == null)
            {
                Logger.LogError("[RPC]PickRouter, can_not_get_consistent_hash");
                return 0;
            }

            return consistentHash.PickNode(key);
        }

        internal uint PickRouter(long key)
        {
            var consistentHash = GetConsistentHashByRole(RoleName.ROLE_ROUTER);
            if (consistentHash == null)
            {
                Logger.LogError("[RPC]PickRouter, can_not_get_consistent_hash");
                return 0;
            }

            return consistentHash.PickNode(key);
        }

        internal bool RegisterCallbackOnRoleJoin(string role, string callbackName, NodeJoinCallback cb)
        {
            var callbacks = Role2JoinCallback[role];
            if (!callbacks.TryAdd(callbackName, cb))
            {
                Logger.LogError("[RPC]RegisterCallbackOnRoleJoin_failed, callback_name={callback_name}, Role={Role}",
                    callbackName, role);
                return false;
            }

            return true;
        }

        internal bool UnRegisterCallbackOnRoleJoin(string role, string callbackName)
        {
            var callbacks = Role2JoinCallback[role];
            if (!callbacks.Remove(callbackName))
            {
                Logger.LogError("[RPC]RegisterCallbackOnRoleJoin_failed, callback_name={callback_name}, Role={Role}",
                    callbackName, role);
                return false;
            }

            return true;
        }

        internal bool RegisterCallbackOnRoleLeave(string role, string callbackName, NodeLeaveCallback cb)
        {
            var callbacks = Role2LeaveCallback[role];
            if (!callbacks.TryAdd(callbackName, cb))
            {
                Logger.LogError("[RPC]RegisterCallbackOnRoleLeave_failed, callback_name={callback_name}, Role={Role}",
                    callbackName, role);
                return false;
            }

            return true;
        }

        internal bool UnRegisterCallbackOnRoleLeave(string role, string callbackName)
        {
            var callbacks = Role2LeaveCallback[role];
            if (!callbacks.Remove(callbackName))
            {
                Logger.LogError("[RPC]UnRegisterCallbackOnRoleLeave_failed, callback_name={callback_name}, Role={Role}",
                    callbackName, role);
                return false;
            }

            return true;
        }

        internal void SendNode_CR(uint nodeId, MsgPackStructBase msg)
        {
            SendNode(nodeId, msg);
        }

        internal void SendNode_CR(ServerNode node, MsgPackStructBase msg)
        {
            SendNode(node, msg);
        }

        protected void Broadcast(MsgPackStructBase message, bool excludeSelf = true)
        {
            foreach (var pair in NodeId2ServerNodeInfo)
            {
                if (excludeSelf && pair.Value == GetMyNode())
                {
                    continue;
                }

                SendNode(pair.Value, message);
            }
        }

        public void SendRpcResponse(RpcRequestInfo reqInfo, MsgPackStructBase message, int errCode)
        {
            MsgPack_S_Rpc_Ack resp = new()
            {
                ReqId = reqInfo.ReqId, ReqNode = reqInfo.NodeId, HiddenReqNode = reqInfo.HiddenNodeId, RespNodeId = ServerOptions.NodeId,
                MsgProtoCode = (int)message.ProtoCode,
                RespData = MsgPackProtoHelper.Serialize(message)!,
                ErrorCode = errCode
            };

            if (reqInfo.NodeId == ServerOptions.NodeId)
            {
                HandleRpcResponse(resp.RespNodeId, (resp));
                return;
            }

            SendNode(reqInfo.NodeId, resp);
        }

        public void SendRpcResponse(RpcRequestInfo reqInfo, byte[] packedData, RpcErrCode errCode)
        {
            MsgPack_S_Rpc_Ack resp = new()
            {
                ReqId = reqInfo.ReqId, ReqNode = reqInfo.NodeId, HiddenReqNode = reqInfo.HiddenNodeId, RespNodeId = ServerOptions.NodeId,
                RespData = packedData,
                ErrorCode = (int)errCode
            };

            if (reqInfo.NodeId == ServerOptions.NodeId)
            {
                HandleRpcResponse(resp.RespNodeId, (resp));
                return;
            }

            SendNode(reqInfo.NodeId, resp);
        }

        internal void RegisterPlayerRouterFunc(Func<long, uint> func, bool isSpacePlayer)
        {
            if (ServerOptions.Role == RoleName.ROLE_ROUTER)
            {
                _RegisterPlayerRouterFunc(func, isSpacePlayer);
            }
            else if (ServerOptions.Role == RoleName.ROLE_BATTLE_GUARD)
            {
                throw new NotImplementedException();
                /*auto impl = (BattleGuardNodeMgrImpl*)impl_;
                impl->RegisterPlayerRouterFunc(func, is_space_player);*/
            }
            else
            {
                Logger.LogInformation("RegisterPlayerRouterFunc is only support ROUTER or BATTLE_GUARD role");
            }
        }

        protected virtual void _RegisterPlayerRouterFunc(Func<long, uint> func, bool isSpacePlayer)
        {
        }

        internal virtual void SetPlayerMsgPending(long id, bool isSpacePlayer)
        {
            if (ServerOptions.Role != RoleName.ROLE_ROUTER)
            {
                return;
            }
        }

        internal virtual void FlushPlayerMsg(long id, bool isSpacePlayer)
        {
            //only router can pause player msg route.
            if (ServerOptions.Role != RoleName.ROLE_ROUTER)
            {
                return;
            }
        }

        private const int RPC_CONNECTION_CHECK_INTERVAL = 15;

        private const int
            MaxRpcWaitTimeForRequiredNodesToJoin =
                2 * 60 * 1000; // in milliseconds, otherwise just wait for (percent_on_node_start_)% of required nodes join

        protected bool IsShutdown = false;
        protected bool RpcReady = false;
        protected ServerNode? MasterCommander;
        private bool IsInited;

        public static readonly int kConnectMagic = 0x20121018;
        public static readonly long kWatchInterval = 1000;

        public RpcLogicCallback RpcLogicCallback = null!;
        public Action<long, MsgPackStructBase>? HandleServerForwardClientMsgFunc = null;

        public Dictionary<string, ConsistentHash> Role2ConsistentHash
        {
            get;
        } = new();

        protected ServerOptions ServerOptions;
        protected ClusterOptions ClusterOptions;
        private readonly HashSet<nodeid_t> ConnectingNodes = new();

        public ServerNode MyNode
        {
            get;
        }

        private long m_lastTickTime;
        private const uint SecureParamHash = 543521123;
        private static readonly ILogger Logger = InternalLoggerFactory.LoggerFactory.CreateLogger<ServerNodeManager>();
        protected Dictionary<nodeid_t, ServerNode> NodeId2ServerNodeInfo = new();
        private readonly Dictionary<string, List<nodeid_t>> RoleName2ServerNodeIds = new();
        private readonly Dictionary<string, Dictionary<string, NodeJoinCallback>> Role2JoinCallback = new();
        private readonly Dictionary<string, Dictionary<string, NodeLeaveCallback>> Role2LeaveCallback = new();
        public event Action<nodeid_t>? EventOnCommanderSwitch;
        private readonly Dictionary<nodeid_t, connid_t> m_nodeId2ConnId = new();

        public readonly ConnectionManager ConnectionManager = new();
        private readonly NetWorkerCallback m_callbacks = new();
        private readonly ConsistentHashSyncData m_chashSyncData = new();
        private readonly List<connid_t> m_nodesFromAccept = new();
        private readonly MailBoxManager m_mailboxManager;
        private int64_t m_checkRpcReadyStartTime;
        private readonly double m_percentOnNodeStart = 0.9f;
        private readonly List<string> m_roleNeedConnect = new();

        protected long CurrentMilliseconds => RpcService.Instance.CurrentMilliseconds;

        private readonly IDictionary<string, uint> m_roleRequireNum;
        private AsyncDispatcher m_asyncDispatcher = null!;
        private readonly Random m_random = Random.Shared;
    }
}
