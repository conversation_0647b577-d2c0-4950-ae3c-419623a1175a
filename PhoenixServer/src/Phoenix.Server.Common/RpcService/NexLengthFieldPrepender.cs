/*using DotNetty.Codecs;
using DotNetty.Transport.Channels;
using Nex.Proto;
using Nex.Proto.Buffers;
using System.Buffers;
using System.Diagnostics;

namespace Phoenix.Service
{
    public class NexLengthFieldPrepender : MessageToMessageEncoder<INexProtoMessage>
    {
        public NexLengthFieldPrepender()
        {
        }

        protected override void Encode(IChannelHandlerContext context, INexProtoMessage message, List<object> output)
        {
            var buffer = ArrayPool<byte>.Shared.Rent(message.CalcPackSize() + 7);
            ByteBuffer byteBuffer = new ByteBuffer(buffer);
            message.PackMessageWithHeader(byteBuffer);
            Debug.Assert(byteBuffer.Position - byteBuffer.BufferArrayOffset == message.CalcPackSize() + 7);
            output.Add(context.Allocator.Buffer().WriteBytes(byteBuffer.BufferArray, byteBuffer.BufferArrayOffset, byteBuffer.Position));
            ArrayPool<byte>.Shared.Return(buffer);
        }
    }
}
*/
