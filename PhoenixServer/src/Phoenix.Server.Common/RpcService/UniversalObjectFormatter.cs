/*
using MessagePack;
using MessagePack.Formatters;
using Phoenix.MsgPackLogic.Protocol;
using System.Buffers;

namespace Phoenix.Server.Common;

public sealed class UniversalObjectFormatter : IMessagePackFormatter<object?>
{
    public void Serialize(ref MessagePackWriter writer, object? value, MessagePackSerializerOptions options)
    {
        if (value == null)
        {
            writer.WriteNil();
            return;
        }

        switch (value)
        {
            case int i: writer.Write(i); break;
            case string s: writer.Write(s); break;
            case bool b: writer.Write(b); break;
            case float f: writer.Write(f); break;
            case byte[] bs: writer.Write(bs); break;
            case double d: writer.Write(d); break;
            case List<object?> list:
#pragma warning disable CS8620 // 由于引用类型的可为 null 性差异，实参不能用于形参。
                new ObjectListFormatter().Serialize(ref writer, list, options);
#pragma warning restore CS8620 // 由于引用类型的可为 null 性差异，实参不能用于形参。
                break;
            /*case Dictionary<object, object> dict:
                writer.WriteMapHeader(dict.Count);
                foreach (var kvp in dict)
                {
                    // 序列化Key
                    MessagePackSerializer.Serialize<object>(ref writer, kvp.Key, options);

                    // 处理特殊类型标记
                    if (kvp.Value is MsgPackStructBase typeValue)
                    {
                        #1#/*if (!TypeRegistry.TryGetTypeId(typeValue, out var typeId))
                            throw new MessagePackSerializationException($"Unregistered type: {typeValue.Name}");#1#/*

                        writer.WriteMapHeader(2);
                        writer.Write("__type");
                        writer.Write(typeId);
                        writer.Write("data");
                        MessagePackSerializer.Serialize(typeValue, ref writer, kvp.Value, options);
                    }
                    else
                    {
                        MessagePackSerializer.Serialize<object>(ref writer, kvp.Value, options);
                    }
                }
                break;#1#
            case MsgPackStructBase:
                {
                    writer.Flush();
                    var msg = value as MsgPackStructBase;
                    var buffer = new ArrayBufferWriter<byte>();
                    var msg_writer = new MessagePackWriter(buffer);
                    msg_writer.WriteInt32((int)msg.ProtoCode);
                    var data = MsgPackProtoHelper.Serialize((int)msg.ProtoCode, msg);
                    msg_writer.Write(data);
                    msg_writer.Flush();
                    writer.WriteExtensionFormatHeader(new ExtensionHeader(122, buffer.WrittenCount));
                    writer.WriteRaw(buffer.WrittenSpan);
                    writer.Flush();
                }
                break;
            default:
                throw new MessagePackSerializationException($"Unsupported type: {value.GetType().Name}");
                /*if (!TypeRegistry.TryGetTypeId(value.GetType(), out var typeId))
                    throw new MessagePackSerializationException($"Unregistered type: {value.GetType().Name}");
                writer.WriteArrayHeader(2);
                writer.Write(typeId);
                MessagePackSerializer.Serialize(value.GetType(), ref writer, value, options);
                break;#1#
        }
    }

    public object? Deserialize(ref MessagePackReader reader, MessagePackSerializerOptions options)
    {
        if (reader.TryReadNil()) return null;

        switch (reader.NextMessagePackType)
        {
            case MessagePackType.Integer:
                return reader.ReadInt32();
            case MessagePackType.String:
                return reader.ReadString();
            case MessagePackType.Boolean:
                return reader.ReadBoolean();
            case MessagePackType.Float:
                return reader.ReadDouble();
            case MessagePackType.Array:
                return ReadTypedArray(ref reader, options);
            case MessagePackType.Extension:
                {
                    var header = reader.ReadExtensionFormatHeader();
                    if (header.TypeCode == 122)
                    {
                        int protoCode = reader.ReadInt32();
                        var data = reader.ReadBytes();
                        return MsgPackProtoHelper.Deserialize(protoCode, data.Value.ToArray<byte>());
                        /*                        Assert.IsTrue(data != null);
                                                return data;#1#
                    }
                    return reader.ReadExtensionFormat();
                }

            case MessagePackType.Unknown:
                return null;
            case MessagePackType.Nil:
                return null;
            case MessagePackType.Binary:
                return reader.ReadBytes();
            case MessagePackType.Map:
/*                return ReadMapTyped(ref reader, options);#1#
            default:
                throw new MessagePackSerializationException($"Unsupported type: {reader.NextMessagePackType}");
        }

        /*return reader.NextMessagePackType switch
        {
            MessagePackType.Integer => reader.ReadInt32(),
            MessagePackType.String => reader.ReadString(),
            MessagePackType.Boolean => reader.ReadBoolean(),
            MessagePackType.Float => reader.ReadDouble(),
            MessagePackType.Array => ReadTypedArray(ref reader, options),
            _ => throw new MessagePackSerializationException($"Unsupported type: {reader.NextMessagePackType}")
        };#1#
    }

    /*private object ReadMapTyped(ref MessagePackReader reader, MessagePackSerializerOptions options)
    {
        var mapCount = reader.ReadMapHeader();
        var dictionary = new Dictionary<object, object>(mapCount);

        for (var i = 0; i < mapCount; i++)
        {
            // 反序列化Key
            var key = Deserialize(ref reader, options);

            // 特殊处理类型标记
            if (key is string keyStr && keyStr == "__type")
            {
                // 读取类型元数据
                var typeId = (int)Deserialize(ref reader, options);
                var data = Deserialize(ref reader, options);

                if (TypeRegistry.TryGetType(typeId, out var type))
                {
                    // 创建动态字典实例
                    var dynamicDict = new Dictionary<object, object>
                    {
                        ["__type"] = typeId,
                        ["data"] = data
                    };
                    return MessagePackSerializer.Deserialize(type,
                        MessagePackSerializer.Serialize(dynamicDict, options),
                        options);
                }
                throw new MessagePackSerializationException($"Unknown type ID: {typeId}");
            }
            else
            {
                // 常规值反序列化
                var value = Deserialize(ref reader, options);
                dictionary[key] = value;
            }
        }

        return dictionary;
    }#1#

    private object? ReadTypedArray(ref MessagePackReader reader, MessagePackSerializerOptions options)
    {
        var count = reader.ReadArrayHeader();
        /* if (count == 2)
        {
            var typeId = reader.ReadInt32();
            if (!TypeRegistry.TryGetType(typeId, out var type))
                throw new MessagePackSerializationException($"Unknown type ID: {typeId}");

            return MessagePackSerializer.Deserialize(type, ref reader, options);
        }#1#

        var list = new List<object?>();
        for (int i = 0; i < count; i++)
            list.Add(Deserialize(ref reader, options));
        return list;
    }
}
*/
