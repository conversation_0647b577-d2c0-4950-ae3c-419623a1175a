using Microsoft.Extensions.Logging;
using Phoenix.MsgPackLogic.Protocol;
using System.Text;
using Microsoft.Extensions.Options;

namespace Phoenix.Server.Common
{
    public sealed class RouterNodeManager : ServerNodeManager
    {
        public RouterNodeManager(IOptions<ServerOptions> serverOptions, IOptions<ClusterOptions> clusterOptions) : base(serverOptions, clusterOptions)
        {
        }

        protected override void RegisterMessageHandlers()
        {
            base.RegisterMessageHandlers();
            void handle_REGISTER_MAILBOX(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_RegisterMailBox_Req? registerMailbox = message as MsgPack_S_RegisterMailBox_Req;
                HandleRegisterMailbox(conn, registerMailbox!);
            }

            void handle_UNREGISTER_MAILBOX(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_UnregisterMailBox_Req? unregisterMailbox = message as MsgPack_S_UnregisterMailBox_Req;
                HandleUnRegisterMailbox(conn, unregisterMailbox!);
            }

            void handle_RPC_BY_MAILBOX(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_RpcByMailBox_Proto? rpcByMailbox = message as MsgPack_S_RpcByMailBox_Proto;
                HandleRpcByMailbox(rpcByMailbox!);
            }

            void handle_RPC_TO_PLAYER_ROUTER(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_RpcToPlayerRouter_Proto? rpcToPlayerRouter = message as MsgPack_S_RpcToPlayerRouter_Proto;
                HandleRpcRequestToPlayerRouter(rpcToPlayerRouter!);
            }

            void handle_RPC_TO_NODE(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_RpcToNode_Proto? rpcToNode = message as MsgPack_S_RpcToNode_Proto;

                if (rpcToNode == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                // connid_t connId = conn.ConnId;
                if (rpcToNode.DestNodeId != MyNode.NodeInfo.NodeId)
                {
                    #region
                    // temp py <--> c#
                    // var pydata = msg._rpcData;


                    #endregion

                    // forward rpc to dest node
                    SendNode(rpcToNode.DestNodeId, message);
                    return;
                }
                // handle rpc request
                HandleRpcRequest(rpcToNode.RpcReq);
            }

            void handle_RPC_RESPONSE(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_Rpc_Ack? msg = message as MsgPack_S_Rpc_Ack;

                if (msg == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                // connid_t connId = conn.ConnId;
                if (msg.ReqNode != MyNode.NodeInfo.NodeId)
                {
                    // forward rpc response to request node
                    SendNode(msg.ReqNode, message);
                    return;
                }
                // handle rpc response
                var respNodeId = msg.RespNodeId;
                HandleRpcResponse(respNodeId, msg);
            }

            RegisterMessageHandler((int)EProtoCode.CORE_S_REGISTERMAILBOX_REQ, (handle_REGISTER_MAILBOX));
            RegisterMessageHandler((int)EProtoCode.CORE_S_UNREGISTERMAILBOX_REQ, (handle_UNREGISTER_MAILBOX));
            RegisterMessageHandler((int)EProtoCode.CORE_S_RPCBYMAILBOX_PROTO, (handle_RPC_BY_MAILBOX));
            RegisterMessageHandler((int)EProtoCode.CORE_S_RPCTOPLAYERROUTER_PROTO, (handle_RPC_TO_PLAYER_ROUTER));
            RegisterMessageHandler((int)EProtoCode.CORE_S_RPCTONODE_PROTO, (handle_RPC_TO_NODE));
            RegisterMessageHandler((int)EProtoCode.CORE_S_RPC_ACK, (handle_RPC_RESPONSE));
        }

        //pause player rpc message
        internal override void SetPlayerMsgPending(userid_t id, bool isSpacePlayer)
        {
            if (isSpacePlayer)
            {
                m_spacePlayerPendingMsg[id] = new List<MsgPackStructBase>();
            }
            else
            {
                m_basePlayerPendingMsg[id] = new List<MsgPackStructBase>();
            }
        }

        internal override void FlushPlayerMsg(userid_t playerId, bool isSpacePlayer)
        {
            Dictionary<userid_t, List<MsgPackStructBase>> pendingMsg = isSpacePlayer ? m_spacePlayerPendingMsg : m_basePlayerPendingMsg;
            if(!pendingMsg.TryGetValue(playerId, out var playerPendingMsg))
            {
                s_logger.LogInformation("flush_player_pending_msg_done, PlayerId={PlayerId}, IsSpacePlayer={IsSpacePlayer}", playerId, isSpacePlayer);
                return;
            }

            var routerFunc = isSpacePlayer ? spacePlayerRouter : basePlayerRouter;
            if (routerFunc == null)
            {
                s_logger.LogWarning("find node failed, player router function has not register, PlayerId={PlayerId}, IsSpacePlayer={IsSpacePlayer}", playerId, isSpacePlayer);
                return;
            }

            var playerInNode = routerFunc(playerId);
            foreach (var msg in playerPendingMsg)
            {
                SendNode(playerInNode, msg);
            }
            pendingMsg.Remove(playerId);
            s_logger.LogInformation("flushplayer pending msg done, PlayerId={PlayerId}, IsSpacePlayer={IsSpacePlayer}", playerId, isSpacePlayer);
        }
        protected override void HandleRpcRequestToPlayerRouter(MsgPack_S_RpcToPlayerRouter_Proto playerReq)
        {
            userid_t playerId = playerReq.Uid;
            bool isSpacePlayer = playerReq.IsSpacePlayer;
            MsgPack_S_RpcToPlayer_Proto rpcToPlayer = new();
            rpcToPlayer.IsSpacePlayer = playerReq.IsSpacePlayer;
            rpcToPlayer.Uid = playerReq.Uid;
            rpcToPlayer.RpcReq = playerReq.RpcReq;

            Dictionary<userid_t, List<MsgPackStructBase>> pendingMsg = isSpacePlayer ? m_spacePlayerPendingMsg : m_basePlayerPendingMsg;
            if (pendingMsg.TryGetValue(playerId, out var playerPendingMsg))
            {
                playerPendingMsg.Add(rpcToPlayer);
                return;
            }

            // send response when error occur
            void SendResp(RpcErrCode errCode)
            {
                MsgPack_S_Rpc_Req req = playerReq.RpcReq;
                var needResponse = req.Flag & RpcFlag.RPC_NEED_RESP;
                if (needResponse != 0)
                {
                    return;
                }

                RpcRequestInfo reqInfo = new()
                {
                    ReqId = req.ReqId,
                    NodeId = req.SrcNode,
                    HiddenNodeId = req.HiddenSrcNode,
                    NeedResponse = needResponse != 0
                };
                SendRpcResponse(reqInfo, new byte[0], errCode);
            }

            var routerFunc = isSpacePlayer ? spacePlayerRouter : basePlayerRouter;
            if (routerFunc == null)
            {
                s_logger.LogWarning("find node failed, can not find player in server, PlayerId={PlayerId}, IsSpacePlayer={IsSpacePlayer}", playerId, isSpacePlayer);
                SendResp(RpcErrCode.RPC_ERROR_ENTITY_NOTFOUND);
                return;
            }
            uint playerInNode = routerFunc(playerId);
            if (playerInNode == 0)
            {
                s_logger.LogWarning("find node failed, can not find player in server, PlayerId={PlayerId}, IsSpacePlayer={IsSpacePlayer}", playerId, isSpacePlayer);
                SendResp(RpcErrCode.RPC_ERROR_ENTITY_NOTFOUND);
                return;
            }

            SendNode(playerInNode, rpcToPlayer);
        }

        public void HandleRpcByMailbox(MsgPack_S_RpcByMailBox_Proto mailReq)
        {
            if (!m_mailboxes.TryGetValue(mailReq.MailboxName, out var destNodeId))
            {
                s_logger.LogWarning("[RPC] route mailbox rpc failed, can't find mailbox, MailboxName={MailboxName}", mailReq.MailboxName);
                MsgPack_S_Rpc_Req req = mailReq.RpcReq;
                var needResponse = (req.Flag & RpcFlag.RPC_NEED_RESP) != 0;
                if (!needResponse)
                {
                    return;
                }

                RpcRequestInfo reqInfo = new()
                {
                    ReqId = req.ReqId,
                    NodeId = req.SrcNode,
                    HiddenNodeId = req.HiddenSrcNode,
                    NeedResponse = needResponse
                };
                byte[] packedData = Encoding.UTF8.GetBytes($"Can't find mailbox, MailboxName={mailReq.MailboxName}");
                SendRpcResponse(reqInfo, packedData, RpcErrCode.RPC_ERROR_MAILBOX_NOTFOUND);
                return;
            }

            // change it to RpcRequestToNode
            MsgPack_S_RpcToNode_Proto rpcToNode = new()
            {
                DestNodeId = destNodeId,
                RpcReq = mailReq.RpcReq
            };
            SendNode(destNodeId, rpcToNode);
        }

        private void HandleRegisterMailbox(NodeTcpConnection conn, MsgPack_S_RegisterMailBox_Req msg)
        {
            var mailboxName = msg.MailboxName;
            void SendResponse(RpcErrCode errCode, string errMsg)
            {
                MsgPack_S_RegisterMailBox_Ack resp = new()
                {
                    MailboxName = mailboxName,
                    ErrorCode = (int)errCode,
                    RouterNodeId = MyNode.NodeInfo.NodeId
                };
                DirectSendMsg(conn.ConnId, resp);
            }


            if (m_mailboxes.ContainsKey(mailboxName))
            {
                if(!msg.IsOverride)
                {
                    SendResponse(RpcErrCode.RPC_ERROR_MAILBOX_DUPLICATE, "mailbox already exists");
                    return;
                }

            }

            m_mailboxes[mailboxName] = msg.NodeId;

            SendResponse(RpcErrCode.RPC_NO_ERROR, "mailbox register success");
            s_logger.LogInformation("[RPC]mailbox register success, MailboxName={MailboxName}, NodeId={NodeId}", mailboxName, msg.NodeId);
        }

        public void HandleUnRegisterMailbox(NodeTcpConnection conn, MsgPack_S_UnregisterMailBox_Req msg)
        {
            var mailboxName = msg.MailboxName;
            void SendResponse(RpcErrCode errCode, string errMsg)
            {
                MsgPack_S_UnregisterMailBox_Ack resp = new()
                {
                    MailboxName = mailboxName,
                    ErrorCode = (int)errCode
                };
                DirectSendMsg(conn.ConnId, resp);
            }

            if (!m_mailboxes.ContainsKey(mailboxName))
            {
                s_logger.LogWarning("handle unregister mailbox failed, can not find mailbox, MailboxName={MailboxName}", mailboxName);
                SendResponse(RpcErrCode.RPC_ERROR_MAILBOX_NOTFOUND, $"handle unregister mailbox failed, can not find mailbox, MailboxName={mailboxName}");
                return;
            }
            m_mailboxes.Remove(mailboxName);
            s_logger.LogInformation("handle unregister mailbox succeeded, MailboxName={MailboxName}", mailboxName);
            SendResponse(RpcErrCode.RPC_NO_ERROR, $"handle unregister mailbox succeeded, MailboxName={mailboxName}");
        }

        protected override void _RegisterPlayerRouterFunc(Func<long, uint> func, bool isSpacePlayer)
        {
            if (isSpacePlayer)
            {
                spacePlayerRouter = func;
            }
            else
            {
                basePlayerRouter = func;
            }
        }

        private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<ServerNodeManager>();

        private readonly Dictionary<string, nodeid_t> m_mailboxes = new();
        // base_player means player in base game node, never migrate between node until relogin
        // space_player means player in space node, may migrate between space.
        private Func<long, uint>? basePlayerRouter;
        private Func<long, uint>? spacePlayerRouter;
        private readonly Dictionary<userid_t, List<MsgPackStructBase>> m_basePlayerPendingMsg = new();
        private readonly Dictionary<userid_t, List<MsgPackStructBase>> m_spacePlayerPendingMsg = new();
    }
}
