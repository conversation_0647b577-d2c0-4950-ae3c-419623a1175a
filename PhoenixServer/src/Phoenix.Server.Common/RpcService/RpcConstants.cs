namespace Phoenix.Server.Common
{
    internal static class RpcFlag
    {
        public const int32_t RPC_NEED_RESP = 1;
        public const int32_t FLAG_RPC_BY_MAILBOX = 1 << 1;
        public const int32_t RPC_SHARDED = 1 << 2;
        public const int32_t RPC_INTERNAL = 1 << 3;
        public const int32_t RPC_BROADCAST = 1 << 4;
        public const int32_t RPC_FOWRARD_TO_CLIENT = 1 << 5;
    }

    public enum RpcErrCode
    {
        // rpc return code
        RPC_NO_ERROR,
        RPC_ERROR_ENTITY_NOTFOUND,
        RPC_ERROR_MAILBOX_NOTFOUND,
        RPC_ERRPR_UNKNOWN_CMD,
        RPC_ERROR_TIMEOUT,
        RPC_ERROR_CANCEL,
        RPC_ERROR_MAILBOX_DUPLICATE,
        RPC_ROUTER_NOTFOUND
    }

    public static class RpcConstants
    {
        public static readonly string ERR_CODE = "ecode";
        public static readonly string ERR_MSG = "emsg";
        public static readonly string RESULT = "result";
    }
}
