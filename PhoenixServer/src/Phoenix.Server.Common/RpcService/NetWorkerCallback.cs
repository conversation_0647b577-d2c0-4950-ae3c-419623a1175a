// Copyright (c) Phoenix.All Rights Reserved.

using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Server.Common;

public class NetWorkerCallback
{
    public Action<NodeTcpConnection, MsgPackStructBase>? OnHandleRead;
    public Action<NodeTcpConnection>? OnHandleClose;
    public Action<NodeTcpConnection>? OnHandleConnected;
    public Action<NodeTcpConnection>? OnHandleConnectFail;
    public Action<NodeTcpConnection>? OnHandleAccepted;
}
