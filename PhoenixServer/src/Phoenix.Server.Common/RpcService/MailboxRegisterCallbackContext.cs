using Microsoft.Extensions.Logging;

namespace Phoenix.Server.Common
{
    internal class MailboxRegisterCallbackContext
    {
        public MailboxRegisterCallbackContext(DelegateMailboxRegisterCallback? callback, HashSet<nodeid_t> waitingWorld)
        {
            m_callback = callback;
            m_waitingWorldResp = waitingWorld;
            ExpiredAt = RpcService.Instance.CurrentMilliseconds + ExpiredTimeMs;
        }

        internal bool OnRecv(string mailboxName, bool success, nodeid_t nodeId)
        {
            if (success)
            {
                s_logger.LogInformation("register mailbox success,  MailboxName={ MailboxName}, RouterNodeId={RouterNodeId}", mailboxName, nodeId);
            }
            else
            {
                s_logger.LogWarning("register mailbox failed,  MailboxName={ MailboxName}, RouterNodeId={RouterNodeId}", mailboxName, nodeId);
            }

            if (m_waitingWorldResp.Contains(nodeId))
            {
                m_waitingWorldResp.Remove(nodeId);
            }

            if (success)
            {
                m_mailboxReqSuccessNum++;
            }

            if (m_waitingWorldResp.Count > 0)
            {
                return false;
            }

            s_logger.LogInformation("All world router has register mailbox, MailboxName={MailboxName}", mailboxName);
            if (m_callback != null)
            {
                m_callback(mailboxName, success, MailBoxManager.AllRouter);
            }
            return true;
        }

        public bool OnExpired(string mailboxName, int worldRouterNum)
        {
            s_logger.LogWarning("register mailbox expired, MailboxName={MailboxName}, MailboxReqSuccessNum={MailboxReqSuccessNum}, unregistered_world_router={unregistered_world_router}",
             mailboxName, m_mailboxReqSuccessNum, m_waitingWorldResp.ToString());
            bool success = m_mailboxReqSuccessNum == (ulong)worldRouterNum;
            if (m_callback != null)
            {
                m_callback( mailboxName, success, MailBoxManager.AllRouter);
            }
            return true;
        }

        private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<MailboxRegisterCallbackContext>();

        // if 60s expired, then callback
        private const uint ExpiredTimeMs = 60 * 1000;

        private readonly HashSet<nodeid_t> m_waitingWorldResp;
        private readonly DelegateMailboxRegisterCallback? m_callback;
        public int64_t ExpiredAt { get; private set; }
        private size_t m_mailboxReqSuccessNum;
    }
}
