using DotNetty.Transport.Bootstrapping;
using DotNetty.Transport.Channels.Sockets;
using DotNetty.Transport.Channels;
using Microsoft.Extensions.Logging;
using System.Net;
using DotNetty.Handlers.Logging;
using DotNetty.Common.Utilities;
using DotNetty.Transport.Libuv;
using DotNetty.Buffers;
using Phoenix.MsgPackLogic.Protocol;
using DotNetty.Codecs.Protobuf;
using Phoenix.Codecs;

namespace Phoenix.Server.Common
{
    public class NetWorkManager
    {
        public static readonly NetWorkManager Instance = new();
        public static AttributeKey<NodeTcpConnection> KeyNodeTcpConnection = AttributeKey<NodeTcpConnection>.ValueOf("NodeTcpConnection");

        private NetWorkManager() { }

        public void Init()
        {
            try
            {
                m_clientBootstrap
                    .Group(m_clientWorkerGroup)
                    .Channel<TcpSocketChannel>()
                    .Option(ChannelOption.TcpNodelay, true)
                    .Option(ChannelOption.Allocator, PooledByteBufferAllocatorInitializer.Instance.Allocator)
                    .Handler(new ActionChannelInitializer<ISocketChannel>(channel =>
                    {
                        IChannelPipeline pipeline = channel.Pipeline;
                        pipeline.AddLast(new LoggingHandler());
                        pipeline.AddLast("framing-size-dec", new ProtobufVarint32FrameDecoder());
                        pipeline.AddLast("framing-size-enc", new ProtobufVarint32LengthFieldPrepender());
                        pipeline.AddLast("framing-enc", new MessagePackEncoder<MsgPackStructBase>());
                        pipeline.AddLast("framing-dec", new MessagePackDecoder<MsgPackStructBase>());
                        pipeline.AddLast(new NodeClientHandler());
                    }));
            }
            catch (Exception ex)
            {
                s_logger.LogError(ex, "NetWorkManager.Init failed");
            }
        }

        public async void StartListen(ushort port, ConnectionManager connectionManager)
        {
            try
            {
                m_serverBootstrap = new();
                m_serverBootstrap.Group(m_serverBossGroup, m_serverWorkerGroup)
                    .Channel<TcpServerSocketChannel>()
                    .Option(ChannelOption.SoBacklog, 100)
                    .Option(ChannelOption.TcpNodelay, true)
                    .Option(ChannelOption.SoReuseaddr, true)
                    .Option(ChannelOption.Allocator, PooledByteBufferAllocatorInitializer.Instance.Allocator)
                    .ChildOption(ChannelOption.Allocator, PooledByteBufferAllocatorInitializer.Instance.Allocator)
                    .Handler(new LoggingHandler("SRV-LSTN"))
                    .ChildHandler(new ActionChannelInitializer<IChannel>(channel =>
                    {
                        IChannelPipeline pipeline = channel.Pipeline;
                        pipeline.AddLast(new LoggingHandler("SRV-CONN"));
                        pipeline.AddLast("framing-size-dec", new ProtobufVarint32FrameDecoder());
                        pipeline.AddLast("framing-size-enc", new ProtobufVarint32LengthFieldPrepender());
                        pipeline.AddLast("framing-enc", new MessagePackEncoder<MsgPackStructBase>());
                        pipeline.AddLast("framing-dec", new MessagePackDecoder<MsgPackStructBase>());
                        pipeline.AddLast(new NodeServerHandler(connectionManager));
                    }));


                m_serverBoundChannel = await m_serverBootstrap.BindAsync(port);
            }
            catch (Exception ex)
            {
                s_logger.LogError(ex, "StartListen.Init failed");
            }
        }

        private async Task StopListen()
        {
            if (m_serverBoundChannel != null)
            {
                await m_serverBoundChannel.CloseAsync();
                await Task.WhenAll(
                    m_serverBossGroup.ShutdownGracefullyAsync(TimeSpan.FromMilliseconds(100), TimeSpan.FromSeconds(1)),
                    m_serverWorkerGroup.ShutdownGracefullyAsync(TimeSpan.FromMilliseconds(100), TimeSpan.FromSeconds(1)));
            }
        }

        public async Task Stop()
        {
            await m_clientWorkerGroup.ShutdownGracefullyAsync(TimeSpan.FromMilliseconds(100), TimeSpan.FromSeconds(100));
            await StopListen();
        }

        public async Task<IChannel> NewConnect(IPEndPoint endPoint)
        {
            return await m_clientBootstrap.ConnectAsync(endPoint);
        }

        private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<NetWorkManager>();

        private readonly Bootstrap m_clientBootstrap = new();
        private readonly IEventLoopGroup m_clientWorkerGroup = new EventLoopGroup();

        private IChannel? m_serverBoundChannel;
        private ServerBootstrap? m_serverBootstrap;
        //private DispatcherEventLoopGroup? ServerBossGroup;
        private readonly MultithreadEventLoopGroup m_serverBossGroup = new MultithreadEventLoopGroup(1);

        private readonly MultithreadEventLoopGroup m_serverWorkerGroup = new MultithreadEventLoopGroup();
        //private IEventLoopGroup? ServerWorkerGroup;
    }
}
