using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Server.Common
{
    public class RpcLogicCallback
    {
        public Action<uint, string>? OnNodeConnected;
        public Action<uint, string>? OnNodeConntionClose;
        public Action<uint, MsgPack_S_Rpc_Req>? OnRpcRequest;
        public Action<uint, MsgPack_S_Rpc_Ack>? OnRpcResponse;

        public Action<MsgPack_S_ServerNodeInfo_Proto>? OnNodeJoin;
        public Action<MsgPack_S_ServerNodeInfo_Proto>? OnNodeLeave;

        public Action<MsgPack_S_RpcToPlayer_Proto>? OnRpcRequestToPlayer;
        public Action? OnRpcReady;
        public Action<MsgPack_S_BroadcastData_Proto>? OnRecvBroadcastData;
    }
}
