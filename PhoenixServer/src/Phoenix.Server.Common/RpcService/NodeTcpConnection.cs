using Microsoft.Extensions.Logging;
using DotNetty.Transport.Channels;
using System.Diagnostics.Contracts;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Utils;
using System.Net;

namespace Phoenix.Server.Common
{
    public class NodeTcpConnection : Connection
    {
        public NodeTcpConnection(string ip, ushort port, uint connId)
        {
            ConnType = ConnType.TCP;
            PeerIp = ip;
            PeerPort = port;
            ConnId = connId;
            IsFromAccept = false;
        }

        public NodeTcpConnection(IChannel channel)
        {
            Channel = channel;
            ParseEndPoint(channel.RemoteAddress);
            ConnType = ConnType.TCP;
            ConnectedBeginTime = DateTimeUtils.GetSecondsNowSinceEpoch();
        }

        public void Connect()
        {
            if (IsConnecting)
            {
                return;
            }

            IsConnecting = true;
            Task.Run(async () =>
            {
                try
                {
                    ConnectedBeginTime = DateTimeUtils.GetSecondsNowSinceEpoch();
                    IChannel channel = await NetWorkManager.Instance.NewConnect(new IPEndPoint(IPAddress.Parse(PeerIp), (short)PeerPort));
                    Channel = channel;
                    ParseEndPoint(channel.RemoteAddress);
                    channel.GetAttribute<NodeTcpConnection>(NetWorkManager.KeyNodeTcpConnection).Set(this);
                    s_logger.LogInformation("Connect success NodeId={NodeId}, ConnId={ConnId} Ip={Ip}, Port={Port}", NodeId, ConnId, PeerIp, PeerPort);
                    OnConnected(true);
                }
                catch (Exception ex)
                {
                    OnConnected(false);
                    s_logger.LogWarning("Connect failed NodeId={NodeId}, ConnId={ConnId} Ip={Ip}, Port={Port}, exception={Exception}", NodeId, ConnId, PeerIp, PeerPort, ex.Message);
                }
                finally
                {
                    IsConnecting = false;
                }
            });
        }

        private void ParseEndPoint(EndPoint endPoint)
        {
            if (endPoint is IPEndPoint ipEndPoint)
            {
                PeerIp = ipEndPoint.Address.ToString();
                PeerPort = (ushort)ipEndPoint.Port;
            }
        }

        private void OnConnected(bool success)
        {
            if (IsClosed)
            {
                return;
            }

            if (success)
            {
                ConnectedBeginTime = DateTimeUtils.GetSecondsNowSinceEpoch();
                Contract.Assert(m_connectionManager.cbHandleConnected != null);
                m_connectionManager.cbHandleConnected(this);
            }
            else
            {
                Contract.Assert(m_connectionManager.cbHandleConnectFail != null);
                m_connectionManager.cbHandleConnectFail(this);
                CloseSocket();
            }
        }

        private void CloseSocket()
        {
            if (IsClosed)
            {
                return;
            }

            IsClosed = true;

            if (Channel != null)
            {
                Task.Run(async () =>
                {
                    try
                    {
                        await Channel.DisconnectAsync();
                    }
                    catch (Exception ex)
                    {
                        s_logger.LogError(ex, "CloseSocket failed");
                    }
                });
            }

            CleanUp();
        }

        private void CleanUp()
        {
            m_connectionManager.cbHandleClose!(this);
            m_connectionManager.UnRegConnection(this);
        }

        public override void Disconnect(ConnDisconnectReason reason)
        {
            if (IsDisconnected)
            {
                return;
            }

            IsDisconnected = true;
            s_logger.LogInformation("Disconnect, Reason={Reason} ConnId={ConnId}, Ip={Ip}, Port={Port}", reason, ConnId, PeerIp, PeerPort);
            if (!IsSendingData)
            {
                CloseSocket();
            }
        }

        internal void OnRead(MsgPackStructBase message)
        {
            m_connectionManager.cbHandleRead!(this, message);
        }

        internal override void WriteData(MsgPackStructBase message)
        {
            try
            {
                if(Channel != null)
                {
                    Channel.WriteAndFlushAsync(message);
                }
            }
            catch (Exception e)
            {
                OnException(e);
            }
        }

        internal void SetManager(ConnectionManager connectionManager)
        {
            m_connectionManager = connectionManager;
        }

        internal void OnDisconnect()
        {
            s_logger.LogInformation("OnDisconnect, ConnId={ConnId}", ConnId);
            CloseSocket();
        }

        internal void OnException(Exception exception)
        {
            s_logger.LogInformation("OnException, ConnId={ConnId} RemoteIp={RemoteIp} RemotePort={RemotePort} IsFromAccept={IsFromAccept} exception={exception}", ConnId, RemoteIp(), RemotePort(), IsFromAccept, exception.Message);
            CloseSocket();
        }

        internal ushort RemotePort()
        {
            return PeerPort;
        }

        internal string RemoteIp()
        {
            return PeerIp;
        }

        internal void OnAccepted()
        {
            if (m_connectionManager.cbHandleAccepted != null)
            {
                m_connectionManager.cbHandleAccepted(this);
            }
        }

        private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<NodeTcpConnection>();

        public string PeerIp { get; private set; } = string.Empty;
        public ushort PeerPort { get; private set; }
        public int64_t EntityId { get; private set; }          //entity id (used for binding userid/node_id, etc.)

        public nodeid_t NodeId { get; set; }
        public IChannel? Channel { get; private set; } = null!;

        private bool IsClosed;
        private long ConnectedBeginTime; // connectied begin time
        private bool IsSendingData;
        private bool IsDisconnected;
        private bool IsConnecting;    //Is true when TCP connection handshake completed
        private ConnectionManager m_connectionManager = null!;
        public bool IsFromAccept { get; private set; }
    }
}
