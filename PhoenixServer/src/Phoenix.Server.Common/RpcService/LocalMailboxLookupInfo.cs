
namespace Phoenix.Server.Common
{
    public sealed class LocalMailboxLookupInfo
    {
        public LocalMailboxLookupInfo(ServerEntity entity, CallbackOnRegisterRes? cbOnRegisterRes = null)
        {
            EntityId = entity?.EntityId ?? 0;
            State = MailboxNotRegistered;
            CallbackOnRegisterRes = cbOnRegisterRes;
            MailboxMgrId = 0;
            IsBroadcastRegister = false;
        }

        // Get entity instance
        public ServerEntity? GetEntity()
        {
            return EntityAdmin.Instance.GetEntity(EntityId);
        }

        // Set entity instance
        public void SetEntity(ServerEntity entity)
        {
            EntityId = entity.EntityId;
        }

        // Constants for mailbox registration state
        public const int MailboxHasRegistered = 1;
        public const int MailboxNotRegistered = 0;

        // Properties
        public long EntityId { get; private set; }
        public int State { get; set; }
        public CallbackOnRegisterRes? CallbackOnRegisterRes { get; set; }
        public nodeid_t MailboxMgrId { get; set; }
        public bool IsBroadcastRegister { get; set; }
    }
}
