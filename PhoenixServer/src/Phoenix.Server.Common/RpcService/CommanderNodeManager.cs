using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Server.Common
{
    public sealed class CommanderNodeManager : ServerNodeManager
    {
        public CommanderNodeManager(IOptions<ServerOptions> serverOptions, IOptions<ClusterOptions> clusterOptions) : base(serverOptions, clusterOptions)
        {
        }

        public override void Tick()
        {
            if (IsShutdown)
            {
                return;
            }

            if (!IsMaster())
            {
                // connect to master commander
                WatchMasterCommander();
                return;
            }

            m_needRetryRoles.Clear();
            // 1. check keepalive
            m_deadNodes.Clear();
            long current = CurrentMilliseconds;
            foreach (var pair in NodeId2ServerNodeInfo)
            {
                var node = pair.Value;
                if (node == GetMyNode())
                {
                    continue;
                }

                if (current - node.GetLastHeartbeatTime() > ServerOptions.ServerHeartbeatTimeout * 1000)
                {
                    nodeid_t nodeId = node.NodeInfo.NodeId;
                    connid_t connId = node.ConnId;
                    m_deadNodes.Add(new KeyValuePair<connid_t, nodeid_t>(connId, nodeId));
                }
            }

            foreach (var pair in m_deadNodes)
            {
                s_logger.LogWarning("ServerHeartbeatTimeout, node={NodeId}, connId={ConnId}", pair.Value, pair.Key);
                HandleNodeLeave(pair.Key, pair.Value);
            }

            // 2. retry consistent hash transaction

            foreach (var pair in m_hashSyncDelay)
            {
                if (current - pair.Value > DelaySyncInterval)
                {
                    m_needRetryRoles.Add(pair.Key);
                }
            }

            foreach (var pair in m_hashSyncContexts)
            {
                var context = pair.Value;
                if (current - context.CreateTime > BroadcastTimeInterval)
                {
                    m_needRetryRoles.Add(pair.Key);
                }
            }

            foreach (string role in m_needRetryRoles)
            {
                var chash = GetConsistentHashByRole(role);
                if (chash == null)
                {
                    s_logger.LogWarning("SyncConsistentHashFailed, can't find consistent hash cache for Role={Role}", role);
                    continue;
                }
                SyncConsistentHash(role, chash);
            }

            // 3. broadcast hash digest
            BroadcastHashDigest();
        }

        protected override void HandleNodeLeave(uint connId, uint nodeId)
        {
            if (MyNode.NodeInfo.NodeId == nodeId)
            {
                return;
            }

            ServerNode? node = FindNode(nodeId);
            if (node == null)
            {
                return;
            }

            // 0. update consistent hash
            string role = node.NodeInfo.Role;
            var chash = GetConsistentHashByRole(role);
            if (chash != null && chash.ContainsNode(nodeId))
            {
                chash.DelNode(nodeId);
            }

            // 1. broadcast node_leave
            MsgPack_S_NodeLeave_Ntf nodeLeaveNotify = new()
            {
                NodeId = nodeId
            };

            foreach (var pair in NodeId2ServerNodeInfo)
            {
                if (pair.Key == nodeId)
                {
                    continue;
                }

                node = pair.Value;
                if (node.IsActive() == false)
                {
                    continue;
                }

                SendNode_CR(node, nodeLeaveNotify);
            }

            base.HandleNodeLeave(connId, nodeId);

            chash = GetConsistentHashByRole(role);
            if (chash != null)
            {
                SyncConsistentHash(role, chash);
            }
        }

        protected override void InitMasterCommander()
        {
            base.InitMasterCommander();

            // check if i am the master commander
            if (MyNode.AddressEqual(MasterCommander))
            {
                // i am master commander
                if (State == ECommanderState.MASTER)
                {
                    return; // nothing happened
                }

                State = ECommanderState.MASTER;
                var nodeInfo = MyNode.NodeInfo;
                s_logger.LogInformation("FromSlaveToMaster, NodeId={NodeId}, addr={Ip}:{Port}, role={Role}",
                                nodeInfo.NodeId, nodeInfo.Ip, nodeInfo.Port, nodeInfo.Role);
                // reset all node heartbeat time, otherwise, all node will be removed
                foreach (var pair in NodeId2ServerNodeInfo)
                {
                    pair.Value.SetLastHeartbeatTime(CurrentMilliseconds);
                }
                m_lastBroadcastTime = CurrentMilliseconds;
                if (SwitchToMasterCallback != null)
                {
                    SwitchToMasterCallback();
                }

                foreach (var nodeChash in Role2ConsistentHash)
                {
                    if (nodeChash.Value.GetNodeCount() == 0)
                    {
                        continue;
                    }

                    m_hashSyncDelay[nodeChash.Value.HashName] = CurrentMilliseconds;
                }
            }
            else
            {
                // i am slave commander
                if (State == ECommanderState.SLAVE)
                {
                    return; // nothing happened
                }

                State = ECommanderState.SLAVE;
                MsgPack_S_ServerNodeInfo_Proto nodeInfo = MyNode.NodeInfo;
                s_logger.LogInformation("FromMasterToSlave, NodeId={NodeId}, Addr={Ip}:{Port}, Role={Role}",
                                nodeInfo.NodeId, nodeInfo.Ip, nodeInfo.Port, nodeInfo.Role);
                if (SwitchToSlaveCallback != null)
                {
                    SwitchToSlaveCallback();
                }
            }
            return;
        }

        protected override void HandleConsistentHashUpdate(MsgPack_S_UpdateConsistentHash_Proto msg)
        {
            // master commander will not receive this msg
            if (IsMaster())
            {
                return;
            }

            s_logger.LogInformation("HandleConsistentHashUpdate, Role={Role}", msg.Role);
            var consistentHash = GetConsistentHashByRole(msg.Role);
            if (consistentHash == null)
            {
                consistentHash = CreateConsistentHash(msg.Role);
            }

            System.Diagnostics.Debug.Assert(consistentHash != null);

            if (!consistentHash.ParseFromString(msg.ConsistentHashData))
            {
                s_logger.LogError("ParseConsistentHashData failed, Role={Role}", msg.Role);
            }
        }

        protected override void RegisterMessageHandlers()
        {
            base.RegisterMessageHandlers();
            void handle_S_GET_CONSISTENT_HASH(NodeTcpConnection nodeConn, MsgPackStructBase message)
            {
                MsgPack_S_GetConsistentDigest_Proto? msg = message as MsgPack_S_GetConsistentDigest_Proto;

                if (msg == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                s_logger.LogInformation("[RPC] handle_S_GET_CONSISTENT_HASH GetConsistentHash, NodeId={NodeId}, Role={Role}", nodeConn.NodeId, msg.Role);

                if (!m_hashCaches.TryGetValue(msg.Role, out var hashCaches))
                {
                    s_logger.LogError("Can not find consistent hash, Role={Role}", msg.Role);
                    return;
                }

                ServerNode? node = FindNode(nodeConn.NodeId);
                if (node == null)
                {
                    return;
                }

                MsgPack_S_UpdateConsistentHash_Proto hashUpdate = new()
                {
                    Role = msg.Role
                };
                ;
                if (node.NodeInfo.Role == RoleName.ROLE_COMMANDER)
                {
                    hashUpdate.ConsistentHashData = hashCaches.PackedHashDataToCommander!;
                }
                else
                {
                    hashUpdate.ConsistentHashData = hashCaches.PackedHashData!;
                }

                DirectSendMsg(nodeConn.ConnId, hashUpdate);
            }

            void handle_S_TRANS_UPDATE_CONSISTENT_HASH_ACK(NodeTcpConnection conn, MsgPackStructBase message)
            {
                MsgPack_S_TransUpdateConsistentHash_Ack? msg = message as MsgPack_S_TransUpdateConsistentHash_Ack;
                if (msg == null)
                {
                    throw new Exception($"message convert failed {message}");
                }

                s_logger.LogInformation("[RPC]handle_S_TRANS_UPDATE_CONSISTENT_HASH_ACK NodeId={NodeId} Role={Role}, TransId={TransId}, State={State}", conn.NodeId, msg.Role, msg.TransId, (ConsistentHashSyncState)msg.TransState);

                if (!m_hashSyncContexts.TryGetValue(msg.Role, out var context))
                {
                    return;
                }

                var transState = (ConsistentHashSyncState)(msg.TransState);
                if (context.TransId != msg.TransId || context.State != transState)
                {
                    return;
                }

                if (context.NodeStates[conn.NodeId] >= transState)
                {
                    return;
                }

                context.NodeStates[conn.NodeId] = transState;
                foreach (var pair in context.NodeStates)
                {
                    if (pair.Value != context.State)
                    {
                        return;
                    }
                }

                switch (context.State)
                {
                    case ConsistentHashSyncState.INIT:
                        {
                            s_logger.LogInformation("[RPC]handle_S_TRANS_UPDATE_CONSISTENT_HASH_ACK SYNC_HASH, NodeId={NodeId} Role={Role}, TransId={TransId}, State={State}", conn.NodeId, msg.Role, msg.TransId, (ConsistentHashSyncState)msg.TransState);
                            return;
                        }
                    case ConsistentHashSyncState.SYNC_HASH:
                        {
                            s_logger.LogInformation("[RPC]handle_S_TRANS_UPDATE_CONSISTENT_HASH_ACK SYNC_HASH, NodeId={NodeId} Role={Role}, TransId={TransId}, State={State}", conn.NodeId, msg.Role, msg.TransId, (ConsistentHashSyncState)msg.TransState);
                            context.State = ConsistentHashSyncState.COMMIT;
                            MsgPack_S_TransUpdateConsistentHash_Req updateMsg = new() { TransId = msg.TransId, TransState = (byte)context.State, Role = msg.Role };

                            foreach (var pair in context.NodeStates)
                            {
                                SendNode_CR(pair.Key, updateMsg);
                            }

                            break;
                        }
                    case ConsistentHashSyncState.COMMIT:
                        {
                            s_logger.LogInformation("[RPC]handle_S_TRANS_UPDATE_CONSISTENT_HASH_ACK COMMIT, NodeId={NodeId} Role={Role}, TransId={TransId}, State={State}", conn.NodeId, msg.Role, msg.TransId, (ConsistentHashSyncState)msg.TransState);

                            UpdateConsistentHashToAllNodes(msg.Role, context);
                            break;
                        }
                }
            }

            RegisterMessageHandler((int)EProtoCode.CORE_S_GETCONSISTENTDIGEST_PROTO, handle_S_GET_CONSISTENT_HASH);
            RegisterMessageHandler((int)EProtoCode.CORE_S_TRANSUPDATECONSISTENTHASH_ACK, handle_S_TRANS_UPDATE_CONSISTENT_HASH_ACK);
        }

        private void UpdateConsistentHashToAllNodes(string role, ConsistentHashSyncContext context)
        {
            m_hashCaches[role] = new ConsistentHashCache(context.PackedHashData, context.PackedHashDataToCommander, context.Digest);

            MsgPack_S_UpdateConsistentHash_Proto updateMsgToCommander = new()
            {
                Role = role,
                ConsistentHashData = context.PackedHashDataToCommander!
            };

            MsgPack_S_UpdateConsistentHash_Proto updateMsgToOther = new()
            {
                Role = role,
                ConsistentHashData = context.PackedHashData!
            };

            foreach (var pair in NodeId2ServerNodeInfo)
            {
                var node = pair.Value;
                if (node == MyNode || !node.IsActive())
                {
                    continue;
                }

                if (node.NodeInfo.Role == RoleName.ROLE_COMMANDER)
                {
                    SendNode_CR(node, updateMsgToCommander);
                }
                else
                {
                    SendNode_CR(node, updateMsgToOther);
                }
            }

            m_hashSyncContexts.Remove(role);
        }

        protected override bool CheckRpcInitReady()
        {
            if (null == MasterCommander)
            {
                return false;
            }

            if (!IsMaster() && !MasterCommander.IsActive())
            {
                return false;
            }

            if (RpcReady)
            {
                return true;
            }

            SetRpcReady();
            return true;
        }

        private void BroadcastHashDigest()
        {
            long current = CurrentMilliseconds;
            // 20s
            if (current - m_lastBroadcastTime < BroadcastTimeInterval)
            {
                return;
            }

            m_lastBroadcastTime = current;
            MsgPack_S_CheckConsistentDigest_Proto checkMsg = new();
            //periodically broadcast latest consistent hash digest
            List<MsgPack_S_CheckConsistentDigestInfo> nodeInfoList = checkMsg.Digests = new();
            foreach (KeyValuePair<string, ConsistentHashCache> cache in m_hashCaches)
            {
                var msg = new MsgPack_S_CheckConsistentDigestInfo
                {
                    Role = cache.Key,
                    Digest = cache.Value.Digest
                };
                nodeInfoList.Add(msg);
            }

            Broadcast(checkMsg);
        }

        private bool IsMaster()
        {
            return State == ECommanderState.MASTER;
        }

        private void SyncConsistentHash(string role, ConsistentHash chash)
        {
            s_logger.LogDebug("SyncConsistentHash, Role={Role}", role);
            ConsistentHashSyncContext context = new()
            {
                TransId = m_nextHashTransId++,
                CreateTime = CurrentMilliseconds,
                State = ConsistentHashSyncState.SYNC_HASH,
                Digest = chash.Digest,
                PackedHashData = chash.SerializeSlotsToString(),
                PackedHashDataToCommander = chash.SerializeToString()
            };

            MsgPack_S_TransUpdateConsistentHash_Req transUpdate = new()
            {
                TransId = context.TransId,
                TransState = (byte)context.State,
                Role = chash.HashName,
                ConsistentHashData = context.PackedHashData
            };

            int nodeCount = 0;
            foreach (var pair in NodeId2ServerNodeInfo)
            {
                var node = pair.Value;
                if (node.IsActive() == false || node.NodeInfo.Role != role)
                {
                    continue;
                }

                nodeCount++;
                context.NodeStates[pair.Key] = ConsistentHashSyncState.INIT;
                SendNode_CR(node, transUpdate);
            }

            if (nodeCount == 0)
            {
                s_logger.LogCritical("ConsistentHash has no available node, NodeCount is zero Role={Role}", role);
                UpdateConsistentHashToAllNodes(role, context);
            }
            else
            {
                m_hashSyncContexts[role] = context;
            }

            m_hashSyncDelay.Remove(role);
        }

        protected override void OnConnClose(uint connId, uint nodeId)
        {
            base.OnConnClose(connId, nodeId);
            if (IsMaster())
            {
                HandleNodeLeave(connId, nodeId);
            }
        }

        protected override void HandleConnectRequest(uint connId, MsgPack_S_ServerNodeInfo_Proto nodeInfo)
        {
            if (!IsMaster())
            {
                s_logger.LogWarning("RejectConnectRequest, {NodeId} is not master commander, ReqFrom: NodeId={NodeId}, Addr={Ip}:{Port}, Role={Role}",
                                    MyNode.NodeInfo.NodeId, nodeInfo.NodeId, nodeInfo.Ip, nodeInfo.Port, nodeInfo.Role);
                CloseNodeConnect(nodeInfo.NodeId);
                return;
            }
            var role = nodeInfo.Role;
            var rawNodeInfo = nodeInfo;
            nodeid_t nodeId = nodeInfo.NodeId;
            s_logger.LogInformation("[RPC]BroadcastNodeJoin, NodeId={NodeId}, Addr={Ip}:{Port}, Role={Role}", nodeInfo.NodeId, nodeInfo.Ip, nodeInfo.Port, nodeInfo.Role);
            base.HandleConnectRequest(connId, nodeInfo);

            MsgPack_S_NodeInfoList_Proto nodeInfoList = new();
            // 0. update consistent hash
            var chash = GetConsistentHashByRole(role);
            if (chash != null && chash.AddNode(nodeId))
            {
                m_hashSyncDelay[role] = CurrentMilliseconds;
            }

            // 1. broadcast node_join
            MsgPack_S_NodeJoin_Ntf nodeJoin = new()
            {
                NodeInfo = nodeInfo
            };

            foreach (var pair in NodeId2ServerNodeInfo)
            {
                if (pair.Key == nodeId)
                {
                    continue;
                }

                var node = pair.Value;
                if (node.IsActive() == false)
                {
                    continue;
                }

                SendNode_CR(node, nodeJoin);
                nodeInfoList.NodeInfoList.Add(node.NodeInfo);
            }

            // 2. sync node_map to this node
            if (nodeInfoList.NodeInfoList.Count > 0)
            {
                DirectSendMsg(connId, nodeInfoList);
            }

            // 3. sync all consistent hash to this node
            SendAllConsistentHash(connId, role == RoleName.ROLE_COMMANDER);

            // 4. save node info
            OnNodeJoin(rawNodeInfo);

            // 5. if all node have been ready
            if (rawNodeInfo.IsReady)
            {
                HandleNodeReady(connId, nodeId);
            }
        }

        private void SendAllConsistentHash(connid_t connId, bool isToCommander)
        {
            foreach (var pair in m_hashCaches)
            {
                MsgPack_S_UpdateConsistentHash_Proto hashUpdate = new()
                {
                    Role = pair.Key
                };
                if (isToCommander)
                {
                    hashUpdate.ConsistentHashData = pair.Value.PackedHashDataToCommander!;
                }
                else
                {
                    hashUpdate.ConsistentHashData = pair.Value.PackedHashData!;
                }

                DirectSendMsg(connId, hashUpdate);
            }
        }

        protected override void HandleNodeReady(uint connId, uint nodeId)
        {
            if (IsMaster())
            {
                var myNodeId = MyNode.NodeInfo.NodeId;
                MsgPack_S_NodeReady_Proto nodeReady = new()
                {
                    NodeId = nodeId
                };
                MsgPack_S_NodesReady_Proto nodesReady = new();
                var nodeList = nodesReady.ReadyNodeList = new();
                foreach (var pair in NodeId2ServerNodeInfo)
                {
                    var node = pair.Value;
                    if (!node.IsLogicReady())
                    {
                        continue;
                    }
                    // send ready to other node
                    if (node.NodeInfo.NodeId != myNodeId)
                    {
                        SendNode_CR(node, nodeReady);
                    }
                    nodeList.Add(node.NodeInfo.NodeId);
                }
                // send nodes have been ready to this node
                nodeList.Add(nodeId);
                DirectSendMsg(connId, nodesReady);
            }

            base.HandleNodeReady(connId, nodeId);
        }

        public override void RegMasterCommanderSwitchCallback(Action switchToMasterCallback, Action switchToSlaveCallback)
        {
            if (ServerOptions.Role != RoleName.ROLE_COMMANDER)
            {
                return;
            }

            RegisterMasterSwitchCallback(switchToMasterCallback, switchToSlaveCallback);
        }

        private void RegisterMasterSwitchCallback(Action switchToMasterCallback, Action switchToSlaveCallback)
        {
            SwitchToMasterCallback = switchToMasterCallback;
            SwitchToSlaveCallback = switchToSlaveCallback;
        }

        public override void OnMasterSwitch(MsgPack_S_ServerNodeInfo_Proto masterNodeInfo)
        {
            base.OnMasterSwitch(masterNodeInfo);
            if (MyNode.NodeInfo.Equals(masterNodeInfo))
            {
                State = ECommanderState.MASTER;
                foreach ((string role, ConsistentHash chash) in Role2ConsistentHash)
                {
                    m_hashSyncDelay[role] = CurrentMilliseconds;
                }
                SwitchToMasterCallback?.Invoke();
            }
            else
            {
                State = ECommanderState.SLAVE;
                m_hashCaches.Clear();
                SwitchToSlaveCallback?.Invoke();
            }
        }

        private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<CommanderNodeManager>();

        private const int64_t DelaySyncInterval = 10000;
        private const int64_t BroadcastTimeInterval = 20 * 1000; // 20s

        private int64_t m_lastBroadcastTime;
        public ECommanderState State = ECommanderState.SLAVE;
        public Action? SwitchToMasterCallback;
        public Action? SwitchToSlaveCallback;
        private int64_t m_nextHashTransId;
        private readonly Dictionary<string, ConsistentHashSyncContext> m_hashSyncContexts = new();
        private readonly Dictionary<string, int64_t> m_hashSyncDelay = new();
        private readonly Dictionary<string, ConsistentHashCache> m_hashCaches = new();
        private readonly List<KeyValuePair<connid_t, nodeid_t>> m_deadNodes = new();
        private readonly HashSet<string> m_needRetryRoles = new();
    }

    internal class ConsistentHashSyncContext
    {
        public int64_t TransId;
        public int64_t CreateTime;
        public uint32_t Digest;
        public byte[]? PackedHashData;
        public byte[]? PackedHashDataToCommander;
        public ConsistentHashSyncState State;
        public Dictionary<nodeid_t, ConsistentHashSyncState> NodeStates = new();
    }

    public enum ECommanderState
    {
        MASTER,
        SLAVE,
    };

    internal class ConsistentHashCache
    {
        public ConsistentHashCache(byte[]? data, byte[]? dataToCommander, uint32_t digest)
        {
            PackedHashData = data;
            PackedHashDataToCommander = dataToCommander;
            Digest = digest;
        }

        public byte[]? PackedHashData;
        public byte[]? PackedHashDataToCommander;
        public uint32_t Digest;
    };
}
