// Copyright (c) Phoenix.All Rights Reserved.

using System.ComponentModel;
using Phoenix.Server.Utils;

namespace Phoenix.Server.Common;
public class Entity
{

    public Entity(entity_id_t entityId = 0)
    {
        if (entityId == 0)
        {
            EntityId = IdGenerator.Instance.GenGUID_Int64();
        }
        else
        {
            EntityId = entityId;
        }

        CreateComponents();
    }

    public virtual void CreateComponents()
    {

    }

    protected void AddComponent(Component comp)
    {
        Components.Add(comp);
        // Name2Component.Add(comp.GetComponentName(), comp);
    }

    public virtual void Init(object data)
    {

    }

    public entity_id_t EntityId { get; private set; }

    // user id (same with GameServiceAvatar's uid in game)
    public int64_t UserId { get; set; }
    public virtual byte[]? InitData() { return null; }

    private List<Component> Components = new();
    public Dictionary<string, Component> Name2Component = new();
}
