using Microsoft.Extensions.Logging;

namespace Phoenix.Server.Common
{
    public class EntityAdmin
    {
        public static readonly EntityAdmin Instance = new EntityAdmin();

        private EntityAdmin()
        {
        }

        public Dictionary<entity_id_t, ServerEntity> Entities = new();
        public Dictionary<userid_t, ServerEntity> BasePlayerEntities = new();

        internal void AddEntity(ServerEntity entity)
        {
            if (Entities.TryGetValue(entity.EntityId, out var oldEntity))
            {
                if (oldEntity == entity)
                {
                    return;
                }
                else
                {
                    DelEntity(oldEntity);
                    Logger.LogError("Entity already exists remove old eid={eid}", entity.EntityId);
                }
            }

            Entities[entity.EntityId] = entity;
            if (entity.UserId > 0)
            {
                BasePlayerEntities[entity.UserId] = entity;
            }

            Logger.LogDebug("AddEntity eid={eid} uid={uid}", entity.EntityId, entity.UserId);
        }

        public void DelEntity(Entity entity)
        {
            Entities.Remove(entity.EntityId);
            if (entity.UserId > 0)
            {
                BasePlayerEntities.Remove(entity.UserId);
            }
        }

        public ServerEntity? GetEntity(long entityId)
        {
            return Entities.GetValueOrDefault(entityId);
        }

/*        public GameBasePlayer? GetBasePlayer(long uid)
        {
            BasePlayerEntities.TryGetValue(uid, out var entity);
            return entity as GameBasePlayer;
        }*/

        internal void on_node_shutdown(HashSet<string> waiting)
        {
            /* '''before process shutdown, make sure all entity persistent done
             :param on_pre_shutdown_done: async cleanup work done callback(usually all entity DB persistent)

         '''*/

/*            foreach (var key in Entities.Keys)
            {
                if(Entities.TryGetValue(key, out var entity))
                {
                    entity.Destroy();
                }
            }
            waiting.Add("EntityAdmin.destroy_entities");
            int randCheckTime = Random.Next(800) + 200;

            TimerManager.Instance.AddEngineNamedTimerInMillisec("EntityAdminShutdownCheck", randCheckTime, check_entities_destroy_done, false, waiting);*/
        }

        private void check_entities_destroy_done(object? obj)
        {
/*            HashSet<string> waiting = obj as HashSet<string>;
            if (Entities.Count == 0)
            {
                Logger.LogInformation("entities_destroy_done");
                waiting.Remove("EntityAdminShutdownCheck");
            }
            else
            {
                Logger.LogInformation("entities_destroy_waiting! entity_count={count}", Entities.Count);
                int randCheckTime = Random.Next(800) + 200;
                TimerManager.Instance.AddEngineNamedTimerInMillisec("EntityAdminShutdownCheck", randCheckTime, check_entities_destroy_done, false, waiting);
            }*/
        }

        private static readonly ILogger Logger = InternalLoggerFactory.LoggerFactory.CreateLogger<EntityAdmin>();
        private readonly Random Random = new Random();
    }

    public enum EObjectTypeTag : int64_t
    {
        // fast type casting ( 1 ~ 16bit)
        ETAG_ENTITY = 1L,
        ETAG_BASE_PLAYER = 1L << 2,
        ETAG_ACTOR = 1L << 3,
        ETAG_SPACE_PLAYER = 1L << 4,
        ETAG_COMPONENT = 1L << 5,

        // Object Ability tag (from 16bit)
        ETAG_MOVABLE = 1L << 15,
    };
}
