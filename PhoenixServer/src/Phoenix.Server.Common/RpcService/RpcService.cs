// Copyright (c) Phoenix.All Rights Reserved.

using MessagePack;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Phoenix.MsgPackLogic.Protocol;
using System.Diagnostics;
using Phoenix.Server.Utils;
using static Phoenix.Server.Common.ConsistentHash;

namespace Phoenix.Server.Common;

public sealed class RpcService : IHostedService
{
    public static RpcService Instance
    {
        get;
        private set;
    } = null!;

    public RpcService(IOptions<ServerOptions> serverOptions,
        IOptions<ClusterOptions> clusterOptions,
        ILogger<RpcService> logger,
        ConsulLeaderElectionService? leaderElectionService = null,
        ConsulLeaderDiscoveryService? leaderDiscoveryService = null)
    {

        Instance = this;
        ServerOptions = serverOptions.Value;
        InitServerNodeManager(serverOptions, clusterOptions);
        s_logger = logger;
        m_leaderElectionService = leaderElectionService;
        m_leaderDiscoveryService = leaderDiscoveryService;
        /*if (m_leaderElectionService != null)
        {
            m_leaderElectionService.OnBecomeLeader += (sender, args) =>
            {
                s_logger.LogInformation("OnBecomeLeader: NodeId={NodeId}", ServerOptions.NodeId);
            };
        }*/

        if (m_leaderDiscoveryService != null)
        {
            m_leaderDiscoveryService.OnLeaderChanged += (sender, nodeInfo) =>
            {
                s_logger.LogInformation("OnLeaderChanged: NodeId={NodeId}", nodeInfo.NodeId);
                AsyncDispatcher.Post(() =>
                {
                    NodeMgr.OnMasterSwitch(nodeInfo);
                });
            };
        }

        // init node manager by role
        // init etcd session
        Stopwatch = Stopwatch.StartNew();
        BootTime = DateTimeOffset.Now.ToUnixTimeMilliseconds() + Stopwatch.ElapsedMilliseconds;
    }

    public void SetHandleServerForwardClientMsgFunc(Action<long, MsgPackStructBase> action)
    {
        NodeMgr.HandleServerForwardClientMsgFunc = action;
    }

    #region implementation of IHostedService

    public Task StartAsync(CancellationToken cancellationToken)
    {
        Start();
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Stop();
    }

    #endregion implementation of IHostedService

    private void Start()
    {
        Stopwatch.Start();
        AsyncDispatcher.Start();
        m_tickTimer = new Timer(_ =>
        {
            AsyncDispatcher.Post(Tick);
        }, null, TimeSpan.Zero, TimeSpan.FromMilliseconds(100));
    }

    private Timer m_tickTimer;

    private async Task Stop()
    {
        Stopwatch.Stop();
        await AsyncDispatcher.Stop();
        IsShutdown = true;
        m_tickTimer.Change(Timeout.Infinite, 0);
    }

    private void Tick()
    {
        try
        {
            UpdateCurrentTime();
            NodeMgr.Tick();
            RpcManager.Instance.Tick();
            MailBoxManager.Instance.Tick();
        }
        catch (Exception e)
        {
            s_logger.LogError(e, "Tick Exception");
        }
    }

    private void InitServerNodeManager(IOptions<ServerOptions> serverOptions,
        IOptions<ClusterOptions> clusterOptions)
    {
        if (serverOptions.Value.Role == RoleName.ROLE_COMMANDER)
        {
            NodeMgr = new CommanderNodeManager(serverOptions, clusterOptions);
        }
        else if (serverOptions.Value.Role == RoleName.ROLE_ROUTER)
        {
            NodeMgr = new RouterNodeManager(serverOptions, clusterOptions);
        }
        else
        {
            NodeMgr = new ServerNodeManager(serverOptions, clusterOptions);
        }

        NodeMgr.Init(AsyncDispatcher);

        InitRpcLogicCallback();

        NodeMgr.RegisterMessageHandler((int)EProtoCode.RPC_FORWARDCLIENTMESSAGETOSERVERNTF,
            (conn, msg) =>
            {
                handleForwardClientMessageToServer(conn.NodeId, msg);
            });

        NodeMgr.RegCallbackOnHashChange(RoleName.ROLE_ROUTER, "checkMailbox", OnWorldHashChange);
    }

    private void OnWorldHashChange(ConsistentHash hash, Dictionary<slot_index_t, nodeid_t> diffMap)
    {
        s_logger.LogDebug("OnWorldHashChange, HashName={HashName}, DiffMapSize={DiffMapSize}", hash.HashName, diffMap.Count);

        if (DelegateOnWorldHashChanged != null)
        {
            DelegateOnWorldHashChanged.Invoke(hash.HashName, hash.CreateConsistentHashImmutable());
        }
        else
        {
            s_logger.LogWarning("OnWorldHashChange DelegateOnWorldHashChanged is invalid HashName={HashName}", hash.HashName);
        }
    }

    private void InitRpcLogicCallback()
    {
        RpcLogicCallback cb = new()
        {
            OnNodeJoin = nodeInfo =>
            {
                s_logger.LogDebug("OnNodeJoin, NodeId={NodeId} Role={Role}", nodeInfo.NodeId, nodeInfo.Role);
                if (DelegateOnNodeJoin != null)
                {
                    DelegateOnNodeJoin(nodeInfo);
                }
                else
                {
                    s_logger.LogWarning("OnNodeJoin DelegateOnNodeJoin is invalid NodeId={NodeId} Role={Role}",
                        nodeInfo.NodeId, nodeInfo.Role);
                }
            },
            OnNodeLeave = nodeInfo =>
            {
                s_logger.LogDebug("OnNodeLeave, NodeId={NodeId} Role={Role}", nodeInfo.NodeId, nodeInfo.Role);
                if (DelegateOnNodeLeave != null)
                {
                    DelegateOnNodeLeave(nodeInfo.NodeId, nodeInfo.Role);
                }
                else
                {
                    s_logger.LogWarning("OnNodeJoin DelegateOnNodeLeave is invalid NodeId={NodeId} Role={Role}",
                        nodeInfo.NodeId, nodeInfo.Role);
                }
            },
            OnRpcRequest = (nodeId, req) =>
            {
                if (req.MsgProtoCode != 0)
                {
                    s_logger.LogTrace(
                        "OnRpcRequest, NodeId={NodeId} ReqId={ReqId} , Uri={Uri} Func={Func} MsgProtoCode={MsgProtoCode} SrcNode={SrcNode} Flag={Flag}",
                        nodeId, req.ReqId, req.Uri, req.Func, (EProtoCode)req.MsgProtoCode, req.SrcNode, req.Flag);
                }
                else
                {
                    s_logger.LogDebug(
                        "OnRpcRequest, NodeId={NodeId} ReqId={ReqId} , Uri={Uri} Func={Func} SrcNode={SrcNode} Flag={Flag}",
                        nodeId, req.ReqId, req.Uri, req.Func, req.SrcNode, req.Flag);
                }

                OnRpcRequest(nodeId, req);
            },
            OnRpcRequestToPlayer = req =>
            {
                s_logger.LogDebug("OnRpcRequest, PlayerId={PlayerId}", req.Uid);
                OnRpcRequestToPlayer(req);
            },
            OnRpcResponse = (nodeId, resp) =>
            {
                if (resp.ErrorCode != (int)RpcErrCode.RPC_NO_ERROR)
                {
                    s_logger.LogError("OnRpcResponse, NodeId={NodeId} ReqId={ReqId} , ErrCode={ErrCode} Flag={Flag} HiddenReqNode={HiddenReqNode}",
                        nodeId, resp.ReqId, resp.ErrorCode, resp.Flag, resp.HiddenReqNode);
                }

                s_logger.LogTrace(
                    "OnRpcResponse, NodeId={NodeId} ReqId={ReqId} , ErrCode={ErrCode} Flag={Flag} HiddenReqNode={HiddenReqNode}",
                    nodeId, resp.ReqId, resp.ErrorCode, resp.Flag, resp.HiddenReqNode);
                OnRpcResponse(nodeId, resp);
            },
            OnRpcReady = () =>
            {
                s_logger.LogDebug("OnRpcReady");
                if (DelegateOnRpcReady != null)
                {
                    DelegateOnRpcReady.Invoke();
                }
                else
                {
                    s_logger.LogWarning("OnRpcReady DelegateOnRpcReady is invalid");
                }
            },
            OnRecvBroadcastData = data =>
            {
                s_logger.LogDebug("OnRecvBroadcastData, Key={Key}", data.Key);
                if (DelegateOnRecvBroadcastData != null)
                {
                    DelegateOnRecvBroadcastData.Invoke(data.Key, data.Data);
                }
                else
                {
                    s_logger.LogWarning(
                        "OnRecvBroadcastData DelegateOnRecvBroadcastData is invalid Key={Key} Data={Data}", data.Key,
                        data.Data);
                }
            }
        };

        NodeMgr.RpcLogicCallback = cb;
    }

    public void RegisterMailbox(string mailboxName, bool isOverride = false, bool isBroadcastRegister = false)
    {
        NodeMgr.RegisterMailBox(mailboxName, RegisterMailBoxCallback, isOverride, isBroadcastRegister);
    }

    private void RegisterMailBoxCallback(string mailboxName, bool isSuccess, nodeid_t router)
    {
        if (DelegateOnRegMailboxResult != null)
        {
            DelegateOnRegMailboxResult.Invoke(mailboxName, isSuccess, router);
        }
        else
        {
            s_logger.LogWarning("OnRegMailbox DelegateOnRegMailboxResult is invalid MailboxName={MailboxName} IsSuccess={IsSuccess} Router={Router}", mailboxName, isSuccess, router);
        }
    }


    // @brief: 	register global service by mailbox, then mailbox can be used to send rpc to certain service
    // 			DelegateOnUnRegMailboxResult will be called in a async way
    public void UnRegisterMailbox(string mailboxName)
    {
        NodeMgr.UnRegisterMailbox(mailboxName, UnRegisterMailboxCallback);
        return;
    }

    private void UnRegisterMailboxCallback(string mailboxName, bool isSuccess, nodeid_t router)
    {
        if (DelegateOnUnRegMailboxResult != null)
        {
            DelegateOnUnRegMailboxResult.Invoke(mailboxName, isSuccess, router);
        }
        else
        {
            s_logger.LogWarning("OnUnregisterMailbox DelegateOnUnRegMailboxResult is invalid MailboxName={MailboxName} IsSuccess={IsSuccess} Router={Router}", mailboxName, isSuccess, router);
        }
    }

    // Send RPC API (MsgPack)

    // @brief: send rpc to direct node, will be handled by OnRpcRequest in target node
    // 			DelegateOnRpcRequest will be called
    // @param ep: target node id
    // @param service_uri: target service uri
    // @param func: target function name
    // @param packedData: rpc data (MsgPack)
    // @param isNeedResp: if true, a rpc response can be received, DelegateOnRpcResponse will be called to handle
    public void RpcCallToDirectNode(nodeid_t nodeId, string serviceUri, string func, byte[] packedData,
        bool needResp, request_id_t reqId)
    {
        NodeMgr.RpcToNode(nodeId, serviceUri, func, packedData, needResp, false, reqId);
    }

    // Send RPC API (MsgPack)

    // @brief: send rpc to direct node, will be handled by OnRpcRequest in target node
    // 			DelegateOnRpcRequest will be called
    // @param ep: target node id
    // @param service_uri: target service uri
    // @param func: target function name
    // @param packedData: rpc data (MsgPack)
    // @param isNeedResp: if true, a rpc response can be received, DelegateOnRpcResponse will be called to handle
    public void RpcCallToDirectNode(nodeid_t nodeId, string serviceUri, string funcName,
        MsgPackStructBase message, bool needResp, request_id_t reqId)
    {
        NodeMgr.RpcToNode(nodeId, serviceUri, funcName, message, needResp, false, reqId);
    }

    // @brief: send rpc to global service by mailbox, will be handled by OnRpcRequest in target node
    // 			DelegateOnRpcRequest will be called
    // @param mailboxName: name of mailbox, service_uri
    // @param func: target service func
    // @param packedData: rpc data (MsgPack)
    // @param isNeedResp: if true, a rpc response can be received, DelegateOnRpcResponse will be called to handle
    public void RpcByMailbox(string mailboxName, string func, byte[] packedData, bool needResp, request_id_t reqId)
    {
        NodeMgr.RpcByMailbox(mailboxName, func, packedData, needResp, reqId);
    }

    public void RpcByMailbox(string mailboxName, MsgPackStructBase message, bool needResp, request_id_t reqId)
    {
        NodeMgr.RpcByMailbox(mailboxName, message, needResp, reqId);
    }

    // @brief: send rpc to specified player, will be handled by OnRpcRequestToPlayer in target node
    // 			DelegateOnRpcRequestToPlayer will be called
    // @param uid: player uid
    // @param is_space_player: if true, player is a space player, otherwise call base player
    // @param func: target player func name
    // @param packedData: rpc data (MsgPack)
    // @param isNeedResp: if true, a rpc response can be received, DelegateOnRpcResponse will be called to handle
    public void RpcToPlayer(userid_t playerId, bool isSpacePlayer, string func, byte[] packedData,
        bool needResp, request_id_t reqId)
    {
        NodeMgr.RpcToPlayer(playerId, isSpacePlayer, func, packedData, null, needResp, false, reqId);
    }

    public void RpcToPlayer<T>(userid_t playerId, T req, bool needResp = true, request_id_t reqId = 0) where T : MsgPackStructBase
    {
        NodeMgr.RpcToPlayer(playerId, false, "", null, req, needResp, false,  reqId);
    }

    // Send RPC API (Nex)
    // @brief: send Nex rpc to direct node, will be handled by OnRpcRequest in target node
    // @param ep: target node id
    // @param service_uri: target service uri
    // @param func: target function name
    // @param packedData: rpc data (Nex)
    // @param isNeedResp: if true, a rpc response can be received, DelegateOnRpcResponse will be called to handle
    void SendNexToDirectNode(nodeid_t nodeId, MsgPackStructBase message, bool isNeedResp, request_id_t reqId)
    {
        NodeMgr.RpcToNode(nodeId, "", "", message, isNeedResp, false, reqId);
    }

    // @brief: send Nex rpc to global service by mailbox, will be handled by OnRpcRequest in target node
    // @param mailboxName: name of mailbox, service_uri
    // @param func: target service func
    // @param packedData: rpc data (Nex)
    // @param isNeedResp: if true, a rpc response can be received, DelegateOnRpcResponse will be called to handle
    void SendNexByMailbox(string mailboxName, byte[] packedData, bool isNeedResp, request_id_t reqId)
    {
        NodeMgr.RpcByMailbox(mailboxName, "", packedData, isNeedResp, reqId);
    }

    // @brief: send rpc to specified player, will be handled by OnRpcRequestToPlayer in target node
    // @param uid: player uid
    // @param is_space_player: if true, player is a space player, otherwise call base player
    // @param func: target player func name
    // @param packedData: rpc data (Nex)
    // @param isNeedResp: if true, a rpc response can be received, DelegateOnRpcResponse will be called to handle
    /*SendNexToPlayer(userid_t uid, bool isSpacePlayer, byte[] packedData, bool isNeedResp)
    {
        return NodeMgr.RpcToPlayer(uid, isSpacePlayer, "", packedData, null, isNeedResp, true);
    }*/

    void BroadcastRpc(string serviceUri, string func, byte[] packedData)
    {
        AsyncDispatcher.Post(() =>
        {
            int dotPos = serviceUri.IndexOf('.');
            if (dotPos != -1)
            {
                var role = serviceUri.Substring(0, dotPos);
                var roleNodes = NodeMgr.GetActiveNodesByRole(role);
                foreach (var node in roleNodes)
                {
                    NodeMgr.RpcToNode(node, serviceUri, func, packedData, false, true);
                }
            }
            else
            {
                var allNodes = NodeMgr.GetAllActiveNodes();
                foreach (var node in allNodes)
                {
                    NodeMgr.RpcToNode(node, serviceUri, func, packedData, false, true);
                }
            }
        });
    }

    void OnRpcRequest(nodeid_t node, MsgPack_S_Rpc_Req rpcMsg)
    {
        request_id_t reqId = rpcMsg.ReqId;
        nodeid_t nodeId = rpcMsg.SrcNode;
        MsgPackStructBase? protoMessage = null;
        if ((rpcMsg.Flag & RpcFlag.RPC_INTERNAL) != 0 || rpcMsg.MsgProtoCode != 0)
        {
            protoMessage = RpcMsgParamParser.ParseMsgpackMessage(rpcMsg.MsgProtoCode, rpcMsg.Data);
            if (HandleInternalRpc(nodeId, protoMessage!))
            {
                return;
            }
        }

        //msgpack
        RpcRequestInfo reqInfo = new()
        {
            ReqId = reqId,
            NodeId = nodeId,
            HiddenNodeId = rpcMsg.HiddenSrcNode,
            NeedResponse = (rpcMsg.Flag & RpcFlag.RPC_NEED_RESP) != 0,
            IsMailboxRequest = (rpcMsg.Flag & RpcFlag.FLAG_RPC_BY_MAILBOX) != 0
        };
        var func = rpcMsg.Func;
        string[] svcPart;
        string svcName;

        if (reqInfo.IsMailboxRequest)
        {
            // uri formation: role.LS_NAME
            svcPart = rpcMsg.Uri.Split('.');
            if (svcPart.Length == 2)
            {
                svcName = svcPart[1];
            }
            else
            {
                // not specify role
                svcName = rpcMsg.Uri;
            }
        }
        else
        {
            // specify mailbox_name as uri
            svcName = rpcMsg.Uri;
        }

        if (protoMessage != null)
        {
            if (DelegateOnRpcMessageRequest != null)
            {
                DelegateOnRpcMessageRequest(svcName, func, protoMessage, reqInfo);
            }
            else
            {
#if DEBUG
                s_logger.LogWarning(
                    "OnRpcRequest DelegateOnRpcMessageRequest is invalid svc_name={svc_name} ProtoCode={ProtoCode}",
                    svcName, protoMessage.ProtoCode);
#endif
            }

            return;
        }

        object[]? paramList = RpcMsgParamParser.ParseParamList(rpcMsg.Data);
        /*var paramStr = new StringBuilder();
        paramStr.Append("[");
        foreach (var param in paramList!)
        {
            paramStr.Append(param.ToString());
            paramStr.Append(", ");
        }

        paramStr.Append("]");

        s_logger.LogTrace("OnRpcRequest svc_name={svc_name} func={func} params={paramList}", svcName, func,
            paramStr.ToString());
            */

        if (DelegateOnRpcRequest != null)
        {
            DelegateOnRpcRequest(svcName, func, paramList!, reqInfo);
        }
        else
        {
#if DEBUG
            s_logger.LogWarning("OnRpcRequest DelegateOnRpcRequest is invalid svc_name={svc_name} func={func}", svcName,
                func);
#endif
        }
    }

    void OnRpcResponse(nodeid_t nodeId, MsgPack_S_Rpc_Ack resp)
    {
        nodeid_t respNodeId = resp.ReqNode;
        request_id_t reqId = resp.ReqId;
        if (DelegateOnRpcResponse != null)
        {
            DelegateOnRpcResponse.Invoke(respNodeId, reqId, resp.ErrorCode, resp.MsgProtoCode, resp.RespData);
        }
        else
        {
#if DEBUG
            s_logger.LogWarning(
                "OnRpcResponse DelegateOnRpcResponse is invalid resp_node_id={resp_node_id} ReqId={ReqId}  rcode={rcode}",
                respNodeId, reqId, resp.ErrorCode);
#endif
        }
    }

    public void SendRpcResponse(RpcRequestInfo reqInfo, MsgPackStructBase message, int retCode)
    {
//        AsyncDispatcher.Post(() => NodeMgr.SendRpcResponse(reqInfo, message, retCode));
        AsyncDispatcher.Post(NodeMgr.SendRpcResponse, reqInfo, message, retCode);
    }

    public void SendRpcResponse(RpcRequestInfo reqInfo, byte[] packedData, RpcErrCode errCode)
    {
        AsyncDispatcher.Post(NodeMgr.SendRpcResponse, reqInfo, packedData, errCode);
        /*AsyncDispatcher.Post(() =>
            {
                NodeMgr.SendRpcResponse(reqInfo, packedData, errCode);
            }
        );*/
    }

    void OnRpcRequestToPlayer(MsgPack_S_RpcToPlayer_Proto req)
    {
        MsgPack_S_Rpc_Req rpcReq = req.RpcReq;
        request_id_t reqId = rpcReq.ReqId;
        nodeid_t nodeId = rpcReq.SrcNode;
        nodeid_t hiddenNodeId = rpcReq.HiddenSrcNode;

        //internal NEX proto
        if ((rpcReq.Flag & RpcFlag.RPC_INTERNAL) != 0 || rpcReq.ProtoCode != 0)
        {
            if (HandleInternalRpc(nodeId, rpcReq))
            {
                return;
            }
            // rpc stat msg
            //RPC_STAT_MGR.Inc(slice.cmd_index, req_info.data().size());
        }

        var reqHeader = new RpcRequestInfo
        {
            NodeId = nodeId,
            HiddenNodeId = hiddenNodeId,
            ReqId = reqId,
            NeedResponse = (rpcReq.Flag & RpcFlag.RPC_NEED_RESP) != 0
        };

        MsgPackStructBase? message = null;
        object[]? paramList = null;
        if (rpcReq.MsgProtoCode != 0)
        {
            message = RpcMsgParamParser.ParseMsgpackMessage(rpcReq.MsgProtoCode, rpcReq.Data);
        }
        else
        {
            paramList = RpcMsgParamParser.ParseParamList(rpcReq.Data);
        }

        if (DelegateOnRpcRequestToPlayer != null)
        {
            DelegateOnRpcRequestToPlayer.Invoke(req.Uid, req.IsSpacePlayer, rpcReq.Func, paramList, message, reqHeader);
        }
        else
        {
            s_logger.LogWarning(
                "OnRpcRequestToPlayer DelegateOnRpcRequestToPlayer is invalid PlayerId={PlayerId} IsSpacePlayer={IsSpacePlayer} Func={Func}",
                req.Uid, req.IsSpacePlayer, rpcReq.Func);
        }
    }

    // player_rpc
    public void RegisterPlayerRouterFunc( /*const player_router_fn&*/ Func<userid_t, nodeid_t> func,
        bool isSpacePlayer)
    {
        NodeMgr.RegisterPlayerRouterFunc(func, isSpacePlayer);
    }

    void SetPlayerMsgPending(userid_t playerId, bool isSpacePlayer)
    {
        NodeMgr.SetPlayerMsgPending(playerId, isSpacePlayer);
    }

    void FlushPlayerMsg(userid_t id, bool is_space_player)
    {
        NodeMgr.FlushPlayerMsg(id, is_space_player);
    }

    bool HandleInternalRpc(nodeid_t nodeId, MsgPackStructBase msg)
    {
        if (m_internalProtoHandles.ContainsKey((uint)msg.ProtoCode))
        {
            m_internalProtoHandles[(uint)msg.ProtoCode](nodeId, msg);
            return true;
        }

        return false;
    }

    public void RegisterInternalProtoHandler(uint protoId, Action<nodeid_t, MsgPackStructBase> handle)
    {
        m_internalProtoHandles[protoId] = handle;
    }

    /*    public void SendMessageToClient(userid_t playerId, nodeid_t gateNodeId, byte[] msgData, bool isSpaceChannel = false)
        {
            MsgPack_S_ForwardClientMsg_Proto forwardMsg = new() { Uid = playerId, SrcHost = ServerOptions.NodeId, Data = msgData };
            if (isSpaceChannel)
            {
                forwardMsg.Flag = (0x01);
            }

            NodeMgr.RpcToNode(gateNodeId, string.Empty, forwardMsg, false, true, false);
        }*/

    public void SendMessageToClient(userid_t playerId, nodeid_t gateNodeId, MsgPackStructBase message,
        bool isSpaceChannel = false)
    {
        ForwardMessageToClientNtf forwardMessageToMsg = new()
        {
            PlayerId = playerId,
            SrcHost = ServerOptions.NodeId,
            MsgProtoCode = (int)message.ProtoCode,
            Data = MsgPackProtoHelper.Serialize(message)!
        };

        if (isSpaceChannel)
        {
            forwardMessageToMsg.Flag = (0x01);
        }

        NodeMgr.RpcToNode(gateNodeId, string.Empty, forwardMessageToMsg, false, true, false, RpcFlag.RPC_FOWRARD_TO_CLIENT);
    }

    public void SendMessageToNode(uint nodeId, MsgPackStructBase message)
    {
        NodeMgr.SendNode(nodeId, message);
    }

    void RegClientMsgHandler(uint cmd_index, Action<userid_t, MsgPackStructBase> handler)
    {
        m_clientMsgHandles.Add(cmd_index, handler);
    }

    public void handleForwardClientMessageToServer(nodeid_t nodeId, MsgPackStructBase message)
    {
        ForwardClientMessageToServerNtf? forwardClientMsg = message as ForwardClientMessageToServerNtf;

        if (forwardClientMsg == null)
        {
            throw new Exception($"message convert failed {message}");
        }

        userid_t playerId = forwardClientMsg.PlayerId;
        var clientMsg = MsgPackProtoHelper.Deserialize(forwardClientMsg.MsgProtoCode, forwardClientMsg.Data);

        // stat rpc msg
        //RPC_STAT_MGR.StatRpcMsg(payload);

        if (clientMsg!.ProtoCode == EProtoCode.SYS_C_CALLBASEPLAYERMETHOD_PROTO)
        {
            if (DelegateOnBasePlayerCall != null)
            {
                var callMsg = clientMsg as MsgPack_C_CallBasePlayerMethod_Proto;
                DelegateOnBasePlayerCall.Invoke(playerId, callMsg!);
            }
            else
            {
                s_logger.LogWarning("handle_C_FORWARD_CLIENT_MSG DelegateOnBasePlayerCall is invalid PlayerId={PlayerId}", playerId);
            }

            return;
        }

        // handle by engine
        if (HandleClientMsg(playerId, clientMsg))
        {
            return;
        }

        // handle by script
        if (DelegateOnForwardClientMsg != null)
        {
            DelegateOnForwardClientMsg.Invoke(playerId, clientMsg);
        }
        else
        {
            s_logger.LogWarning(
                "handle_C_FORWARD_CLIENT_MSG DelegateOnForwardClientMsg is invalid PlayerId={PlayerId} ClientMsg={ClientMsg}",
                playerId, clientMsg);
        }
    }

    bool HandleClientMsg(userid_t uid, MsgPackStructBase message)
    {
        if (m_clientMsgHandles.TryGetValue((uint)message.ProtoCode, out var handler))
        {
            handler(uid, message);
            return true;
        }

        // Logger.LogError("HandleClientMsg handler is invalid uid={} cmd={}", uid, message.GetCmdIndex());

        return false;
    }

    private void UpdateCurrentTime()
    {
        CurrentMilliseconds = BootTime + Stopwatch.ElapsedMilliseconds;
    }

    // Select node according to hashkey
    public nodeid_t? SelectNode(RpcTarget target, dynamic hashkey)
    {
        RpcNodeSelectStrategy selector = target.Selector;
        string role = target.ServiceRole;

        if (selector == RpcNodeSelectStrategy.DEDICATED_NODE)
        {
            return target.NodeId;
        }

        if (selector == RpcNodeSelectStrategy.FIXED_HASH)
        {
            int hash = (int)MurmurHash.Hash(hashkey);
            var nodeList = GetActiveNodesByRole(role);
            if (!nodeList.Any())
            {
                s_logger.LogError("[RPC] select_node_error, Uri={Uri}, Selector={Selector}, err=empty_nodes", target.Uri,
                    selector);
                return null;
            }

            return nodeList[(hash % nodeList.Count)];
        }

        if (selector == RpcNodeSelectStrategy.CONSISTENT_HASH)
        {
            nodeid_t nodeId = PickNode(role, hashkey);
            if (nodeId == 0)
            {
                s_logger.LogError($"[RPC] select_node_error selector consistent_hash HashKey={hashkey}");
                return null;
            }

            return nodeId;
        }

        if (selector == RpcNodeSelectStrategy.DYNAMIC_MAILBOX)
        {
            s_logger.LogError("[RPC] select_node_error, Uri={Uri}, Selector={Selector}, err=invalid_selector", target.Uri,
                selector);
            return null;
        }

        {
            // RANDOM_NODE selector
            var nodeList = GetActiveNodesByRole(role);
            if (!nodeList.Any())
            {
                s_logger.LogError("[RPC] select_node_error, Uri={Uri}, Selector={Selector}, err=empty_nodes", target.Uri,
                    selector);
                return null;
            }

            return nodeList[Random.Shared.Next(nodeList.Count)];
        }
    }

    private List<nodeid_t> GetActiveNodesByRole(string role)
    {
        // TODO fix thread safe
        return NodeMgr.GetActiveNodesByRole(role);
    }

    public nodeid_t PickNode(string hashName, dynamic key)
    {
        var consistentHash = NodeMgr.GetConsistentHashByRole(hashName);
        if (consistentHash == null)
        {
            s_logger.LogWarning("pick_node fail consistent hash is not setup for this hashName, HashName={HashName}",
                hashName);
            return 0;
        }

        if (key is int64_t or string)
        {
            return consistentHash.GetConsistentHashImmutable().PickNode(key);
        }

        s_logger.LogWarning($"pick_node fail consistent_hash invalid key type={key.GetType()}");
        return 0;
    }

    public static bool UseConsul()
    {
        return Environment.GetEnvironmentVariable("PHOENIX_DEV", EnvironmentVariableTarget.Machine) == null;
    }

    private static ILogger s_logger = null!;

    private long BootTime; // seconds passed since boottime
    private Stopwatch Stopwatch;

    private readonly Dictionary<uint, Action<nodeid_t, MsgPackStructBase>> m_internalProtoHandles = new();
    private readonly Dictionary<uint, Action<userid_t, MsgPackStructBase>> m_clientMsgHandles = new();

    public event Action<userid_t, bool, string, object[]?, MsgPackStructBase?, RpcRequestInfo>?
        DelegateOnRpcRequestToPlayer;

    public event Action<string, bool, nodeid_t>? DelegateOnRegMailboxResult;
    public event Action<string, bool, nodeid_t>? DelegateOnUnRegMailboxResult;
    public event Action<userid_t, MsgPack_C_CallBasePlayerMethod_Proto>? DelegateOnBasePlayerCall;

    public event Action<userid_t, entity_id_t, string, byte[]>? DelegateOnEntityCall;

    // public event Action<userid_t, C_ANY_ACTION>? DelegateOnAnyActionCall;
    public event Action<userid_t, MsgPackStructBase>? DelegateOnForwardClientMsg;
    public event Action<nodeid_t, request_id_t, int32_t, int, byte[]>? DelegateOnRpcResponse;
    public event Action<string, string, object[], RpcRequestInfo>? DelegateOnRpcRequest;
    public event Action<string, string, MsgPackStructBase, RpcRequestInfo>? DelegateOnRpcMessageRequest;
    public event Action? DelegateOnRpcReady;
    public event Action<string, byte[]>? DelegateOnRecvBroadcastData;

    public event Action<MsgPack_S_ServerNodeInfo_Proto>? DelegateOnNodeJoin;
    public event Action<nodeid_t, string>? DelegateOnNodeLeave;

    // @brief: delegate called on world router hash table changed
    // @param hash: hash name
    public event Action<string, ConsistentHashImmutable>? DelegateOnWorldHashChanged;

    private bool IsShutdown;
    private ServerNodeManager NodeMgr = null!;
    private readonly ConsulLeaderElectionService? m_leaderElectionService;
    private readonly ConsulLeaderDiscoveryService? m_leaderDiscoveryService;

    public ServerNode MyNode
    {
        get
        {
            return NodeMgr.MyNode;
        }
    }

    private ServerOptions ServerOptions
    {
        get;
        set;
    } = null!;

    public long CurrentMilliseconds
    {
        get;
        private set;
    }

    public AsyncDispatcher AsyncDispatcher
    {
        get;
        private set;
    } = new AsyncDispatcher();

    public Task<uint> GetRandomNodeByRole(string roleGame)
    {
        return NodeMgr.GetRandomNodeByRole(roleGame);
    }
}

public sealed class RpcMsgParamParser
{
    public static object[]? ParseParamList(byte[] data)
    {
        object[]? paramList = null;
        try
        {
            MessagePackReader reader = new MessagePackReader(data);
            paramList = MessagePack.MessagePackSerializer.Deserialize<object[]>(data);
            return paramList;
        }
        catch (Exception e)
        {
            s_logger.LogError(e.ToString());
            return null;
        }
    }

    public static MsgPackStructBase? ParseMsgpackMessage(int protoCode, byte[] data)
    {
        return MsgPackProtoHelper.Deserialize(protoCode, data);
    }

    private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<RpcMsgParamParser>();
}
