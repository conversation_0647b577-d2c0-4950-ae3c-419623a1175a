using Microsoft.Extensions.Logging;
using System.Reflection;

namespace Phoenix.Server.Common
{
    public class RpcModuleMethodFinder
    {
        public static readonly RpcModuleMethodFinder Instance = new RpcModuleMethodFinder();

        public void RegModule(object obj)
        {
            Type type = obj.GetType();
            var attri =type.GetCustomAttribute<RpcModuleAttribute>();
            if (attri != null)
            {
                string moduleName = attri.Name;
                if (string.IsNullOrEmpty(moduleName))
                {
                    throw new Exception($"moduleName is empty, type={type}");
                }
                RegModule(moduleName, obj);

                foreach( var methodInfo in type.GetMethods(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static))
                {
                    var methodAttri = methodInfo.GetCustomAttribute<RpcMethodAttribute>();
                    if (methodAttri != null)
                    {
                        string methodName = string.IsNullOrEmpty(methodAttri.Name) ? methodInfo.Name : methodAttri.Name;
                        RegModuleMethod(moduleName, obj, methodName, methodInfo);
                    }
                }
            }
        }

        private void RegModule(string name, object instance)
        {
            if (m_name2Module.ContainsKey(name))
            {
                return;
            }

            m_name2Module[name] = new ModuleInfo(instance);
        }

        private void RegModuleMethod(string moduleName, object module, string methodName, MethodInfo methodInfo)
        {
            RegModule(moduleName, module);
            m_name2Module[moduleName].RegMethod(methodName, methodInfo);
        }

        internal Task ExecuteModuleMethod(string svcName, string funcName, params object[] paramerter)
        {
            if (!m_name2Module.ContainsKey(svcName))
            {
                s_logger.LogError("module not found, moduleName={moduleName}", svcName);
                return Task.CompletedTask;
            }

            m_name2Module.TryGetValue(svcName, out var module);
            if (module == null)
            {
                s_logger.LogError("module not found, moduleName={moduleName}, funcName={funcName}", svcName, funcName);
                return Task.CompletedTask;
            }

            var methodInfo = module[funcName];
            if (methodInfo != null)
            {
                try
                {
                    Task task = (Task)methodInfo.Invoke(module.objInstance, paramerter);
                    return task!;
                }
                catch (Exception e)
                {
                    s_logger.LogError("ExecuteModuleMethod fail Module={Module} FuncName={FuncName} Exception={Exception}", module, funcName, e.ToString());
                    return Task.CompletedTask;
                }
            }
            return Task.CompletedTask;
        }

        private readonly Dictionary<string, ModuleInfo> m_name2Module = new Dictionary<string, ModuleInfo>();

        private class ModuleInfo
        {

            public ModuleInfo(object module)
            {
                objInstance = module;
            }

            public void RegMethod(string name, MethodInfo method)
            {

                Name2Method[name] = method;
            }

            public MethodInfo? this[string name]
            {
                get
                {
                    return Name2Method!.GetValueOrDefault(name, null);
                }
            }

            public object objInstance { get; private set; }

            private readonly Dictionary<string, MethodInfo> Name2Method = new();
        }

        public class TypeModuleInfo
        {
            public readonly Dictionary<string, MethodInfo> Name2Method = new();
        }

        public static TypeModuleInfo? GetTypeModuleInfo(Type type)
        {
            s_type2ModuleInfo.TryGetValue(type, out var module);
            return module;
        }

        private static readonly Dictionary<Type, TypeModuleInfo> s_type2ModuleInfo = new();

        private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<RpcModuleMethodFinder>();
    }

    [AttributeUsage(AttributeTargets.Method)]
    public class RpcMethodAttribute : Attribute
    {
        public string Role { get; set; } = "";

        public string Name { get; set; } = "";
    }

    [AttributeUsage(AttributeTargets.Class)]
    public class RpcModuleAttribute : Attribute
    {
        public string Name { get; set; } = "";
    }
}
