using DotNetty.Transport.Channels;
using Microsoft.Extensions.Logging;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Server.Common
{
    public class NodeServerHandler : ChannelHandlerAdapter
    {
        public NodeServerHandler(ConnectionManager connectionManager)
        {
            m_connManager = connectionManager;
        }

        public override void ChannelActive(IChannelHandlerContext context)
        {
            m_conn = new(context.Channel);
            m_connManager.RegConnection(m_conn, 0);
            m_conn.OnAccepted();
        }

        public override void ChannelInactive(IChannelHandlerContext context)
        {
            s_logger.LogInformation("NodeServerHandler.ChannelInactive ConnId={ConnId}", m_conn.ConnId);
            m_conn.OnDisconnect();
        }

        public override void ChannelRead(IChannelHandlerContext context, object message)
        {
            m_conn?.OnRead((MsgPackStructBase)message);
        }

        public override void ChannelReadComplete(IChannelHandlerContext context) => context.Flush();

        public override void ExceptionCaught(IChannelHandlerContext context, Exception exception)
        {
            s_logger.LogInformation("ExceptionCaught Exception={Exception}", exception.Message);
            context.CloseAsync();
            m_conn?.OnException(exception);
        }

        private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<NodeServerHandler>();
        private readonly ConnectionManager m_connManager;
        private NodeTcpConnection? m_conn;
    }
}
