using Microsoft.Extensions.Logging;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Server.Common;

/// <summary>
/// ServerNode status and information
/// </summary>
public class ServerNode
{
    public ServerNode(nodeid_t nodeId, string role, connid_t connId)
    {
        NodeInfo = new MsgPack_S_ServerNodeInfo_Proto { NodeId = nodeId, Role = role, IsReady = false };
        ConnId = connId;
        if (ConnId != 0)
        {
            State = ENodeConnState.CONNECTED;
        }
    }

    public ServerNode(MsgPack_S_ServerNodeInfo_Proto nodeInfo, connid_t connId = 0)
    {
        NodeInfo = nodeInfo;
        ConnId = connId;
        UpdateState();
    }

    private void UpdateState()
    {
        if (State == ENodeConnState.INVALID)
        {
            if (NodeInfo.Ip.Length > 0)
            {
                State = ENodeConnState.DISCONNECT;
            }

            if (ConnId > 0)
            {
                State = ENodeConnState.CONNECTED;
            }
        }
    }

    public bool IsActive()
    {
        return State == ENodeConnState.ACTIVE;
    }

    public bool IsConnected()
    {
        return (IsActive() || State == ENodeConnState.CONNECTED) && (ConnId > 0);
    }

    public bool IsConnecting()
    {
        return State == ENodeConnState.CONNECTING;
    }

    internal void UpdateNodeInfo(MsgPack_S_ServerNodeInfo_Proto nodeInfo)
    {
        this.NodeInfo = nodeInfo;
    }

    public void SetNetAddr(string ip, ushort port)
    {
        NodeInfo.Ip = ip;
        NodeInfo.Port = port;
        UpdateState();
    }

    public void SetExtraData(byte[] extraData)
    {
        NodeInfo.ExtraData = extraData;
    }

    public IEnumerable<MsgPackStructBase> GetSendCache()
    {
        return SendCache;
    }

    public bool AppendToSendCache(MsgPackStructBase msg)
    {
        //  TODO cache size control
        SendCache.Add(msg);
        return true;
    }

    public bool IsSendCacheEmpty()
    {
        return SendCache.Any();
    }

    public void ClearSendCache()
    {
        SendCache.Clear();
    }

    public void SetRouterId(nodeid_t routerId)
    {
        m_routerId = routerId;
    }

    public nodeid_t GetRouterId()
    {
        return m_routerId;
    }

    public void SetLastHeartbeatTime(int64_t time)
    {
        m_lastHeartbeatTime = time;
    }

    public int64_t GetLastHeartbeatTime()
    {
        return m_lastHeartbeatTime == 0 ? RpcService.Instance.CurrentMilliseconds : m_lastHeartbeatTime;
    }

    public void SetNodeTimeout(int64_t timeVal)
    {
        kHeartbeatTimeout = timeVal;
    }

    public bool AddressEqual(ServerNode? other)
    {
        if (other == null)
        {
            return false;
        }

        return NodeInfo.Ip == other.NodeInfo.Ip && NodeInfo.Port == other.NodeInfo.Port;
    }

    /*public void BeginSendHeartBeat()
    {
        // Logger.LogInformation("begin send heartbeat, connId={ConnId}, nodeId={NodeId}", ConnId, NodeInfo.NodeId);

        RpcService.Instance.AsyncDispatcher.Post(async () =>
        {

        });
    }*/

    public void StopSendHeartBeat()
    {
        IsKeepAlive = false;
    }

    public bool IsKeepAlive = true;

    public void SetLogicReady(bool isReady)
    {
        IsReady = isReady;
    }

    public bool IsLogicReady()
    {
        return IsReady;
    }

    internal long GetLastConnectTime()
    {
        return m_lastConnectTime;
    }

    internal void SetLastConnectTime(long currentMilliseconds)
    {
        m_lastConnectTime = currentMilliseconds;
    }

    public void SetConnectBeginTime(long currentMilliseconds)
    {
        m_connectBeginTime = currentMilliseconds;
    }

    public long GetConnectBeginTime()
    {
        return m_connectBeginTime;
    }

    internal ENodeConnState SetState(ENodeConnState newState)
    {
        var oldState = State;
        State = newState;
        return oldState;
    }

    private static readonly ILogger Logger = InternalLoggerFactory.LoggerFactory.CreateLogger<ServerNode>();

    public connid_t ConnId { get; set; }
    public ENodeConnState State { get; private set; } = ENodeConnState.INVALID;
    private int64_t m_connectBeginTime = 0;
    private int64_t m_lastConnectTime = 0;
    private readonly List<MsgPackStructBase> SendCache = new(); //pending message queue

    // private size_t m_pendingMsgSize = 0;

    // total pending message size
    // if routerNodeID not empty, send rpc message route by router node
    // message route:  source -> router -> destination
    private nodeid_t m_routerId;

    // last heartbeat time
    private int64_t m_lastHeartbeatTime;

    // is ready to receive rpc request in script
    private bool IsReady;

    /// <summary>
    /// 心跳间隔(毫秒)
    /// </summary>
    public static long kHeartbeatInterval;

    /// <summary>
    /// 心跳超时(毫秒)
    /// </summary>
    public static long kHeartbeatTimeout;

    public MsgPack_S_ServerNodeInfo_Proto NodeInfo { get; private set; }
}
