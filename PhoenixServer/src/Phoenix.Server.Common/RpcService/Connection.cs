using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Server.Common
{
    public class Connection
    {
        public virtual void Disconnect(ConnDisconnectReason reason)
        {

        }

        public void SetConnId(connid_t connId)
        {
            this.ConnId = connId;
        }

        public connid_t GetConnId()
        {
            return ConnId;
        }

        internal virtual void WriteData(MsgPackStructBase message)
        {
            throw new NotImplementedException();
        }

        public ConnType ConnType { get; protected set; }

        public connid_t ConnId;
    }

    public enum ConnType
    {
        TCP,
        KCP // dot implementation
    }

    public enum ConnDisconnectReason
    {
        NodeLeave,

        HeartbeatRespDotFindNode,
        HeartbeatDotFindNode,
        RpcConnectRejected,
        RecvRpcEstablishNodeIdNotMatch,
        DoCloseConnection
    }
}
