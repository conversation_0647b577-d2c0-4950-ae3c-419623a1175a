using Microsoft.Extensions.Logging;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Server.Common
{
    internal sealed class MailBoxManager
    {
        public static MailBoxManager Instance
        {
            get;
            private set;
        } = null!;

        public MailBoxManager(ServerNodeManager owner)
        {
            Instance = this;
            Owner = owner;
        }

        public void RegisterMailbox(string mailboxName, DelegateMailboxRegisterCallback? callback = null,
                      bool isOverride = false, bool broadcastRegister = false)
        {
            m_retryRegister.Remove(mailboxName);

            List<nodeid_t> routers = new();
            if (broadcastRegister)
            {
                // include all router
                routers = Owner.GetActiveNodesByRole(RoleName.ROLE_ROUTER);
            }
            else
            {
                // pick a router by key
                uint pickRouter = Owner.PickRouter(mailboxName);
                if (pickRouter != 0)
                {
                    routers.Add(pickRouter);
                }
            }
            if (routers.Count == 0)
            {
                m_retryRegister.Add(mailboxName, new RegisterMailboxArgs(callback, isOverride, broadcastRegister));
                return;
            }

            s_logger.LogInformation("Begin_register_mailbox, MailboxName={MailboxName}, will be registered in world Routers={Routers}", mailboxName, routers.ToString());

            nodeid_t router = routers.Count > 1 ? MailBoxManager.AllRouter : routers[0];
            if (m_mailboxRecords.TryGetValue(mailboxName, out var oldRecord) && !isOverride)
            {
                s_logger.LogWarning("Repeat_registration, MailboxName={MailboxName}, Router={Router}", mailboxName, oldRecord.Router);
                if (callback != null) callback(mailboxName, false, oldRecord.Router);
                return;
            }

            if (isOverride)
            {
                m_mailboxRecords[mailboxName] = new MailboxRecord(router, broadcastRegister);
            }

            MsgPack_S_RegisterMailBox_Req registerMailboxMsg = new()
            {
                MailboxName = mailboxName,
                IsOverride = isOverride,
                NodeId = Owner.GetMyNode()!.NodeInfo.NodeId,
                IsBroadcastRegister = broadcastRegister
            };

            foreach (nodeid_t targetRouter in routers)
            {
                Owner.SendNode_CR(targetRouter, registerMailboxMsg);
            }

            HashSet<nodeid_t> waitingWorldRouters = new();
            routers.ForEach(targetRouter => waitingWorldRouters.Add(targetRouter));

            var callbackContext = new MailboxRegisterCallbackContext(callback, waitingWorldRouters);
            m_mailboxRegisterContexts.Add(mailboxName, (callbackContext));
            if (broadcastRegister)
            {
                // register callback on add world router
                Owner.RegisterCallbackOnRoleJoin(RoleName.ROLE_ROUTER, mailboxName, (nodeInfo) =>
                {
                    s_logger.LogInformation("register_mailbox_to_new_world_router, MailboxName={MailboxName}, worldNodeId={WorldNodeId}", mailboxName, nodeInfo.NodeId);
                    Owner.SendNode_CR(nodeInfo.NodeId, registerMailboxMsg);
                });

            }
        }

        public void UnRegisterMailbox(string mailboxName, DelegateMailboxUnregisterCallback? callbacks = null)
        {
            m_retryRegister.Remove(mailboxName);

            if (!m_mailboxRecords.TryGetValue(mailboxName, out var recordRouter))
            {
                s_logger.LogWarning("unregister_mailbox_failed, mailbox has not been registered, MailboxName={MailboxName}", mailboxName);
                return;
            }

            List<nodeid_t> routers = new();
            if (recordRouter.BroadcastRegister)
            {
                routers = Owner.GetActiveNodesByRole(RoleName.ROLE_ROUTER);
                // remove callback on add world router
                Owner.UnRegisterCallbackOnRoleJoin(RoleName.ROLE_ROUTER, mailboxName);
            }
            else
            {
                routers.Add(recordRouter.Router);
            }
            m_mailboxRecords.Remove(mailboxName);
            // delete mailbox in router
            MsgPack_S_UnregisterMailBox_Req unregister = new()
            {
                MailboxName = mailboxName
            };
            foreach (nodeid_t router in routers)
            {
                Owner.SendNode_CR(router, unregister);
            }
            s_logger.LogInformation("unregister_mailbox, MailboxName={MailboxName}, Routers={Routers}", mailboxName, routers.ToString());
            if (callbacks != null)
            {
                m_mailboxRegisterCallbacks.Add(mailboxName, callbacks);
            }
        }

        internal void HandleRegisterResp(MsgPack_S_RegisterMailBox_Ack resp)
        {
            string mailbox = resp.MailboxName;
            if (resp.ErrorCode != 0)
            {
                s_logger.LogWarning("register mailbox failed, ErrorMsg={ErrorMsg}", resp.ErrorCode);
                m_mailboxRecords.Remove(mailbox);
            }

            if (!m_mailboxRegisterContexts.ContainsKey(mailbox))
            {
                s_logger.LogWarning("register mailbox context not found, MailboxName={MailboxName}", mailbox);
                return;
            }

            if (m_mailboxRegisterContexts.TryGetValue(mailbox, out var context))
            {
                context.OnRecv(mailbox, resp.ErrorCode == 0, resp.RouterNodeId);
                m_mailboxRegisterContexts.Remove(mailbox);
            }
            s_logger.LogInformation("register mailbox success, MailboxName={MailboxName}, Router={Router}", mailbox, resp.RouterNodeId);
        }

        internal void HandleUnRegisterRes(MsgPack_S_UnregisterMailBox_Ack resp, nodeid_t router)
        {
            string mailbox = resp.MailboxName;
            if (m_mailboxRegisterCallbacks.TryGetValue(mailbox, out var callback))
            {
                callback(mailbox, resp.ErrorCode == 0, router);
            }
            s_logger.LogInformation("unregister mailbox success, MailboxName={MailboxName}, Router={Router}", mailbox, router);
        }

        internal void Tick()
        {
            long current = RpcService.Instance.CurrentMilliseconds;
            // check register mailbox timeout
            foreach (var pair in m_mailboxRegisterContexts)
            {
                string mailboxName = pair.Key;
                var context = pair.Value;
                if (current > context.ExpiredAt)
                {
                    context.OnExpired(mailboxName, Owner.GetActiveNodesByRole(RoleName.ROLE_ROUTER).Count);
                    m_expiredMailboxNames.Add(mailboxName);
                }
            }

            m_expiredMailboxNames.ForEach(mailboxName =>
            {
                m_mailboxRegisterContexts.Remove(mailboxName);
            });

            // check retry register mailbox
            if (m_retryRegister.Count == 0)
            {
                return;
            }

            if (m_lastRetryTime == 0)
            {
                m_lastRetryTime = current;
            }

            if (m_lastRetryTime - current < ReTryRegTime)
            {
                return;
            }

            m_lastRetryTime = current;
            foreach (var retry in m_retryRegister)
            {
                RegisterMailbox(retry.Key, retry.Value.Callback, retry.Value.IsOverride, retry.Value.BroadcastRegister);
            }
        }

        internal bool Send(string mailboxName, MsgPack_S_Rpc_Req req)
        {
            // my mailbox
            if (m_mailboxRecords.ContainsKey(mailboxName))
            {
                // send to myself
                Owner.HandleRpcRequest(req);
                return true;
            }

            uint router = Owner.PickRouter(mailboxName);
            if (router == 0)
            {
                s_logger.LogWarning("Can't pick router, MailboxName={MailboxName}", mailboxName);
                return false;
            }

            MsgPack_S_RpcByMailBox_Proto mailReq = new()
            {
                MailboxName = mailboxName,
                RpcReq = req
            };

            // i am router
            if (router == Owner.GetMyNode()!.NodeInfo.NodeId)
            {
                // send to myself
                RpcService.Instance.AsyncDispatcher.Post(() =>
                {
                    ((RouterNodeManager)Owner).HandleRpcByMailbox(mailReq);
                });
                return true;
            }

            Owner.SendNode(router, mailReq);
            return true;
        }

        private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<MailBoxManager>();

        public const int64_t ReTryRegTime = 2*1000; // 2s
        public const nodeid_t AllRouter = uint.MaxValue;

        private ServerNodeManager Owner { get; }
        private readonly List<string> m_expiredMailboxNames = new();
        private readonly Dictionary<string, RegisterMailboxArgs> m_retryRegister = new();
        private readonly Dictionary<string, MailboxRecord> m_mailboxRecords = new();
        private readonly Dictionary<string, MailboxRegisterCallbackContext> m_mailboxRegisterContexts = new();
        private readonly Dictionary<string, DelegateMailboxUnregisterCallback> m_mailboxRegisterCallbacks = new();
        private int64_t m_lastRetryTime;
    }

    public delegate void DelegateMailboxRegisterCallback(string mailboxName, bool isOverride, nodeid_t nodeId);
    public delegate void DelegateMailboxUnregisterCallback(string mailboxName, bool isOverride, nodeid_t nodeId);
    public delegate nodeid_t PlayerRouterFunc(userid_t playerId);

    internal record MailboxRecord(uint Router, bool BroadcastRegister)
    {
        public nodeid_t Router = Router;
        public bool BroadcastRegister = BroadcastRegister;
    }

    internal record RegisterMailboxArgs(DelegateMailboxRegisterCallback? Callback, bool IsOverride, bool BroadcastRegister)
    {
        public DelegateMailboxRegisterCallback? Callback = Callback;
        public readonly bool IsOverride = IsOverride;
        public readonly bool BroadcastRegister = BroadcastRegister;
    }
}
