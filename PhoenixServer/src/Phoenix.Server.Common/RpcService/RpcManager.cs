using System.Collections.Concurrent;
using MessagePack;
using Microsoft.Extensions.Logging;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Utils;

namespace Phoenix.Server.Common
{
    public sealed class RpcManager : SingletonBase<RpcManager>
    {
        public RpcManager()
        {
            BindDelegate();
        }

        public void BindDelegate()
        {
            RpcService.Instance.DelegateOnRpcRequest += OnRpcRequest;
            // RpcService.Instance.DelegateOnRpcRequestToPlayer += OnRpcRequestToPlayer;
            RpcService.Instance.DelegateOnRpcResponse += OnRpcResponse;
            RpcService.Instance.DelegateOnRegMailboxResult += OnRegMailboxResult;
            RpcService.Instance.DelegateOnUnRegMailboxResult += OnUnRegMailboxResult;
            RpcService.Instance.DelegateOnForwardClientMsg += OnForwardClientMsg;
        }

        // process_client_message
        private void OnForwardClientMsg(userid_t playerId, MsgPackStructBase message)
        {
        }


        private void OnWorldResharding(string hashName)
        {
            /*[engine callback] when world node changed(node auto-scale or node failure),
                re-register local mailbox to new world node
                :param hash_name: _description_
            */
            List<string> outdatedMailbox = new();

            foreach ((string mailboxName, LocalMailboxLookupInfo localMailboxLookupInfo) in m_mailboxEntities)
            {
                var entity = localMailboxLookupInfo.GetEntity();
                if (entity == null)
                {
                    outdatedMailbox.Add(mailboxName);
                    continue;
                }

                nodeid_t current = RpcService.Instance.PickNode(hashName, mailboxName);

                if (localMailboxLookupInfo.MailboxMgrId != current && !localMailboxLookupInfo.IsBroadcastRegister)
                {
                    s_logger.LogInformation("[RPC] world node resharding re-register mailbox, MailboxName={MailboxName}", mailboxName);
                    localMailboxLookupInfo.State = LocalMailboxLookupInfo.MailboxNotRegistered;

                    RegisterMailbox(mailboxName, entity, RegMailboxCallback, true);
                }
            }

            foreach (string mailboxName in outdatedMailbox)
            {
                m_mailboxEntities.Remove(mailboxName);
            }

            return;

            ServerEntity? RegMailboxCallback(string mailboxName, bool isSuccess, nodeid_t worldNodeId)
            {
                s_logger.LogInformation("re-register_mailbox MailboxName={MailboxName} IsSuccess={IsSuccess}, WorldNodeId={WorldNodeId}", mailboxName, isSuccess, worldNodeId);
                return null;
            }
        }

        public void RegisterMailbox(string mailboxName, ServerEntity entity, CallbackOnRegisterRes? regMailboxCb, bool isOverride, bool isBroadcastRegister = false)
        {
            if (m_mailboxEntities.TryGetValue(mailboxName, out var mailboxEntity) && !isOverride && mailboxEntity.State == LocalMailboxLookupInfo.MailboxHasRegistered)
            {
                s_logger.LogError("[RPC] duplicate_mailbox_registration, MailboxName={MailboxName}", mailboxName);
                if (regMailboxCb != null)
                {
                    regMailboxCb(mailboxName, false, mailboxEntity.MailboxMgrId);
                    return;
                }
            }

            mailboxEntity = new LocalMailboxLookupInfo(entity, regMailboxCb);
            mailboxEntity.IsBroadcastRegister = isBroadcastRegister;
            m_mailboxEntities.Add(mailboxName, mailboxEntity);
            RpcModule.RegisterMailbox(mailboxName, isOverride, isBroadcastRegister);
            s_logger.LogInformation("[RPC] register mailbox, MailboxName={MailboxName}", mailboxName);
        }



        private void OnRegMailboxResult(string mailboxName, bool isSuccess, uint mgrId)
        {
            /*'''[engine callback]  mailbox register result
			:param mailbox_name: mailbox name
			:param succ: 成功/失败
			:param mgr_id: router node id*/

            if (!m_mailboxEntities.TryGetValue(mailboxName, out var mailboxEntity))
            {
                s_logger.LogError("[RPC] Can't find mailbox entity, MailboxName={MailboxName}", mailboxName);
                return;
            }

            // callback
            if (mailboxEntity.CallbackOnRegisterRes != null)
            {
                var entity = mailboxEntity.CallbackOnRegisterRes(mailboxName, isSuccess, mgrId);

                if (isSuccess && mailboxEntity.EntityId == 0 && entity != null)
                    mailboxEntity.SetEntity(entity);
            }

            if (!isSuccess)
            {
                s_logger.LogError("[RPC] register_mailbox_failed, MailboxName={MailboxName}", mailboxName);
                return;
            }

            mailboxEntity.State = LocalMailboxLookupInfo.MailboxHasRegistered;

            mailboxEntity.MailboxMgrId = mgrId;

            s_logger.LogInformation("register mailbox success, MailboxName={MailboxName}", mailboxName);
        }

        public void UnRegisterMailBox(string mailboxName)
        {
            if (string.IsNullOrEmpty(mailboxName))
            {
                return;
            }

            s_logger.LogInformation("[RPC] unregister_mailbox, MailboxName={MailboxName}", mailboxName);
            m_mailboxEntities.Remove(mailboxName);
            RpcModule.UnRegisterMailbox(mailboxName);
        }

        private void OnUnRegMailboxResult(string mailboxName, bool isSuccess, nodeid_t mgrId)
        {
            s_logger.LogInformation("[RPC] unregister_mailbox_result success, MailBoxName={MailBoxName} isSuccess={isSuccess} MgrId={MgrId}", mailboxName, isSuccess, mgrId);
        }

        private void OnRpcRequest(string svcName, string funcName, object[] paramList, RpcRequestInfo requestInfo)
        {
            s_logger.LogInformation("OnRpcRequest ReqId={ReqId} SvcName={SvcName} FuncName={FuncName} ParamList=[{ParamList}]" , requestInfo.ReqId, svcName, funcName, paramList.JoinWithSeparator());
            if (requestInfo.IsMailboxRequest)
            {
                object[] args = new object[paramList.Length + 1];
                args[0] = requestInfo;
                for (int i = 0; i < paramList.Length; ++i)
                {
                    args[i + 1] = paramList[i];
                }

                if (m_mailboxEntities.TryGetValue(svcName, out var mailboxEntity))
                {
                    var entity = mailboxEntity.GetEntity();
                    var methodInfo = entity!.GetType().GetMethod(funcName);
                    if (methodInfo == null)
                    {
                        s_logger.LogError("[RPC] func not found. MailboxName={MailboxName}, FuncName={FuncName}", svcName, funcName);
                        return;
                    }
                    methodInfo.Invoke(entity, args);
                }
                else
                {
                    s_logger.LogError("[RPC] cannot_find_mailbox entity, SvcName={SvcName}", svcName);
                    return;
                }
            }
            else
            {
                // var moduleName = svcName.Split('.')[1];
                //Logger.LogWarning($"OnRpcRequest in RpcManager {moduleName}-{funcName}");
                //RpcModuleMethodFinder.Instance.ExecuteModuleMethod(moduleName, funcName, args);
            }
        }

        /*
        public void OnRpcRequestToPlayer(userid_t userId, bool isSpacePlayerProxy, string funcName, object[]? paramList, MsgPackStructBase? message, RpcRequestInfo requestInfo)
        {
            RpcReqMessage rpcReqMessage = new RpcReqMessage("", requestInfo, "", funcName, paramList, message);
            var actor = ActorRegistry.Instance.GetActor(userId);
            if (actor != null)
            {
                actor.PostMessage(rpcReqMessage);
            }
            //object[] paramList = MessagePackSerializer.Deserialize<object[]>(bytedData);
            /*object[] objects = new object[paramList.Length + 1];
            objects[0] = requestInfo;
            for (int i = 0; i < paramList.Length; ++i)
            {
                objects[i + 1] = paramList[i];
            }#1#

            /*            var player = EntityAdmin.Instance.GetBasePlayer(userId) as GameBasePlayer;
                        if (player == null)
                        {
                            Logger.LogError("[RPC] player not found. uid={uid}, is_space_player_proxy={is_space_player_proxy}", userId, isSpacePlayerProxy);
                            return;
                        }

                        MethodInfo method = typeof(GameBasePlayer).GetMethod(funcName);
                        if (method == null)
                        {
                            Logger.LogError("[RPC] func not found. uid={uid}, func_name={func_name}", userId, funcName);
                            return;
                        }
                        method.Invoke(player, objects);#1#
        }

        /*public async Task<bool> Call(RpcTarget target, string funcName, object[]? svc_args, MethodInfo? cb_func = null, object? cbFunObj = null, object[]? cb_args = null, dynamic? hashKey = null, int timeout = 0)
        {
            bool needResp = cb_func != null;
            request_id_t reqId = 0;
            nodeid_t? nodeId = null;
            if (target.Selector == RpcNodeSelectStrategy.DYNAMIC_MAILBOX)
            {
                byte[] packedData = svc_args == null ? Array.Empty<byte>() : MessagePackSerializer.Serialize<object>(svc_args);
                reqId = RpcService.Instance.RpcByMailbox(target.Uri, funcName, packedData, needResp);
            }
            else
            {
                if (hashKey == null && svc_args != null)
                {
                    if (svc_args != null || svc_args!.Length >= 1)
                    {
                        hashKey = svc_args[0];
                    }
                    else
                    {
                        hashKey = 0;
                    }
                }
                nodeId = target.SelectNode(hashKey);
                if (nodeId != null)
                {
                    byte[] packedData = svc_args == null ? Array.Empty<byte>() : MessagePackSerializer.Serialize<object>(svc_args);
                    reqId = RpcService.Instance.RpcCallToDirectNode(nodeId.Value, target.Uri, funcName, packedData, needResp);
                }
            }

            if (reqId == 0)
            {
                Logger.LogError("[RPC] req_id is zero. uri={uri}->{func}", target.Uri, funcName);
                return false;
            }

            if (!needResp)
            {
                return true;
            }

            var handle = AllocHandle(timeout);
            ReqId2AwaitableHandle.Add(reqId, handle);
            await handle.WaitAsync();
            if (handle.IsTimeOut)
            {
                ReqId2AwaitableHandle.Remove(reqId);
                RpcResponseHeader header = new RpcResponseHeader();
                header.Res = new Dictionary<object, object> { { RpcConstants.ERR_CODE, RpcErrCode.RPC_ERROR_TIMEOUT } };
                RpcService.Instance.AsyncDispatcher.Post(() =>
                {
                    object[]? args = null;

                    if (cb_args != null && cb_args.Length > 0)
                    {
                        args = new object[cb_args.Length + 1];
                        args[0] = header;
                        for (int i = 0; i < cb_args.Length; ++i)
                        {
                            args[i + 1] = cb_args[i];
                        }
                    }
                    else
                    {
                        args = [header];
                    }

                    cb_func?.Invoke(cbFunObj, args);
                });

                RpcService.Instance.AsyncDispatcher.Post(() => OnRequestTimeout(reqId));
            }
            else if (!handle.IsCancel)
            {
                RpcResponseHeader header = handle.GetResult<RpcResponseHeader>();
                RpcService.Instance.AsyncDispatcher.Post(() =>
                {
                    object[]? args = null;

                    if (cb_args != null && cb_args.Length > 0)
                    {
                        args = new object[cb_args.Length + 1];
                        args[0] = header;
                        for (int i = 0; i < cb_args.Length; ++i)
                        {
                            args[i + 1] = cb_args[i];
                        }
                    }
                    else
                    {
                        args = [header];
                    }

                    cb_func?.Invoke(cbFunObj, args);
                });
            }
            return true;
        }#1#*/

        public Task<RpcResponseHeader> AsyncCall(RpcTarget target, string funcName, object[]? paramList, dynamic? hashKey = null, int timeout = 10)
        {
            return AsyncCall(target, funcName, paramList, null, hashKey, timeout);
        }

        public Task<RpcResponseHeader> AsyncCall(RpcTarget target, string funcName, MsgPackStructBase message, dynamic? hashKey = null, int timeout = 10)
        {
            return AsyncCall(target, funcName, null, message, hashKey, timeout);
        }

        private async Task<RpcResponseHeader> AsyncCall(RpcTarget target, string funcName, object[]? paramList, MsgPackStructBase? message, dynamic? hashKey = null, int timeout = 10)
        {
            bool needResp = true;
            var handle = m_awaitableHandleManager.AllocHandle(timeout);
            request_id_t reqId = handle.Token;
            RpcService.Instance.AsyncDispatcher.Post(() =>
            {
                if (target.Selector == RpcNodeSelectStrategy.DYNAMIC_MAILBOX)
                {
                    byte[] packedData = paramList == null ? Array.Empty<byte>() : MessagePackSerializer.Serialize<object>(paramList);
                    RpcService.Instance.RpcByMailbox(target.Uri, funcName, packedData, needResp, reqId);
                }
                else
                {
                    if (hashKey == null)
                    {
                        if (paramList is { Length: >= 1 })
                        {
                            hashKey = paramList[0];
                        }
                        else
                        {
                            hashKey = 0;
                        }
                    }

                    nodeid_t? nodeId = RpcService.Instance.SelectNode(target, hashKey);
                    if (nodeId != null)
                    {
                        if (message == null)
                        {
                            byte[] packedData = paramList == null ? [] : MessagePackSerializer.Serialize<object>(paramList);
                            RpcService.Instance.RpcCallToDirectNode(nodeId.Value, target.Uri, funcName, packedData, needResp, reqId);
                        }
                        else
                        {
                            RpcService.Instance.RpcCallToDirectNode(nodeId.Value, target.Uri, funcName, message, needResp, reqId);
                        }
                    }
                    else
                    {
                        s_logger.LogError("[RPC] nodeId is null. Uri={Uri} Func={Func}", target.Uri, funcName);
                        m_awaitableHandleManager.SetResult(handle.Token, new RpcResponseHeader() { RetCode = RpcErrCode.RPC_ROUTER_NOTFOUND });
                        return;
                    }
                }

                if (reqId == 0)
                {
                    s_logger.LogError("[RPC] reqId is zero. Uri={Uri} Func={Func}", target.Uri, funcName);
                    m_awaitableHandleManager.SetResult(handle.Token, new RpcResponseHeader() { RetCode = RpcErrCode.RPC_ROUTER_NOTFOUND });
                    return;
                }

                if (!needResp)
                {
                    m_awaitableHandleManager.SetResult(handle.Token, new RpcResponseHeader() { RetCode = RpcErrCode.RPC_NO_ERROR });
                    return;
                }
            });

            await handle.WaitAsync().ConfigureAwait(false);
            if (handle.IsTimeOut)
            {
                // RpcService.Instance.AsyncDispatcher.Post(OnRequestTimeout, reqId, funcName, message != null ? (int)message.ProtoCode : 0);
                OnRequestTimeout(reqId, funcName, message != null ? (int)message.ProtoCode : 0);
                return new RpcResponseHeader() { RetCode = RpcErrCode.RPC_ERROR_TIMEOUT };
            }

            return handle.GetResult<RpcResponseHeader>();
        }

        private void OnRequestTimeout(request_id_t reqId, string funcName, int protoCode)
        {
            if (protoCode != 0)
            {
                s_logger.LogWarning("[RPC] OnRequestTimeout ReqId={ReqId} FuncName={FuncName} MsgProtoCode={ProtoCode} timeout", reqId, funcName, (EProtoCode)protoCode);
            }
            else
            {
                s_logger.LogWarning("[RPC] OnRequestTimeout ReqId={ReqId} FuncName={FuncName} timeout", reqId, funcName);
            }
        }

        private void OnRpcResponse(nodeid_t nodeId, request_id_t reqId, int retCode, int protoCode, byte[] retData)
        {
            var handle = m_awaitableHandleManager.GetAwaitableHandle(reqId);
            if (handle != null)
            {
                var rspHeader = new RpcResponseHeader { NodeId = nodeId, RetCode = (RpcErrCode)retCode };
                if (protoCode != 0)
                {
                    rspHeader.Res = MsgPackProtoHelper.Deserialize(protoCode, retData);
                }
                else
                {
                    rspHeader.Res = MessagePackSerializer.Deserialize<Dictionary<object, object>>(retData);
                }
                
                m_awaitableHandleManager.SetResult(handle.Token, rspHeader);
            }
        }

        public static RpcTarget CreateTarget(string uri, RpcNodeSelectStrategy selector, nodeid_t nodeId = 0)
        {
            return new RpcTarget(uri, selector, nodeId);
        }

        public static RpcTarget CreateNodeTarget(string uri, uint nodeId)
        {
            return new RpcTarget(uri, selector: RpcNodeSelectStrategy.DEDICATED_NODE, nodeId);
        }

        public async Task<TAck> AwaitableRpcToPlayer<TReq, TAck>(long playerId, TReq req) where TReq : MsgPackStructBase where TAck : MsgPackStructBase
        {
            AwaitableHandle handle = m_awaitableHandleManager.AllocHandle();
            request_id_t reqId = handle.Token;
            RpcService.Instance.RpcToPlayer(playerId, req, true,  reqId);
            await handle.WaitAsync();
            var ack = handle.GetResult<TAck>();
            return ack;
        }

        public void RpcToPlayer<T>(long playerId, T req) where T : MsgPackStructBase
        {
            RpcService.Instance.RpcToPlayer(playerId, req, false);
        }

        private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<RpcManager>();

        private static RpcService RpcModule => RpcService.Instance;

        // private readonly ConcurrentDictionary<request_id_t, AwaitableHandle> m_reqId2AwaitableHandle = new();
        // private Dictionary<request_id_t, RPCCallbackContextBase> ReqId2CallbackContext = new();
        // local cached mailbox lookup table: {mailbox_name: LocalMailboxLookupInfo}
        private readonly Dictionary<string, LocalMailboxLookupInfo> m_mailboxEntities = new();

        private readonly AwaitableHandleManager m_awaitableHandleManager = new();

        public void Tick()
        {
            m_awaitableHandleManager.Tick();
        }
    }

    public class RpcResponseHeader
    {
        public nodeid_t NodeId { get; set; }

        public RpcErrCode RetCode { get; set; }

        public object? Res { get; set; }
    }

    // (mailbox_name, succ, mgr_id)->Any, 返回值可以是与mailbox_name绑定的实体, defaults to None
    public delegate ServerEntity? CallbackOnRegisterRes(string mailboxName, bool isSuccess, nodeid_t mgrId);
}
