/*using MessagePack;
using MessagePack.Formatters;
using Microsoft.Extensions.Logging;
using System.Buffers;
using System.Text;

namespace Phoenix.Server.Common
{
    public static class PropSyncSerializer
    {
        public static bool IsPropIndexOpen { get; set; } = false;

        public static StringIndex PropIndex { get; set; } = new StringIndex();

        public static StringIndex ClientRpcIndex { get; set; } = new StringIndex();

        #region Dump
        *//*public static byte[]? ToMessage(PropObject obj, int filter)
        {

            var buffer = new ArrayBufferWriter<byte>();
            var writer = new MessagePackWriter(buffer);
            if (DumpObjectToMsg(ref writer, obj, filter))
            {
                writer.Flush();
                return buffer.WrittenSpan.ToArray();
            }
            return null;
        }

        public static byte[] ToObjMessage(PropMap propMap, int filter)
        {
            var buffer = new ArrayBufferWriter<byte>();
            var writer = new MessagePackWriter(buffer);

            if (DumpMapToObjMsg(ref writer, propMap, filter))
            {
                writer.Flush();
                return buffer.WrittenSpan.ToArray();
            }

            return Array.Empty<byte>();
        }

        public static byte[] OpLogToMessage(PropOpLogSlice op_slice, int filter, Predicate<PropOpLog>? extra_filter = null)
        {
            var buffer = new ArrayBufferWriter<byte>();
            var writer = new MessagePackWriter(buffer);
            if (DumpOpLogSlice(ref writer, op_slice, filter, extra_filter))
            {
                writer.Flush();
                return buffer.WrittenSpan.ToArray();
            }

            return Array.Empty<byte>();
        }

        private static bool DumpOpLogSlice(ref MessagePackWriter writer, PropOpLogSlice op_slice, int filter, Predicate<PropOpLog>? extra_filter)
        {
            var oplogs = op_slice.Oplogs;
            if (filter > 0)
            {
                var validOpLogs = oplogs.FindAll(opLog =>
                {
                    if (!FilterOpLog(opLog, filter))
                    {
                        return false;
                    }

                    if (extra_filter != null && !extra_filter(opLog))
                    {
                        return false;
                    }

                    return true;
                });

                if (validOpLogs.Count == 0)
                {
                    return true;
                }

                writer.WriteArrayHeader(validOpLogs.Count);
                foreach (var opLog in validOpLogs)
                {
                    DumpOpLog(ref writer, opLog, filter);
                }
            }
            else
            {
                writer.WriteArrayHeader(op_slice.Size());
                foreach (var opLog in oplogs)
                {
                    DumpOpLog(ref writer, opLog, filter);
                }
            }

            return true;
        }

        static bool FilterOpLog(PropOpLog oplog, int filter)
        {
            if (filter <= 0)
            {
                return true;
            }
            var f = oplog.Flag;
            if ((f & filter) != 0)
            {
                return true;
            }
            return false;
        }

        public static void DumpOpLog(ref MessagePackWriter writer, PropOpLog opLog, int filter)
        {
            var buffer = new ArrayBufferWriter<byte>();
            var opWriter = new MessagePackWriter(buffer);

            opWriter.Write((sbyte)opLog.Op);
            opWriter.Write(opLog.KeyPath.Depth);
            for (int i = 0; i < opLog.KeyPath.Depth; ++i)
            {
                PropKey key = opLog.KeyPath.Keys[i];
                if (key.IsIntKey)
                {
                    int64_t ikey = (int16_t)key.GetInt()!;
                    opWriter.Write(ikey);
                }
                else
                {
                    PackPropKey(ref opWriter, key);
                }
            }

            DumpValueToMsg(ref opWriter, opLog.Value, filter);
            opWriter.Flush();

            writer.WriteExtensionFormatHeader(new ExtensionHeader((sbyte)EMsgpackExtType.E_OPLOG, buffer.WrittenCount));
            writer.WriteRaw(buffer.WrittenSpan);
        }

        public static bool DumpObjectToMsg(ref MessagePackWriter writer, PropObject obj, int filter)
        {
            var buffer = new ArrayBufferWriter<byte>();
            var objWriter = new MessagePackWriter(buffer);
            if (obj.Desc == null)
            {
                return false;
            }

            MessagePackSerializer.Serialize<string>(ref objWriter, obj.Desc.Name());

            if (filter != PropFlags.PROP_FLG_ALL_PROP)
            {
                objWriter.WriteNil();
            }
            else
            {
                DumpDynamicPropDescMap(ref objWriter, obj);
            }

            if (filter == PropFlags.PROP_FLG_NONE || filter == PropFlags.PROP_FLG_ALL_PROP)
            {
                //0: dump all sync properties
                //-1: dump all properties
                obj.IterPropsDump(ref objWriter, filter == 0 ? 0 : -1);
            }
            else
            {
                obj.IterPropsDump(ref objWriter, filter);
            }

            objWriter.Flush();
            writer.WriteExtensionFormatHeader(new ExtensionHeader((sbyte)EMsgpackExtType.E_OBJ, buffer.WrittenCount));
            writer.WriteRaw(buffer.WrittenSpan);

            return true;
        }

        public static bool DumpValueToMsg(ref MessagePackWriter writer, PropValue value, int filter)
        {
            EPropValueType tp = value.ValueType;
            switch (tp)
            {
                case EPropValueType.TV_INT64:
                    writer.WriteInt64(value.i);
                    break;
                case EPropValueType.TV_BINARY:
                    writer.Write(value.buf);
                    break;
                case EPropValueType.TV_STR:
                    MessagePackSerializer.Serialize<string>(ref writer, value.str!);
                    break;
                case EPropValueType.TV_FLOAT:
                    writer.Write(value.f);
                    break;
                case EPropValueType.TV_MAP:
                case EPropValueType.TV_ARRAY:
                case EPropValueType.TV_OBJECT:
                case EPropValueType.TV_SET:
                    DumpMixToMsg(ref writer, value.GetMixPtr(), filter);
                    break;
                case EPropValueType.TV_VEC3:
                    {
                        var buffer = new ArrayBufferWriter<byte>();
                        var vecWriter = new MessagePackWriter(buffer);
                        var vec = value.vec3;
                        vecWriter.Write(vec.X);
                        vecWriter.Write(vec.Y);
                        vecWriter.Write(vec.Z);
                        vecWriter.Flush();
                        writer.WriteExtensionFormatHeader(new ExtensionHeader((sbyte)EMsgpackExtType.E_VEC3, buffer.WrittenCount));
                        writer.WriteRaw(buffer.WrittenSpan);
                        break;
                    }
                case EPropValueType.TV_BITSET:
                    {
                        var buffer = new ArrayBufferWriter<byte>();
                        var bitsetWriter = new MessagePackWriter(buffer);
                        string bitsetStr = value.bitset.EncodeToString();
                        bitsetWriter.Write(Encoding.UTF8.GetBytes(bitsetStr));
                        bitsetWriter.Flush();
                        writer.WriteExtensionFormatHeader(new ExtensionHeader((sbyte)EMsgpackExtType.E_BITSET, buffer.WrittenCount));
                        writer.WriteRaw(buffer.WrittenSpan);
                        break;
                    }
                case EPropValueType.TV_NULL:
                    writer.WriteNil();
                    break;
                case EPropValueType.TV_PY_NONE:
                    {
                        writer.WriteExtensionFormatHeader(new ExtensionHeader((sbyte)EMsgpackExtType.E_PY_NONE, 0));
                        break;
                    }
                default:
                    throw new Exception("PropValueTypeNotSupported");
            }
            return true;
        }

        public static bool DumpMixToMsg(ref MessagePackWriter writer, PropMix? pval, int filter)
        {
            if (pval == null)
            {
                return true;
            }

            var tp = pval.ValueType;
            if (tp == EPropValueType.TV_MAP)
            {
                PropMap ptr = (PropMap)pval;
                if (!string.IsNullOrEmpty(ptr.GetObjName()))
                {
                    //is a copy of a object
                    return DumpMapToObjMsg(ref writer, ptr, filter);
                }

                writer.WriteMapHeader((uint32_t)(ptr.Size()));
                ptr.DumpItems(ref writer, filter);
            }
            else if (tp == EPropValueType.TV_ARRAY)
            {
                PropArray ptr = (PropArray)(pval);
                writer.WriteArrayHeader((uint)ptr.Size());
                ptr.DumpItems(ref writer, filter);
            }
            else if (tp == EPropValueType.TV_OBJECT)
            {
                PropObject ptr = (PropObject)(pval);
                DumpObjectToMsg(ref writer, ptr, filter);
            }
            else if (tp == EPropValueType.TV_SET)
            {
                PropSet ptr = (PropSet)(pval);
                DumpSetToMsg(ref writer, ptr, filter);
            }
            return true;
        }

        public static bool DumpMapToObjMsg(ref MessagePackWriter writer, PropMap ptr, int filter)
        {
            string objName = ptr.GetObjName();
            var objDesc = PropRegistry.Get().GetObjectDesc(objName);
            if (objDesc == null)
            {
                Logger.LogWarning("Dump object to msg failed, can not found object desc, obj_name={obj_name}", objName);
                return false;
            }

            var buffer = new ArrayBufferWriter<byte>();
            var objWriter = new MessagePackWriter(buffer);
            MessagePackSerializer.Serialize<string>(ref objWriter, objName);

            if (filter != PropFlags.PROP_FLG_ALL_PROP)
            {
                objWriter.WriteNil();
            }
            else
            {
                // TODO：动态组件的字典没有迁移 （mos）
                objWriter.WriteNil();
            }

            if (filter == PropFlags.PROP_FLG_NONE || filter == PropFlags.PROP_FLG_ALL_PROP)
            {
                //0: dump all sync properties
                //-1: dump all properties
                ptr.DumpItems(ref objWriter, filter);
            }
            else
            {
                ptr.DumpItems(ref objWriter, filter);
            }

            writer.WriteExtensionFormatHeader(new ExtensionHeader((sbyte)EMsgpackExtType.E_OBJ, buffer.WrittenCount));
            writer.WriteRaw(buffer.WrittenSpan);
            return true;
        }

        public static void DumpSetToMsg(ref MessagePackWriter writer, PropSet ptr, int filter)
        {
            var buffer = new ArrayBufferWriter<byte>();
            var setWriter = new MessagePackWriter(buffer);
            setWriter.WriteArrayHeader((uint)ptr.Size());
            ptr.DumpItems(ref setWriter, filter);
            setWriter.Flush();
            writer.WriteExtensionFormatHeader(new ExtensionHeader((sbyte)EMsgpackExtType.E_SET, buffer.WrittenCount));
            writer.WriteRaw(buffer.WrittenSpan);
        }

        public static void dumpkv(ref MessagePackWriter writer, PropValue val, int filter)
        {

        }

        public static void DumpDynamicPropDescMap(ref MessagePackWriter writer, PropObject obj)
        {
            if (obj.DynamicPropDescs == null || obj.DynamicPropDescs.Count == 0)
            {
                writer.WriteNil();
                return;
            }
            writer.WriteArrayHeader(obj.DynamicPropDescs.Count);
            foreach (var pair in obj.DynamicPropDescs)
            {
                DumpDynamicPropDesc(ref writer, pair.Value);
            }
        }

        public static void DumpDynamicPropDesc(ref MessagePackWriter writer, DynamicPropDesc desc)
        {
            if (desc == null)
            {
                return;
            }

            PackPropKey(ref writer, desc.NameKey);

            var buffer = new ArrayBufferWriter<byte>();
            MessagePackWriter tempWriter = new MessagePackWriter(buffer);
            tempWriter.WriteUInt16(desc.Flag);
            tempWriter.WriteUInt32(desc.ValueIndex);
            string typeName = desc.Detail != null ? desc.Detail.TypeName : "";
            MessagePackSerializer.Serialize<string>(ref tempWriter, typeName);
            tempWriter.WriteRaw(desc.Data);

            writer.WriteExtensionFormatHeader(new ExtensionHeader((sbyte)EMsgpackExtType.E_DYNAMIC_PROP_DESC, buffer.WrittenCount));
            writer.WriteRaw(buffer.WrittenSpan);
        }

        public static void PackPropKey(ref MessagePackWriter writer, PropKey key)
        {
            if (IsPropIndexOpen && key.StringIndex > 0)
            {
                PackIndex(writer, key.StringIndex);
            }
            else
            {
                MessagePackSerializer.Serialize<string>(ref writer, key.GetString());
            }
        }*//*

        public static void PackIndex(MessagePackWriter writer, uint stringIndex)
        {
            var buffer = new ArrayBufferWriter<byte>();
            MessagePackWriter tempWriter = new MessagePackWriter(buffer);
            tempWriter.Write(stringIndex);
            tempWriter.Flush();
            uint size = (uint)buffer.WrittenCount;
            writer.WriteExtensionFormatHeader(new ExtensionHeader((sbyte)EMsgpackExtType.E_INDEX, size));
            writer.WriteRaw(buffer.WrittenSpan);
        }

        public static void DumpValue(ref MessagePackWriter writer, object value, int16_t depth = 0)
        {
            if (depth > kMaxDepth)
            {
                throw new Exception($"depth limit exceeded depth={depth}");
            }
            if (value is Boolean)
            {
                writer.Write((bool)value);
            }
            else if (IsIntergerType(value.GetType()))
            {
                writer.WriteInt64((long)value);
            }
            else if (IsFloatingPointType(value.GetType()))
            {
                writer.Write((double)value);
            }
            else if (value == null)
            {
                writer.WriteNil();
            }
            else if (value is string)
            {
                writer.Write((string)value);
            }
            else if (value is byte[])
            {
                writer.WriteBinHeader(((byte[])value).Length);
                writer.Write((byte[])value);
            }
            *//*else if (value is FVec3)
            {
                var buffer = new ArrayBufferWriter<byte>();
                MessagePackWriter vecWriter = new MessagePackWriter(buffer);
                vecWriter.Write(((FVec3)value).X);
                vecWriter.Write(((FVec3)value).Y);
                vecWriter.Write(((FVec3)value).Z);
                vecWriter.Flush();
                writer.WriteExtensionFormatHeader(new ExtensionHeader((sbyte)EMsgpackExtType.E_VEC3, buffer.WrittenCount));
                writer.WriteRaw(buffer.WrittenSpan);
            }*//*
            else if (value is ISet<object>)
            {
                var buffer = new ArrayBufferWriter<byte>();
                MessagePackWriter vecWriter = new MessagePackWriter(buffer);
            }
            else if (value is IList<object>)
            {
                var array = (IList<object>)value;
                writer.WriteArrayHeader(array.Count);
                foreach (var item in array)
                {
                    DumpValue(ref writer, item, (short)(depth + 1));
                }
            }
           *//* else if (value is PropArray)
            {
                var array = (PropArray)value;
                writer.WriteArrayHeader(array.Size());
                for (int i = 0; i < array.Size(); i++)
                {
                    DumpValue(ref writer, Value2Object(array.GetItem(i)), (short)(depth + 1));
                }
            }*//*
            else if (value is IDictionary<object, object>)
            {
                var dict = (IDictionary<object, object>)value;
                writer.WriteMapHeader(dict.Count);
                foreach (var item in dict)
                {
                    DumpValue(ref writer, item.Key, (short)(depth + 1));
                    DumpValue(ref writer, item.Value, (short)(depth + 1));
                }
            }
            *//*else if (value is PropMap)
            {
                var dict = (PropMap)value;
                writer.WriteMapHeader(dict.Size());
                foreach (var pair in dict)
                {
                    object key = KeyToObject(pair.Key);
                    object val = Value2Object(pair.Value);
                    DumpValue(ref writer, key, (short)(depth + 1));
                    DumpValue(ref writer, val, (short)(depth + 1));
                }
            }*//*
            else if(value is ISet<object>)
            {
                var set = value as ISet<object>;
                var buffer = new ArrayBufferWriter<byte>();
                MessagePackWriter setWriter = new MessagePackWriter(buffer);

                foreach (var item in set)
                {
                    DumpValue(ref setWriter, item, (short)(depth + 1));
                }

                setWriter.Flush();
                writer.WriteExtensionFormatHeader(new ExtensionHeader((sbyte)MsgPackCustomType.CUSTOM_SET, buffer.WrittenCount));
                writer.WriteRaw(buffer.WrittenSpan);
            }
            *//*else if (value is PropSet)
            {
                var buffer = new ArrayBufferWriter<byte>();
                MessagePackWriter setWriter = new MessagePackWriter(buffer);

                var set = (PropSet)value;
                foreach (var item in set)
                {
                    DumpValue(ref setWriter, Value2Object(item), (short)(depth + 1));
                }

                setWriter.Flush();
                writer.WriteExtensionFormatHeader(new ExtensionHeader((sbyte)MsgPackCustomType.CUSTOM_SET, buffer.WrittenCount));
                writer.WriteRaw(buffer.WrittenSpan);
            }
            else if (value is GameObject)
            {
                DumpGameObject(ref writer, (GameObject)value);
            }*//*
        }

        *//*private static void DumpGameObject(ref MessagePackWriter writer, GameObject value)
        {
            PropObject propObj = value.PropObject;
            if (propObj == null)
            {
                throw new Exception("GameObject.PropObject is null");
            }
            var buffer = new ArrayBufferWriter<byte>();
            MessagePackWriter objWriter = new MessagePackWriter(buffer);
            bool result = DumpObjectToMsg(ref objWriter, propObj, PropFlags.PROP_FLG_ALL_PROP);
            if (!result)
            {
                Logger.LogError("Dump object to msg failed, can not found object desc, obj_name={obj_name}", propObj.ObjectTypeName());
                return;
            }
            objWriter.Flush();
            writer.WriteExtensionFormatHeader(new ExtensionHeader((sbyte)MsgPackCustomType.CUSTOM_GAMEOBJECT, buffer.WrittenCount));
            writer.WriteRaw(buffer.WrittenSpan);
        }

        private static object KeyToObject(PropKey key)
        {
            if (key.IsIntKey)
            {
                return key.GetInt()!.Value;
            }
            else
            {
                return key.GetString()!;
            }
        }

        private static object? Value2Object(PropValue propVal)
        {
            switch (propVal.ValueType)
            {
                case EPropValueType.TV_MAP:
                    return (propVal.mix as PropMap);
                case EPropValueType.TV_ARRAY:
                    return (propVal.mix as PropArray);
                case EPropValueType.TV_OBJECT:
                    return (propVal.mix as PropObject);
                case EPropValueType.TV_SET:
                    return (propVal.mix as PropSet);
                default:
                    return ConvertPlainValueToObject(propVal);
            }
        }

        static object ConvertPlainValueToObject(PropValue val)
        {
            switch (val.ValueType)
            {
                case EPropValueType.TV_PY_NONE:
                    return null;
                case EPropValueType.TV_INT64:
                    return (val.i);
                case EPropValueType.TV_STR:
                    if (val.str == string.Empty)
                    {
                        return "";
                    }
                    else
                    {
                        return val.str;
                    }
                case EPropValueType.TV_BINARY:
                    if (val.buf == null)
                    {
                        return Array.Empty<byte>();
                    }
                    else
                    {
                        return val.buf;
                    }
                case EPropValueType.TV_FLOAT:
                    return val.f;
                case EPropValueType.TV_VEC3:
                    {
                        return val.vec3;
                    }
                case EPropValueType.TV_BITSET:
                    return val.bitset;
                default:
                    break;
            }
            return null;
        }*//*

        private static readonly int16_t kMaxDepth = 1000;       //递归深度限制

        #endregion Dump

        #region Parser

        public static bool FromMessage(PropObject obj, byte[] bytes)
        {
            var reader = new MessagePackReader(bytes);
            if (reader.NextMessagePackType != MessagePackType.Extension)
            {
                return false;
            }

            var result = reader.ReadExtensionFormat();
            var objReader = new MessagePackReader(result.Data);
            return ParseObjMsgValue(ref objReader, obj);
        }

        public static PropOpLogSlice OpLogFromMessage(byte[] msg)
        {
            PropOpLogSlice slice = new PropOpLogSlice();
            MessagePackReader reader = new MessagePackReader(msg);
            ParseOpLogSlice(ref reader, slice);
            return slice;
        }

        public static bool ParseOpLogSlice(ref MessagePackReader reader, PropOpLogSlice slice)
        {
            if (reader.NextCode != (sbyte)MessagePackType.Array)
            {
                return false;
            }

            int size = reader.ReadArrayHeader();
            for (int i = 0; i < size; ++i)
            {
                if (reader.NextCode != (sbyte)EMsgpackExtType.E_OPLOG)
                {
                    return false;
                }

                if (ParseOpLogMsg(ref reader, out var op))
                {
                    slice.MergeAppend(op, 0);
                }
            }
            return true;
        }

        private static bool ParseOpLogMsg(ref MessagePackReader reader, out PropOpLog? opLog)
        {
            var exResult = reader.ReadExtensionFormat();
            System.Diagnostics.Debug.Assert(exResult.TypeCode == (sbyte)EMsgpackExtType.E_OPLOG);
            var opReader = new MessagePackReader(exResult.Data);
            opLog = null;
            if (opReader.NextCode != (sbyte)MessagePackType.Integer)
            {
                return false;
            }

            opLog = new PropOpLog();
            opLog.Op = (EPropOpcode)opReader.ReadInt32();
            while (!opReader.End)
            {
                if (opReader.NextCode != (sbyte)MessagePackType.Integer)
                {
                    return false;
                }
                int depth = opReader.ReadInt32();

                for (int i = 0; i < depth; ++i)
                {
                    if (opReader.NextCode == (sbyte)MessagePackType.Integer)
                    {
                        int64_t key = opReader.ReadInt64();
                        opLog.KeyPath.Append(new PropKey(key));
                    }
                    else if (opReader.NextCode == (sbyte)MessagePackType.String)
                    {
                        string key = opReader.ReadString()!;
                        opLog.KeyPath.Append(new PropKey(key));
                    }
                    else if (opReader.NextCode == (sbyte)MessagePackType.Extension)
                    {
                        if (opLog.Op == EPropOpcode.OP_RPC)
                        {
                            string key = ParseRpcIndex(ref opReader);
                            opLog.KeyPath.Append(new PropKey(key));
                        }
                        else
                        {
                            string key = ParsePropIndex(ref opReader);
                            opLog.KeyPath.Append(new PropKey(key));
                        }
                    }
                    else
                    {
                        return false;
                    }
                }
            }

            return true;
        }

        public static uint32_t ParseIndex(ref MessagePackReader reader)
        {
            if ((reader.NextMessagePackType != MessagePackType.Extension) ||
                (reader.TryReadExtensionFormatHeader(out var header) || header.TypeCode != (sbyte)EMsgpackExtType.E_INDEX))
            {
                return 0;
            }

            var bytes = reader.ReadRaw(header.Length);
            var indexReader = new MessagePackReader(bytes);
            return indexReader.ReadUInt32();
        }

        public static string ParsePropIndex(ref MessagePackReader reader)
        {
            uint32_t index = ParseIndex(ref reader);
            string key = PropIndex.GetStr(index);
            if (string.IsNullOrEmpty(key))
            {
                Logger.LogWarning("Parse prop index failed, index={index}", index);
            }
            return key;
        }

        public static string ParseRpcIndex(ref MessagePackReader reader)
        {
            uint32_t index = ParseIndex(ref reader);
            string key = ClientRpcIndex.GetStr(index);
            if (string.IsNullOrEmpty(key))
            {
                Logger.LogWarning("Parse rpc index failed, index={index}", index);
            }
            return key;
        }

        public static string ParsePropKey(ref MessagePackReader reader)
        {

            if (reader.NextMessagePackType == MessagePackType.String)
            {
                string key = reader.ReadString();
                return key;
            }

            if (reader.NextMessagePackType == MessagePackType.Extension)
            {
                return ParsePropIndex(ref reader);
            }

            Logger.LogWarning("Parse prop key failed, type={type}", reader.NextMessagePackType);
            return string.Empty;
        }

        public static PropValue ParseMsgMap(ref MessagePackReader reader, PropDescDetail descDetail)
        {
            if (descDetail.ValueType != EPropValueType.TV_MAP)
            {
                Logger.LogWarning("ParseMsgMap Fail, type_not_match. type={type}", descDetail.ValueType);
                return PropValue.Dummy();
            }

            var map_desc = (PropMapDesc)(descDetail);
            PropMap propMap = new PropMap(map_desc);
            if (reader.NextMessagePackType == MessagePackType.Map)
            {
                var mapSize = reader.ReadMapHeader();
                for (var i = 0; i < mapSize; ++i)
                {
                    PropKey? key = null;
                    if (reader.NextMessagePackType == MessagePackType.Integer)
                    {
                        var intKey = reader.ReadInt32();
                        key =  new PropKey(intKey);
                    }
                    else if (reader.NextMessagePackType == MessagePackType.String)
                    {
                        var strKey = reader.ReadString();
                        key = new PropKey(strKey);
                    }
                    else if (reader.NextMessagePackType == MessagePackType.Extension)
                    {
                        string strKey = ParsePropIndex(ref reader);
                        key = new PropKey(strKey);
                    }

                    if (key == null)
                    {
                        Logger.LogWarning("ParseMsgMap Fail, type_not_match. type={type}", descDetail.ValueType);
                        System.Diagnostics.Debug.Assert(false, $"ParseMsgMap Fail, type_not_match. type={descDetail.ValueType}");
                        return PropValue.Dummy();
                    }

                    PropValue propValue = ParseMsgValue(ref reader, map_desc.DescDetail);
                    if (propValue.ValueType == EPropValueType.TV_NULL)
                    {
                        continue;
                    }

                    if (propMap.SetItem(key, propValue) != 0)
                    {
                        return PropValue.Dummy();
                    }
                }
            }
            return new PropValue(propMap, EPropValueType.TV_MAP);
        }

        public static PropValue ParseMsgValue(ref MessagePackReader reader, PropDescDetail? descDetail)
        {
            switch (reader.NextMessagePackType)
            {
                case MessagePackType.Integer:
                    if (descDetail != null && descDetail.ValueType != EPropValueType.TV_INT64)
                    {
                        Logger.LogWarning("parseMsgValue_fail, type_not_match. type={}", descDetail.ValueType);
                        return PropValue.Dummy();
                    }
                    return new PropValue(reader.ReadInt64());
                case MessagePackType.Float:
                    if (descDetail != null && descDetail.ValueType != EPropValueType.TV_FLOAT)
                    {
                        Logger.LogWarning("parseMsgValue_fail, type_not_match. type={}", descDetail.ValueType);
                        return PropValue.Dummy();
                    }
                    return new PropValue(reader.ReadDouble());
                case MessagePackType.String:
                    {
                        if (descDetail != null && descDetail.ValueType != EPropValueType.TV_STR)
                        {
                            Logger.LogWarning("parseMsgValue_fail, type_not_match. type={}", descDetail.ValueType);
                            return PropValue.Dummy();
                        }
                        return new PropValue(reader.ReadString());
                    }
                case MessagePackType.Binary:
                    {
                        if (descDetail != null && descDetail.ValueType != EPropValueType.TV_BINARY)
                        {
                            Logger.LogWarning("parseMsgValue_fail, type_not_match. type={}", descDetail.ValueType);
                            return PropValue.Dummy();
                        }
                        var bytes = reader.ReadBytes().Value.ToArray<byte>();
                        return new PropValue(bytes);
                    }
                case MessagePackType.Array:
                    {
                        int sz = reader.ReadArrayHeader();
                        if (descDetail != null && descDetail.ValueType != EPropValueType.TV_ARRAY)
                        {
                            Logger.LogWarning("parseMsgValue_fail, type_not_match. type={}", descDetail.ValueType);
                            return PropValue.Dummy();
                        }
                        var array_desc = (PropArrayDesc)(descDetail);
                        PropArray parr = new PropArray(array_desc);
                        for (int i = 0; i<sz; i++)
                        {
                            PropValue p_item = ParseMsgValue(ref reader, parr.GetValueTypeDesc());
                            parr.Append(p_item);
                        }
                        return new PropValue(parr, EPropValueType.TV_ARRAY);
                    }
                case MessagePackType.Map:
                    return ParseMsgMap(ref reader, descDetail);
                case MessagePackType.Extension:
                    {
                        reader.TryReadExtensionFormatHeader(out var exHeader);
                        EMsgpackExtType typeCode = (EMsgpackExtType)exHeader.TypeCode;
                        switch (typeCode)
                        {
                            case EMsgpackExtType.E_VEC3:
                                if (descDetail != null && descDetail.ValueType != EPropValueType.TV_VEC3)
                                {
                                    Logger.LogWarning("parseMsgValue_fail, type_not_match. type={}", descDetail.ValueType);
                                    return PropValue.Dummy();
                                }
                                return ParseVec3(ref reader);
                            case EMsgpackExtType.E_PY_NONE:
                                return PropValue.Create(EPropValueType.TV_PY_NONE);
                            case EMsgpackExtType.E_OBJ:
                                {
                                    if (descDetail != null && descDetail.ValueType != EPropValueType.TV_OBJECT)
                                    {
                                        Logger.LogWarning("parseMsgValue_fail, type_not_match. type={}", descDetail.ValueType);
                                        return PropValue.Dummy();
                                    }
                                    PropObject new_obj = new PropObject();
                                    if (!ParseObjMsgValue(ref reader, new_obj))
                                    {
                                        return PropValue.Dummy();
                                    }
                                    var val = new PropValue(new_obj, EPropValueType.TV_OBJECT);
                                    return val;
                                }
                            case EMsgpackExtType.E_INDEX:
                                {
                                    string sval = ParsePropIndex(ref reader);
                                    return new PropValue(sval);
                                }
                            case EMsgpackExtType.E_BITSET:
                                if (descDetail != null && descDetail.ValueType != EPropValueType.TV_BITSET)
                                {
                                    Logger.LogWarning("parseMsgValue_fail, type_not_match. type={}", descDetail.ValueType);
                                    return PropValue.Dummy();
                                }
                                return ParseBitset(ref reader);
                            case EMsgpackExtType.E_SET:
                                if (descDetail != null && descDetail.ValueType != EPropValueType.TV_SET)
                                {
                                    Logger.LogWarning("parseMsgValue_fail, type_not_match. type={}", descDetail.ValueType);
                                    return PropValue.Dummy();
                                }
                                return ParseMsgSet(ref reader, descDetail);
                            default:
                                break;
                        }
                    }
                    break;
                case MessagePackType.Nil:
                    reader.ReadNil();
                    break;
                case MessagePackType.Boolean:
                    break;
            } //end switch(o.type)

            return PropValue.Dummy();
        }

        public static PropValue ParseMsgSet(ref MessagePackReader reader, PropDescDetail? descDetail)
        {
            int len = reader.ReadArrayHeader();
            PropSetDesc set_desc = (PropSetDesc)descDetail;
            PropSet propSet = new PropSet(set_desc);

            for (int i = 0; i < len; i++)
            {
                PropValue item = ParseMsgValue(ref reader, null);
                if (item.ValueType == EPropValueType.TV_NULL)
                {
                    continue;
                }
                propSet.AddItem(item);
            }

            return new PropValue(propSet, EPropValueType.TV_SET);

        }

        public static PropValue ParseBitset(ref MessagePackReader reader)
        {
            PropValue propValue = PropValue.Create(EPropValueType.TV_BITSET);
            var bytes = reader.ReadBytes().Value.ToArray();
            string base64String = Encoding.UTF8.GetString(bytes);
            var propSet = new PropBitset();
            propSet.ParseFromString(base64String);
            propValue.SetBitset(propSet);
            return propValue;
        }

        public static bool ParseObjMsgValue(ref MessagePackReader objReader, PropObject pobj)
        {
            *//*            var exResult = reader.ReadExtensionFormat();
                        System.Diagnostics.Debug.Assert(exResult.TypeCode == (sbyte)EMsgpackExtType.E_OBJ);
                        MessagePackReader objReader = new MessagePackReader(exResult.Data);*//*
            string objName = ParsePropKey(ref objReader);
            if (string.IsNullOrEmpty(objName))
            {
                Logger.LogWarning("parse_sub_obj_from_msgpack_failed {objName}", objName);
                return false;
            }

            if (pobj.Desc == null)
            {
                // Bind one according to name
                var ptrDesc = PropRegistry.Get().GetObjectDesc(objName);
                if (ptrDesc == null) return false;
                pobj.AttachObjectDesc(ptrDesc);
            }

            // Parse dynamic property desc
            if (objReader.NextMessagePackType == MessagePackType.Array)
            {
                if (!ParseDynamicPropDescMap(ref objReader, pobj))
                {
                    Logger.LogWarning("parse dynamic property desc failed! class_name={class_name}", objName);
                    return false;
                }
            }
            else if (objReader.NextMessagePackType == MessagePackType.Nil)
            {
                objReader.ReadNil();
            }

            PropDesc? propDesc;
            while (!objReader.End)
            {
                string key = ParsePropKey(ref objReader);
                if (string.IsNullOrEmpty(key))
                {
                    Logger.LogInformation("parse_sub_obj_from_msgpack failed {objName}", objName);
                    return false;
                }

                propDesc = pobj.GetPropDesc(key);
                if (propDesc == null)
                {
                    Logger.LogWarning("field_desc_is_nullptr field_name={field_name}", key);
                    continue;
                }

                if (objReader.NextMessagePackType == MessagePackType.Extension)
                {
                    var valHeader = objReader.ReadExtensionFormat();
                    MessagePackReader valReader = new MessagePackReader(valHeader.Data);
                    if (propDesc.IsObject() && valHeader.TypeCode == (sbyte)EMsgpackExtType.E_OBJ)
                    {
                        PropValue val = pobj.GetProp(propDesc);
                        if (!val.IsNull())
                        {
                            if (!ParseObjMsgValue(ref valReader, (PropObject)val.GetMixPtr()))
                            {
                                Logger.LogWarning("parse_sub_obj_from_msgpack_failed, my_class_name={}, sub_obj_class_name={}", pobj.ObjectTypeName(), propDesc.Detail.Name());
                            }
                            continue;
                        }
                    }
                }

                PropValue item = ParseMsgValue(ref objReader, propDesc.Detail);
                if (item.IsNull())
                {
                    Logger.LogWarning("Parse_value_failed, type={type}", propDesc.Detail.Name());
                    continue;
                }
                pobj.SetProp(propDesc, item);
            }

            return true;
        }

        public static bool ParseDynamicPropDescMap(ref MessagePackReader reader, PropObject pobj)
        {
            int size = reader.ReadArrayHeader();
            for (int i = 0; i < size; ++i)
            {
                DynamicPropDesc dynamicPropDesc = ParseDynamicPropDesc(ref reader);
                string name = dynamicPropDesc.Name;
                pobj.AddDynamicPropDesc(name, dynamicPropDesc);
            }
            return true;
        }

        public static DynamicPropDesc ParseDynamicPropDesc(ref MessagePackReader reader)
        {
            string name = ParsePropKey(ref reader);
            uint16_t flag = reader.ReadUInt16();
            uint32_t valueIndex = reader.ReadUInt32();
            string typeName = reader.ReadString();
            byte[] data = reader.ReadBytes().Value.ToArray();

            var desc = new DynamicPropDesc(name, flag);
            desc.SetDetail(PropRegistry.Instance.GetObjectDesc(typeName));
            if (!string.IsNullOrEmpty(typeName) && desc.Detail == null)
            {
                Logger.LogWarning("class_<{}>_not_register_to_engine!", typeName);
            }
            desc.ValueIndex = valueIndex;
            desc.Data = data;
            return desc;

        }

        public static PropValue ParseVec3(ref MessagePackReader reader)
        {
            PropValue propValue = PropValue.Create(EPropValueType.TV_BITSET);
            // var header = reader.ReadExtensionFormatHeader();
            float[] vec3 = new float[3] { 0.0f, 0.0f, 0.0f };
            for (int i = 0; i < 3; ++i)
            {
                vec3[i] = reader.ReadSingle();
            }
            propValue.SetFVec3(new FVec3 { X = vec3[0], Y = vec3[1], Z = vec3[2] });
            return propValue;
        }

        public static object ParseValue(ref MessagePackReader reader)
        {
            object? obj = null;
            switch (reader.NextMessagePackType)
            {
                case MessagePackType.Unknown:
                    throw new NotImplementedException();
                case MessagePackType.Integer:
                    obj = reader.ReadInt64();
                    break;
                case MessagePackType.Nil:
                    obj = reader.ReadNil();
                    break;
                case MessagePackType.Boolean:
                    obj = reader.ReadBoolean();
                    break;
                case MessagePackType.Float:
                    obj = reader.ReadDouble();
                    break;
                case MessagePackType.String:
                    obj = reader.ReadString();
                    break;
                case MessagePackType.Binary:
                    obj = reader.ReadBytes();
                    break;
                case MessagePackType.Array:
                    obj = ParseArray(ref reader);
                    break;
                case MessagePackType.Map:
                    obj = ParseMap(ref reader);
                    break;
                case MessagePackType.Extension:
                    obj = ParseCustomData(ref reader);
                    break;
            }
            return obj;
        }

        private static Dictionary<object, object> ParseMap(ref MessagePackReader reader)
        {
            Dictionary<object, object> dict = new();
            int size = reader.ReadMapHeader();
            for (int i = 0; i < size; ++i)
            {
                object key;
                if (!CheckVaildKey(ref reader))
                {
                    continue;  //skip illegal keys
                }
                else
                {
                    key = ParseValue(ref reader);
                }
                var value = ParseValue(ref reader);
                dict[key] = value;
            }
            return dict;
        }

        public static bool CheckVaildKey(ref MessagePackReader reader)
        {
            if (reader.NextMessagePackType == MessagePackType.Integer ||
                reader.NextMessagePackType == MessagePackType.String ||
                reader.NextMessagePackType == MessagePackType.Binary ||
                (reader.NextMessagePackType == MessagePackType.Extension && reader.NextCode == (byte)MsgPackCustomType.CUSTOM_TUPLE))
            {
                return true;
            }

            return false;
        }

        private static object? ParseCustomData(ref MessagePackReader reader)
        {
            var ext = reader.ReadExtensionFormat();
            MsgPackCustomType type = (MsgPackCustomType)ext.TypeCode;
            switch (type)
            {
                case MsgPackCustomType.CUSTOM_SET:
                    {
                        MessagePackReader objReader = new MessagePackReader(ext.Data);
                        object obj = ParseCustomSet(ref objReader);
                        return obj;
                    }
                case MsgPackCustomType.CUSTOM_GAMEOBJECT:
                    {
                        MessagePackReader objReader = new MessagePackReader(ext.Data);
                        object obj = ParseGameObject(ref objReader);
                        return obj;
                    }
                default:
                    throw new NotImplementedException();
            }
        }

        private static object ParseCustomSet(ref MessagePackReader reader)
        {
            ISet<object> set = new HashSet<object>();
            while(!reader.End)
            {
                var obj = ParseValue(ref reader);
                set.Add(obj);
            }
            return set;
        }

        private static object ParseGameObject(ref MessagePackReader reader)
        {
            var ext = reader.ReadExtensionFormat();
            MessagePackReader objReader = new MessagePackReader(ext.Data);
            PropObject propObject = new PropObject();
            if (ParseObjMsgValue(ref objReader, propObject))
            {
                if (propObject.Owner == null)
                {
                    var gameObj = GameObjectMgr.Instance.CreateGameObjectFromPropObject(propObject);
                    if (gameObj == null)
                    {
                        Logger.LogError("ParseGameObject CreateGameObjectFromPropObject failed. obj_name={obj_name}", propObject.ObjectTypeName());
                        return null;
                    }
                    if (propObject.Parent != null)
                    {
                        propObject.OnAttachParent(propObject.Parent);
                    }
                }
                return propObject.Owner;
            }
            else
            {
                return null;
                throw new Exception("ParseGameObject ParseObjMsgValue failed.");
            }
        }

        private static object? ParseArray(ref MessagePackReader reader)
        {
            int size = reader.ReadArrayHeader();
            var objArray = new object[size];
            for (int i = 0; i < size; ++i)
            {
                var item = ParseValue(ref reader);
                objArray[i] = item;
            }
            return objArray;
        }

        #endregion Parser

        public static readonly ILogger Logger = InternalLoggerFactory.LoggerFactory.CreateLogger("PropSyncSerializer");

        public static bool IsIntergerType(Type type)
        {
            bool isInteger = type.IsPrimitive &&
                         (type == typeof(int) || type == typeof(long) ||
                          type == typeof(short) || type == typeof(byte) ||
                          type == typeof(uint) || type == typeof(ulong) ||
                          type == typeof(ushort) || type == typeof(sbyte));
            return isInteger;
        }

        public static bool IsFloatingPointType(Type type)
        {
            return type == typeof(float) || type == typeof(double);
        }

    }

    public enum EMsgpackExtType : sbyte
    {
        E_OBJ = 0,
        E_VEC3,
        E_OPLOG,
        E_PY_NONE,
        E_BITSET,
        E_DYNAMIC_PROP_DESC,
        E_INDEX,
        E_SET,
    };

    public enum MsgPackCustomType
    {
        CUSTOM_SET = 0,
        CUSTOM_FVEC3 = 1,
        CUSTOM_STRUCT = 2,
        CUSTOM_GAMEOBJECT = 3,
        CUSTOM_TUPLE = 4,
    };

    public class DumpKv
    {

    }

    *//*public class PropObjectFormatter<T> : IMessagePackFormatter<T> where T : PropObject
    {
        public T Deserialize(ref MessagePackReader reader, MessagePackSerializerOptions options)
        {
            T pobj = Activator.CreateInstance<T>();
            if (PropSyncSerializer.ParseObjMsgValue(ref reader, pobj))
            {
                return pobj;
            }
            else
            {
                throw new Exception("Deserialize failed!");
            }
        }

        public void Serialize(ref MessagePackWriter writer, T value, MessagePackSerializerOptions options)
        {
            var obj = value as PropObject;
            if (!PropSyncSerializer.DumpObjectToMsg(ref writer, obj, 0))
            {
                throw new Exception("Serialize failed!");
            }
        }*//*

        private static readonly ILogger Logger = InternalLoggerFactory.LoggerFactory.CreateLogger<PropObjectFormatter<T>>();
    }*//*

    public class StringIndex
    {
        public bool IsOpen { get; set; }

        public uint GetIndex(string name)
        {
            return 0;
            //throw new NotImplementedException();
        }

        public string GetStr(uint index)
        {
            return "notImpl";
            //throw new NotImplementedException();
        }
    }
}
*/
