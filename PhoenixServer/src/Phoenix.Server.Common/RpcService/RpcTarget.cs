using Microsoft.Extensions.Logging;
using System.Reflection;
using Phoenix.Server.Utils;

namespace Phoenix.Server.Common
{
    public class RpcTarget
    {

        // Constructor
        public RpcTarget(string uri, RpcNodeSelectStrategy? selector = null, nodeid_t nodeId = 0)
        {
            Uri = uri;
            UriPart = uri.Split('.');
            if (UriPart.Length < 2)
            {
                // No split by dot('.'), that means no dedicated service role target
                UriPart = ["", uri];
            }

            // Role or nodeid
            string roleOrNode = UriPart[0];

            // Dedicated node_id
            if (nodeId != 0 && nodeid_t.TryParse(roleOrNode, out nodeid_t parsedNodeId))
            {
                nodeId = parsedNodeId;
            }
            NodeId = nodeId;

            // Strategy to select node
            if(NodeId > 0)
            {
                Selector = RpcNodeSelectStrategy.DEDICATED_NODE;
            }
            else if (selector != null)
            {
                Selector = selector.Value;
            }
            else
            {
                Selector = RpcNodeSelectStrategy.FIXED_HASH;
            }

            if (selector != null && Selector != selector)
            {
                Logger.LogWarning($"[RPC] param error, exceptSelector={selector}, but selector={Selector}");
            }
        }

        // Get role
        public string GetRole()
        {
            return UriPart[0];
        }

        // Get service name
        public string GetServiceName()
        {
            return UriPart[1];
        }

        // Service role property
        public string ServiceRole => UriPart[0];

        // Service name property
        public string ServiceName => UriPart[1];

        public Task<RpcResponseHeader?> AsyncCall(string funcName, object[] svcArgs, dynamic? hashKey = null, int timeout = 0)
        {
            return RpcManager.Instance.AsyncCall(this, funcName, svcArgs, hashKey, timeout);
        }


        //async def coro_async_call(self, func_name, svc_args, ** kwargs)->rpc_helper.RPCResponseHeader:
        //return await LS.RPCMgr.coro_async_call(self, func_name, svc_args, ** kwargs)

            // ToString override
        public override string ToString()
        {
            return $"{{hs:{NodeId},uri:{Uri}}}";
        }

        private static ILogger Logger = InternalLoggerFactory.LoggerFactory.CreateLogger<RpcTarget>();

        private static readonly Random Random = Random.Shared;

        public string Uri { get; private set; }
        public string[] UriPart { get; private set; }
        public nodeid_t NodeId { get; set; }
        public RpcNodeSelectStrategy Selector { get; private set; }
    }

    public enum RpcNodeSelectStrategy
    {
        FIXED_HASH = 2,    // fixed hash, for stateful service
        CONSISTENT_HASH = 3,    // 一致性hash, 节点失效会转移到下一个的节点 用于无状态但有时序要求的服务，状态需要底层保证一致或者逻辑本身能容忍状态不一致.
        DEDICATED_NODE = 4,    // dedicated known node_id
        DYNAMIC_MAILBOX = 5,    // global unique mailbox URL, route by router
        ROLE_BROADCAST = 6,    // Broadcast to all specified role nodes (no callbacks allowed)
        RANDOM_NODE = 7,    // Random node from the list
    }
}
