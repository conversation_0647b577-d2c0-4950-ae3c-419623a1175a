using MessagePack;
using Microsoft.Extensions.Logging;
using MongoDB.Bson.Serialization.Attributes;
using Phoenix.Server.Utils;

namespace Phoenix.Server.Common
{
    public class EntityManager
    {
        public static readonly EntityManager Instance = new EntityManager();

        private EntityManager() { }

        public void AddEntity(Entity entity)
        {
            if (Entities.TryGetValue(entity.EntityId, out var oldEntity))
            {
                if (oldEntity == entity)
                {
                    return;
                }
                else
                {
                    DelEntity(oldEntity);
                    Logger.LogError("Entity already exists remove old eid={eid}", entity.EntityId);
                }
            }

            Entities[entity.EntityId] = entity;
            if (entity.UserId > 0)
            {
                Players_ot[entity.UserId] = entity;
            }

            Logger.LogDebug("AddEntity eid={eid} uid={uid}", entity.EntityId, entity.UserId);
        }

        public void DelEntity(Entity entity)
        {
            Entities.Remove(entity.EntityId);
            if (entity.UserId > 0)
            {
                Players_ot.Remove(entity.UserId);
            }
        }

        public Entity? FindPlayer(userid_t userId)
        {
            return Players_ot.GetValueOrDefault(userId);
        }

        public Entity? FindEntity(entity_id_t entityId)
        {
            return Entities.GetValueOrDefault(entityId);
        }

        private static readonly ILogger Logger = InternalLoggerFactory.LoggerFactory.CreateLogger<EntityManager>();

        private Dictionary<userid_t, Entity> Players_ot = new();
        private Dictionary<entity_id_t, Entity> Entities = new();
        private Dictionary<entity_id_t, Entity> Entites_spt = new();
    }



        public enum EntityState
    {
        ENT_ST_PRE_INIT = 0,   // python object instance created, but not init
        ENT_ST_DATA_INITED = 1, // data async load is done
        ENT_ST_INITED = 2,       // component, dependent objects init done
        ENT_ST_DESTROYING = 3,  // destroying, waiting async persist done and other cleanup work
        ENT_ST_DESTROYED = 4,   // destoyred, not allow any gameplay logic work and property modification.
        ENG_ST_RECOVER = 5,    // failover recover status
    }

    public interface IDataSection
    {

    }
/*
    public class BasicComponentServer : Component
    {

        public BasicComponentServer(GameBasePlayer player) : base(player)
        {
            Player = player;
        }

        public void Init(DBPlayerDataBasic dbBasic)
        {
            dbData = dbBasic;
        }

        public override string GetComponentName()
        {
            return "Basic";
        }

        [MosRpcMethodForClient(Name = "Test")]
        public void Test()
        {

        }

        public GameBasePlayer Player { get; private set; }

        public DBPlayerDataBasic dbData { get; set; }
    }

    public class ProBasic
    {
        public string Name { get; set; }

        public int Level { get; set; }
    }


    public class GameBasePlayerData
    {
        //public BasicData BasicData;
    }*/

}
