using Microsoft.Extensions.Logging;
using Phoenix.Server.Utils;

namespace Phoenix.Server.Common
{
    public delegate void ConsistentHashChangeCallback(ConsistentHash consistentHash, Dictionary<slot_index_t, nodeid_t> slotIndex2NodeId);
    public class ConsistentHash
    {
        public ConsistentHash(string name)
        {
            HashName = name;
            HashSlots = Enumerable.Repeat<uint>(0, (int)DefaultSlotsSize).ToList();
            PreviousHashSlots = Enumerable.Repeat<uint>(0, (int)DefaultSlotsSize).ToList();
        }

        public bool AddNode(nodeid_t nodeId)
        {
            if (Permutation.ContainsKey(nodeId))
            {
                Logger.LogWarning("AddNode failed, nodeId is exist, NodeId={NodeId}", nodeId);
                return false;
            }

            PreviousHashSlots.Clear();
            PreviousHashSlots.AddRange(HashSlots);

            NodeList.Add(nodeId);

            // add Permutation[nodeId]
            CalPermutationForOneNode(nodeId);
            // update hash slots
            Populate();

            Logger.LogInformation("[ConsistentHash]AddNodeSuccess, NodeId={NodeId}, Role={Role}, Digest={Digest}", nodeId, HashName, Digest);
            return true;
        }

        public bool DelNode(nodeid_t nodeId)
        {
            if (!Permutation.ContainsKey(nodeId))
            {
                Logger.LogWarning("DelNode failed, nodeId is not exist, NodeId={NodeId}", nodeId);
                return false;
            }

            PreviousHashSlots.Clear();
            PreviousHashSlots.AddRange(HashSlots);

            // remove node from NodeList
            NodeList.Remove(nodeId);
            Permutation.Remove(nodeId);

            Populate();

            Logger.LogInformation("[ConsistentHash]DelNode_success, NodeId={NodeId}, Role={Role}, Digest={Digest} HashSlots={HashSlots}", nodeId, HashName, Digest,
            HashSlots.JoinTopElementsWithSeparator(10));
            return true;
        }

        public bool ContainsNode(nodeid_t nodeId)
        {
            return Permutation.ContainsKey(nodeId);
        }

        public int GetNodeCount()
        {
            return NodeList.Count;
        }

        private static slot_index_t GetSlotIndex(string key, List<nodeid_t> hashSlots)
        {
            hash_idx_t hashKey = MurmurHash.MurmurHash3_32(key, seed);
            return (uint)(hashKey % hashSlots.Count);
        }

        private static slot_index_t GetSlotIndex(long key, List<nodeid_t> hashSlots)
        {
            hash_idx_t hashKey = MurmurHash.MurmurHash3_32(BitConverter.ToString(BitConverter.GetBytes(key)), seed);
            return (uint)(hashKey % hashSlots.Count);
        }

        private const uint seed = 1023;

        private slot_index_t GetSlotIndex(string key)
        {
            return GetSlotIndex(key, HashSlots);
        }

        private slot_index_t GetSlotIndex(long key)
        {
            return GetSlotIndex(key, HashSlots);
        }

        public nodeid_t PickNode(string key)
        {
            return HashSlots[(int)GetSlotIndex(key)];
        }

        public nodeid_t PickNode(long key)
        {
            return HashSlots[(int)GetSlotIndex(key)];
        }

        private slot_index_t GetSlotIndexFromPrevious(string key)
        {
            return GetSlotIndex(key, PreviousHashSlots);
        }

        private slot_index_t GetSlotIndexFromPrevious(long key)
        {
            return GetSlotIndex(key, PreviousHashSlots);
        }

        public nodeid_t PickNodeFromPrevious(string key)
        {
            return PreviousHashSlots[(int)GetSlotIndexFromPrevious(key)];
        }

        public nodeid_t PickNodeFromPrevious(long key)
        {
            return PreviousHashSlots[(int)GetSlotIndexFromPrevious(key)];
        }

        private void Populate()
        {
            Reset();
            if (Permutation.Count != 0)
            {
                Dictionary<nodeid_t, slot_index_t> permutationNext = new();
                foreach (var pair in Permutation)
                {
                    permutationNext.Add(pair.Key, 0);
                }

                int n = 0;
                int slotsSize = HashSlots.Count;

                // according to the order of node list, fill hashSlots from permutation
                while (n < slotsSize)
                {
                    foreach (nodeid_t nodeId in NodeList)
                    {
                        if (!Permutation.ContainsKey(nodeId))
                        {
                            continue;
                        }

                        if (permutationNext[nodeId] >= slotsSize)
                        {
                            continue;
                        }

                        // find empty slots
                        int slotIndex;
                        do
                        {
                            Permutation.TryGetValue(nodeId, out var slotIndexList);
                            slotIndex = (int)slotIndexList![(int)permutationNext[nodeId]];
                            ++permutationNext[nodeId];
                        } while (permutationNext[nodeId] < slotsSize && HashSlots[slotIndex] > 0);

                        // failed
                        if (HashSlots[slotIndex] > 0)
                        {
                            continue;
                        }
                        // success
                        HashSlots[slotIndex] = nodeId;
                        ++n;
                    }
                }
            }
            OnHashSlotsChange();
        }

        private void OnHashSlotsChange()
        {
            UpdateDigest();
            Dictionary<slot_index_t, nodeid_t> diffMap = new();
            for (int i = 0; i < HashSlots.Count; ++i)
            {
                if (PreviousHashSlots[i] != HashSlots[i])
                {
                    diffMap[(slot_index_t)i] = HashSlots[i];
                }
            }

            Logger.LogDebug("OnHashSlotsChange, DiffMapSize={DiffMapSize}, ChashChangeCbsSize={ChashChangeCbsSize}", diffMap.Count, ChashChangeCbs.Count);
            if (diffMap.Count == 0)
            {
                return;
            }

            var newConsistentHashImmutable = CreateConsistentHashImmutable();
            Volatile.Write(ref m_consistentHashImmutable, newConsistentHashImmutable);

            foreach (var cb in ChashChangeCbs)
            {
                var callback = cb.Value;
                callback(this, diffMap);
            }
        }

        private void UpdateDigest()
        {
            Digest = MurmurHash.MurmurHash3_32(SerializeSlotsToString());
        }

        private void Reset()
        {
            for (int i = 0; i < HashSlots.Count; ++i)
            {
                HashSlots[i] = 0;
            }
        }

        private void CalPermutationForOneNode(nodeid_t nodeId)
        {
            int slotsSize = HashSlots.Count;
            Permutation.Add(nodeId, Enumerable.Repeat<uint>(0, slotsSize).ToList());
            slot_index_t offset = (uint)(Hash1(nodeId) % slotsSize);
            slot_index_t skip = (uint)(Hash2(nodeId) % (slotsSize - 1) + 1);
            for (int j = 0; j < slotsSize; j++)
            {
                Permutation[nodeId][j] = (uint)((offset + j * skip) % slotsSize);
            }
        }

        public void RegCallbackOnHashChange(string callbackName, ConsistentHashChangeCallback callback)
        {
            ChashChangeCbs.Add(callbackName, callback);
        }

        public byte[] SerializeSlotsToString()
        {
            var stream = new MemoryStream();
            BinaryWriter writer = new(stream);
            writer.Write((ushort)HashSlots.Count);
            HashSlots.ForEach(writer.Write);
            return stream.ToArray();
        }

        private const int UShortLen = sizeof(ushort);

        public bool ParseSlotsFromString(byte[] buffer, int startIndex, int len)
        {
            PreviousHashSlots.Clear();
            PreviousHashSlots.AddRange(HashSlots);
            HashSlots.Clear();

            BinaryReader reader = new(new MemoryStream(buffer, startIndex, len));
            ushort itemCount = reader.ReadUInt16();
            int count = 0;
            while (reader.BaseStream.Position < buffer.Length)
            {
                HashSlots.Add(reader.ReadUInt32());
                count++;
                if (count == itemCount)
                {
                    break;
                }
            }
            OnHashSlotsChange();

            Logger.LogInformation("ParseSlotsFromString, Role={Role}, Digest={Digest} HashSlots={HashSlots}", HashName, Digest,
                HashSlots.JoinTopElementsWithSeparator(10));
            return true;
        }

        private byte[] SerializeNodeListToString()
        {
            var stream = new MemoryStream();
            BinaryWriter writer = new(stream);
            writer.Write((ushort)NodeList.Count);
            NodeList.ForEach(writer.Write);
            return stream.ToArray();
        }

        private int ParseNodeListFromString(byte[] buffer)
        {
            // parse NodeList
            NodeList.Clear();
            if (buffer.Length < UShortLen)
            {
                return 0;
            }

            BinaryReader reader = new(new MemoryStream(buffer));
            ushort itemCount = reader.ReadUInt16();
            int count = 0;
            while (reader.BaseStream.Position < buffer.Length)
            {
                NodeList.Add(reader.ReadUInt32());
                count++;
                if (count == itemCount)
                {
                    break;
                }
            }

            // Logger.LogTrace("ParseNodeListFromString, NodeList={NodeList}", NodeList.JoinWithSeparator());
            return (int)reader.BaseStream.Position;
        }

        private byte[] SerializePermutationToString()
        {
            var stream = new MemoryStream();
            BinaryWriter writer = new(stream);
            writer.Write((ushort)Permutation.Count);
            foreach (var pair in Permutation)
            {
                writer.Write(pair.Key);
                writer.Write((ushort)pair.Value.Count);
                pair.Value.ForEach(writer.Write);
            }
            return stream.ToArray();
        }

        public int ParsePermutationFromString(byte[] buffer, int startIndex, int len)
        {
            // parse Permutation
            Permutation.Clear();
            if (buffer.Length < UShortLen)
            {
                return 0;
            }

            BinaryReader reader = new(new MemoryStream(buffer, startIndex, len));
            ushort itemCnt = reader.ReadUInt16();
            int idx = 0;
            while (idx < itemCnt)
            {
                nodeid_t nodeId = reader.ReadUInt32();
                uint16_t vecSize = reader.ReadUInt16();
                var slotIndexList = new List<slot_index_t>();
                for (int i = 0; i < vecSize; ++i)
                {
                    slot_index_t slotIndex = reader.ReadUInt32();
                    slotIndexList.Add(slotIndex);
                }
                Permutation.Add(nodeId, slotIndexList);
                idx++;
            }

            /*foreach (var pair in Permutation)
            {
                Logger.LogTrace("ParsePermutationFromString, NodeId={NodeId} Permutation={Permutation}", pair.Key, pair.Value.JoinWithSeparator());
            }*/

            return (int)reader.BaseStream.Position;
        }

        public byte[] SerializeToString()
        {
            var stream = new MemoryStream();
            BinaryWriter writer = new(stream);
            // serialize NodeList
            writer.Write(SerializeNodeListToString());
            // serialize Permutation
            writer.Write(SerializePermutationToString());
            // serialize HashSlots
            writer.Write(SerializeSlotsToString());
            // Logger.LogTrace("SerializeToString, Size={Size}", stream.Position);
            return stream.ToArray();
        }

        public bool ParseFromString(byte[] buffer)
        {
            //Logger.LogTrace("ParseFromString, buffer.size={buffer.size}", buffer.Length);

            int curLen = 0;
            // parse NodeList
            int parseLen = ParseNodeListFromString(buffer);
            if (parseLen == 0)
            {
                Logger.LogError("ParseFromString, ParseNodeListFromString failed");
                return false;
            }

            curLen += parseLen;
            var remainLen = buffer.Length - curLen;
            parseLen = ParsePermutationFromString(buffer, curLen, remainLen);
            if (parseLen == 0)
            {
                Logger.LogError("ParseFromString, ParsePermutationFromString failed");
                return false;
            }
            curLen += parseLen;
            remainLen = buffer.Length - curLen;
            return ParseSlotsFromString(buffer, curLen, remainLen);
        }

        public ConsistentHashImmutable CreateConsistentHashImmutable()
        {
            return new ConsistentHashImmutable(HashSlots);
        }

        public record ConsistentHashImmutable
        {
            public ConsistentHashImmutable(List<nodeid_t> hashSlots)
            {
                HashSlots.AddRange(hashSlots);
            }

            public nodeid_t PickNode(long key)
            {
                return HashSlots[(int)GetSlotIndex(key)];
            }

            private static slot_index_t GetSlotIndex(string key, List<nodeid_t> hashSlots)
            {
                return ConsistentHash.GetSlotIndex(key, hashSlots);
            }

            private static slot_index_t GetSlotIndex(long key, List<nodeid_t> hashSlots)
            {
                return ConsistentHash.GetSlotIndex(key, hashSlots);
            }

            private slot_index_t GetSlotIndex(string key)
            {
                //return ConsistentHash.GetSlotIndex(key, HashSlots);
                return GetSlotIndex(key, HashSlots);
            }

            private slot_index_t GetSlotIndex(long key)
            {
                return GetSlotIndex(key, HashSlots);
            }

            public List<nodeid_t> HashSlots { get; } = new();
        }

        public ConsistentHashImmutable GetConsistentHashImmutable()
        {
            if (m_consistentHashImmutable == null)
            {
                Volatile.Write(ref m_consistentHashImmutable, new ConsistentHashImmutable(HashSlots));
            }
            return Volatile.Read(ref m_consistentHashImmutable);
        }

        // Reference: Maglev: A Fast and Reliable Software Network Load Balancer
        // https://storage.googleapis.com/pub-tools-public-publication-data/pdf/44824.pdf

        // choose SLOT_SIZE to be larger than 100 × N, must be a prime number
        public static ulong DefaultSlotsSize = 5237;

        private static readonly ILogger Logger = InternalLoggerFactory.LoggerFactory.CreateLogger<ConsistentHash>();
        public string HashName { get; private set; }
        readonly List<nodeid_t> HashSlots;
        List<nodeid_t> PreviousHashSlots;
        // digest of hash slots
        public uint Digest { get; private set; }
        // the order of nodes
        readonly List<nodeid_t> NodeList = new();
        // Maglev hashing permutation tables
        readonly Dictionary<nodeid_t, List<slot_index_t>> Permutation = new();
        /* Two different hashing functions to generate 'offset' and 'skip', the permutation table using these numbers as follows:
           offset ← Hash1(name[i]) mod M
           skip ← Hash2(name[i]) mod (M −1) +1
           permutation[i][j] ← (offset+ j×skip) mod M
        */
        readonly Func<slot_index_t, nodeid_t> Hash1 = (nodeId) => (uint)MurmurHash.MurmurHash2_hash64(nodeId);
        readonly Func<slot_index_t, nodeid_t> Hash2 = (nodeId) => (uint)MurmurHash.Hash64(nodeId);
        // callback on consistent hash slots change
        readonly Dictionary<string, ConsistentHashChangeCallback> ChashChangeCbs = new();
        private ConsistentHashImmutable? m_consistentHashImmutable;
    }
}
