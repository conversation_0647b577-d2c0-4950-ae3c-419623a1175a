using Consul;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Server.Common
{
    /// <summary>
    /// 基于Consul的Leader发现服务
    /// 持续监控Leader的变化并通过事件通知
    /// </summary>
    public class ConsulLeaderDiscoveryService : BackgroundService
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="serverOptions">Server配置选项</param>
        /// <param name="clusterOptions">Consul配置选项</param>
        public ConsulLeaderDiscoveryService(ILogger<ConsulLeaderDiscoveryService> logger,
            IOptions<ServerOptions> serverOptions, IOptions<ClusterOptions> clusterOptions)
        {
            m_logger = logger;
            m_serverOptions = serverOptions.Value;
            m_clusterOptions = clusterOptions.Value;
            m_leaderElectionLockKey = ConsulKeyNames.GenLeaderElectionLockKey(m_serverOptions);

            // 创建Consul客户端
            m_consulClient = new ConsulClient(config =>
            {
                config.Address = new Uri(m_clusterOptions.ConsulAddress);
            });
        }

        /// <summary>
        /// 后台服务执行方法
        /// </summary>
        /// <param name="stoppingToken">取消令牌</param>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            m_logger.LogInformation("ConsulLeaderDiscoveryService Starting");

            try
            {
                // 初始化获取当前Leader
                await CheckAndUpdateLeader(stoppingToken);

                // 开始监控Leader变化
                await StartWatchingLeader(stoppingToken);
            }
            catch (Exception ex)
            {
                m_logger.LogError(ex, "Leader discovery error");
            }
        }

        /// <summary>
        /// 检查并更新Leader信息
        /// </summary>
        private async Task CheckAndUpdateLeader(CancellationToken stoppingToken)
        {
            try
            {
                var getResult = await m_consulClient.KV.Get(m_leaderElectionLockKey, stoppingToken);
                if (getResult.Response != null)
                {
                    if (getResult.Response.Value != null)
                    {
                        string leaderValue = Encoding.UTF8.GetString(getResult.Response.Value);
                        var leaderNodeInfo = ConsulKeyNames.ParseLeaderNodeInfo(leaderValue);
                        if (leaderNodeInfo != null)
                        {
                            UpdateCurrentLeader(leaderNodeInfo);
                        }
                    }
                }
                else
                {
                    // 没有Leader
                    if (m_currentLeader != null)
                    {
                        m_logger.LogInformation("Leader lost");
                        m_currentLeader = null;
                        OnLeaderLost?.Invoke(this, EventArgs.Empty);
                    }
                }
            }
            catch (Exception ex)
            {
                m_logger.LogError(ex, "Error checking leader");
            }
        }

        /// <summary>
        /// 开始监控Leader变化
        /// </summary>
        private async Task StartWatchingLeader(CancellationToken stoppingToken)
        {
            // 使用Consul的阻塞查询机制监控变化
            ulong waitIndex = 0;

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // 使用阻塞查询，当值变化时才返回
                    var queryOptions = new QueryOptions
                    {
                        WaitIndex = waitIndex, WaitTime = TimeSpan.FromMinutes(5) // 长轮询超时时间
                    };

                    var getResult = await m_consulClient.KV.Get(m_leaderElectionLockKey, queryOptions, stoppingToken);

                    // 更新索引，用于下次查询
                    waitIndex = getResult.LastIndex;
                    if (getResult.Response != null)
                    {
                        if (getResult.Response.Value != null)
                        {
                            string leaderValue = Encoding.UTF8.GetString(getResult.Response.Value);
                            var newLeaderInfo = ConsulKeyNames.ParseLeaderNodeInfo(leaderValue);
                            if (newLeaderInfo != null)
                            {
                                UpdateCurrentLeader(newLeaderInfo);
                            }
                        }
                    }
                    else
                    {
                        // 没有Leader
                        if (m_currentLeader != null)
                        {
                            m_logger.LogInformation("Leader lost");
                            m_currentLeader = null;
                            OnLeaderLost?.Invoke(this, EventArgs.Empty);
                        }
                    }
                }
                catch (TaskCanceledException)
                {
                    // 预期的异常，忽略
                    m_logger.LogDebug("Leader watch canceled");
                }
                catch (Exception ex)
                {
                    m_logger.LogError(ex, "Error watching leader");

                    // 出错后等待一段时间再重试
                    await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
                }
            }
        }

        /// <summary>
        /// 更新当前Leader信息
        /// </summary>
        private void UpdateCurrentLeader(MsgPack_S_ServerNodeInfo_Proto newLeaderNodeInfo)
        {
            bool isNewLeader = m_currentLeader == null ||
                               m_currentLeader.NodeId != newLeaderNodeInfo.NodeId ||
                               m_currentLeader.Ip != newLeaderNodeInfo.Ip ||
                               m_currentLeader.Port != newLeaderNodeInfo.Port;

            if (isNewLeader)
            {
                m_logger.LogInformation("Leader changed to: NodeInfo={NodeInfo}",
                    $"{newLeaderNodeInfo.NodeId}-{newLeaderNodeInfo.Role}-{newLeaderNodeInfo.Ip}-{newLeaderNodeInfo.Port}");
                m_currentLeader = newLeaderNodeInfo;
                OnLeaderChanged?.Invoke(this, newLeaderNodeInfo);
            }
        }

        /// <summary>
        /// 获取当前Leader信息
        /// </summary>
        public MsgPack_S_ServerNodeInfo_Proto? GetCurrentLeader()
        {
            return m_currentLeader;
        }

        /// <summary>
        /// Leader变化事件
        /// </summary>
        public event EventHandler<MsgPack_S_ServerNodeInfo_Proto>? OnLeaderChanged;

        /// <summary>
        /// Leader丢失事件
        /// </summary>
        public event EventHandler? OnLeaderLost;

        private readonly ILogger<ConsulLeaderDiscoveryService> m_logger;
        private readonly ServerOptions m_serverOptions;
        private readonly ClusterOptions m_clusterOptions;
        private readonly ConsulClient m_consulClient;
        private readonly string m_leaderElectionLockKey;
        private MsgPack_S_ServerNodeInfo_Proto? m_currentLeader;
    }
}
