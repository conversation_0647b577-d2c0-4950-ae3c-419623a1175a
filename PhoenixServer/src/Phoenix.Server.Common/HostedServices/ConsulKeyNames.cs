// Copyright (c) Phoenix All Rights Reserved.

using System.Text.Json;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Server.Common;

internal static class ConsulKeyNames
{
    public static string GenLeaderElectionLockKey(ServerOptions serverOptions)
    {
        return $"{serverOptions.ZoneId}-leader";
    }

    public static string GenLeaderElectionValue(ServerOptions serverOptions)
    {
        MsgPack_S_ServerNodeInfo_Proto serverNodeInfo = new()
        {
            NodeId = serverOptions.NodeId,
            Role = serverOptions.Role,
            Ip = serverOptions.Ip,
            Port = serverOptions.Port,
            Tag = serverOptions.Tag,
            IsReady = false
        };
        return JsonSerializer.Serialize(serverNodeInfo);
    }

    public static MsgPack_S_ServerNodeInfo_Proto? ParseLeaderNodeInfo(string leaderValue)
    {
        MsgPack_S_ServerNodeInfo_Proto? leaderNodeInfo = JsonSerializer.Deserialize<MsgPack_S_ServerNodeInfo_Proto>(leaderValue);
        return leaderNodeInfo;
    }

    public static string GenServiceKey(ServerOptions serverOptions)
    {
        return $"service:{serverOptions.ZoneId}-{serverOptions.NodeId.ToString()}";
    }

    public static string GenServiceName(ServerOptions serverOptions)
    {
        return $"{serverOptions.ZoneId}-{serverOptions.Name}";
    }

    public static string GenCheckId(ServerOptions serverOptions)
    {
        return $"service:{GenServiceName(serverOptions)}";
    }

    public static string GenSessionName(ServerOptions serverOptions)
    {
        return $"{serverOptions.ZoneId}-{serverOptions.Role}-{serverOptions.NodeId}";
    }
}
