// Copyright (c) Phoenix.  All Rights Reserved.

using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Phoenix.Server.Common;

/// <summary>
/// server status service interface provider server status and server weight
/// </summary>
public interface IServerStatusHostedService : IHostedService
{
    /// <summary>
    /// get server weight
    /// </summary>
    /// <returns></returns>
    int GetWeight();
}

/// <summary>
/// server status service provider server status and server weight
/// </summary>
public abstract class ServerStatusHostedService : IServerStatusHostedService
{
    protected ServerStatusHostedService(ILogger<ServerStatusHostedService> logger, int statPeriod)
    {
        m_logger = logger;
        m_statPeriod = statPeriod;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        m_timer = new Timer(TimerCallback, null, TimeSpan.Zero, TimeSpan.FromSeconds(m_statPeriod));
        m_logger.LogInformation("ServerStatusHostedService started.");
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        m_timer.Dispose();
        m_logger.LogInformation("ServerStatusHostedService stopped.");
        return Task.CompletedTask;

    }

    public int GetWeight()
    {
        lock (m_lock)
        {
            return m_weight;
        }
    }

    private void TimerCallback(object? state)
    {
        lock (m_lock)
        {
            CalcWeight();
            m_logger.LogDebug("ServerStatusHostedService weight={weight}", m_weight);
        }
    }

    protected abstract void CalcWeight();

    /// <summary>
    ///     Logger for logging information.
    /// </summary>
    private readonly ILogger m_logger;
    private Timer m_timer = default!;
    private readonly int m_statPeriod;
    private readonly object m_lock = new();
    protected int m_weight = ServerStatusConstants.ServerWeightMax;
}
