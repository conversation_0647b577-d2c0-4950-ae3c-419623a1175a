using System.Text;
using System.Text.Json;
using Consul;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Server.Common
{
    /// <summary>
    /// 基于Consul的Leader选举服务
    /// </summary>
    public class ConsulLeaderElectionService : BackgroundService
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="serverOptions">Server配置选项</param>
        /// <param name="clusterOptions">Consul配置选项</param>
        public ConsulLeaderElectionService(ILogger<ConsulLeaderElectionService> logger,
            IOptions<ServerOptions> serverOptions, IOptions<ClusterOptions> clusterOptions)
        {
            s_logger = logger;
            m_serverOptions = serverOptions.Value;
            m_clusterOptions = clusterOptions.Value;
            m_serviceKey = ConsulKeyNames.GenServiceKey(serverOptions.Value);
            m_leaderElectionLockKey = ConsulKeyNames.GenLeaderElectionLockKey(serverOptions.Value);
            m_leaderElectionValue = ConsulKeyNames.GenLeaderElectionValue(serverOptions.Value);

            // 创建Consul客户端
            m_consulClient = new ConsulClient(config =>
            {
                config.Address = new Uri(m_clusterOptions.ConsulAddress);
            });
        }

        /// <summary>
        /// 后台服务执行方法
        /// </summary>
        /// <param name="stoppingToken">取消令牌</param>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            s_logger.LogInformation("ConsulLeaderElectionService Starting");

            try
            {
                // 注册服务
                await RegisterService();

                // 创建Session
                await CreateSession();

                // 开始Leader选举
                await StartLeaderElection(stoppingToken);
            }
            catch (Exception ex)
            {
                s_logger.LogError(ex, "Leader elect error");
            }
        }

        /// <summary>
        /// 注册服务到Consul
        /// </summary>
        private async Task RegisterService()
        {
            // 获取当前NodeId，用于健康检查
            string checkId = ConsulKeyNames.GenCheckId(m_serverOptions);

            // 创建服务注册信息
            var registration = new AgentServiceRegistration
            {
                ID = $"{m_serverOptions.ZoneId}-{m_serverOptions.NodeId.ToString()}",
                Name = ConsulKeyNames.GenServiceName(m_serverOptions),
                Tags = [m_serverOptions.Tag],
                // 添加健康检查
                Check = new AgentServiceCheck
                {
                    // TTL检查：服务必须定期调用健康检查接口
                    TTL = TimeSpan.FromSeconds(15), // 15秒TTL
                    DeregisterCriticalServiceAfter = TimeSpan.FromMinutes(1), // 1分钟后自动注销不健康的服务
                    Status = HealthStatus.Passing,
                    CheckID = checkId
                },
                Meta = new Dictionary<string, string>()
            };

            await m_consulClient.Agent.ServiceRegister(registration);
            s_logger.LogInformation("Service already register on Consul: NodeId={NodeId}", m_serverOptions.NodeId);

            // 启动健康检查心跳
            _ = StartHealthCheckHeartbeat(checkId);
        }

        /// <summary>
        /// 启动健康检查心跳
        /// </summary>
        /// <param name="checkId">健康检查ID</param>
        private async Task StartHealthCheckHeartbeat(string checkId)
        {
            while (!m_leadershipCts.IsCancellationRequested)
            {
                try
                {
                    // 向Consul报告服务健康状态
                    await m_consulClient.Agent.PassTTL(checkId, "Server is running");
                    // s_logger.LogTrace("Already send HealthCheckHeartbeat: CheckId={CheckId}", checkId);
                }
                catch (Exception ex)
                {
                    s_logger.LogError(ex, "send HealthCheckHeartbeat fail: CheckId={CheckId}", checkId);
                }

                // 等待一段时间后再次发送心跳（TTL的2/3是一个好的经验值）
                await Task.Delay(TimeSpan.FromSeconds(10), m_leadershipCts.Token);
            }
        }

        /// <summary>
        /// 创建Session
        /// </summary>
        private async Task CreateSession()
        {
            var sessionEntry = new SessionEntry
            {
                Name = ConsulKeyNames.GenSessionName(m_serverOptions),
                TTL = TimeSpan.FromSeconds(m_clusterOptions.ConsulSessionTTL),
                // 使用Release行为，确保Session过期时自动释放锁
                Behavior = SessionBehavior.Release,
                // 将LockDelay设置为较小的值，以便在服务崩溃后快速重新选为Leader
                LockDelay = TimeSpan.FromSeconds(5),
                // 将Session与健康检查关联，当健康检查失败时自动使得Session无效
                Checks = { ConsulKeyNames.GenCheckId(m_serverOptions) }
            };

            var sessionResponse = await m_consulClient.Session.Create(sessionEntry);
            m_sessionId = sessionResponse.Response;
            s_logger.LogInformation("Already Create Consul Session SessionId={SessionId}", m_sessionId);

            // 启动Session续约任务
            _ = RenewSession();
        }

        /// <summary>
        /// 定期续约Session
        /// </summary>
        private async Task RenewSession()
        {
            // 计算续约间隔（通常是TTL的一半）
            var ttl = TimeSpan.FromSeconds(m_clusterOptions.ConsulSessionTTL);
            var renewInterval = TimeSpan.FromSeconds(ttl.TotalSeconds / 2);

            while (!m_leadershipCts.IsCancellationRequested)
            {
                try
                {
                    await m_consulClient.Session.Renew(m_sessionId);
                    // s_logger.LogTrace("Already Renew Session SessionId={SessionId}", m_sessionId);
                }
                catch (Exception ex)
                {
                    s_logger.LogError(ex, "Renew Session fail SessionId={SessionId}", m_sessionId);

                    // 如果续约失败，尝试重新创建Session
                    try
                    {
                        var sessionEntry = new SessionEntry
                        {
                            Name = ConsulKeyNames.GenSessionName(m_serverOptions),
                            TTL = ttl,
                            Behavior = SessionBehavior.Release,
                            LockDelay = TimeSpan.Zero,
                            Checks = { ConsulKeyNames.GenCheckId(m_serverOptions) }
                        };

                        var sessionResponse = await m_consulClient.Session.Create(sessionEntry);
                        m_sessionId = sessionResponse.Response;
                        s_logger.LogInformation("Already ReCreate Consul Session: {SessionId}", m_sessionId);
                    }
                    catch (Exception createEx)
                    {
                        s_logger.LogError(createEx, "ReCreate Session fail");
                    }
                }

                await Task.Delay(renewInterval);
            }
        }

        /// <summary>
        /// 开始Leader选举
        /// </summary>
        private async Task StartLeaderElection(CancellationToken stoppingToken)
        {
            m_leadershipTask = Task.Run(async () =>
            {
                while (!stoppingToken.IsCancellationRequested && !m_leadershipCts.IsCancellationRequested)
                {
                    try
                    {
                        // 尝试获取锁
                        var acquireResult = await m_consulClient.KV.Acquire(
                            new KVPair(m_leaderElectionLockKey)
                            {
                                Session = m_sessionId, Value = Encoding.UTF8.GetBytes(m_leaderElectionValue)
                            }, stoppingToken);

                        if (acquireResult.Response)
                        {
                            if (!m_isLeader)
                            {
                                m_isLeader = true;
                                s_logger.LogInformation("BecomeLeader: NodeId={NodeId}", m_serverOptions.NodeId);
                                OnBecomeLeader?.Invoke(this, EventArgs.Empty);
                            }
                        }
                        else
                        {
                            if (m_isLeader)
                            {
                                m_isLeader = false;
                                s_logger.LogInformation("OnBecomeFollower: NodeId={NodeId}", m_serverOptions.NodeId);
                                OnBecomeFollower?.Invoke(this, EventArgs.Empty);
                            }

                            // 如果不是Leader，检查当前的Leader是谁
                            var getResult = await m_consulClient.KV.Get(m_leaderElectionLockKey,
                                stoppingToken);
                            if (getResult.Response != null)
                            {
                                string currentLeader = Encoding.UTF8.GetString(getResult.Response.Value);
                                MsgPack_S_ServerNodeInfo_Proto? leaderNodeInfo = ConsulKeyNames.ParseLeaderNodeInfo(currentLeader);
                                // s_logger.LogTrace("Current Leader is : NodeId:{NodeId}", leaderNodeInfo.NodeId);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        m_isLeader = false;
                        s_logger.LogError(ex, "LeaderElect error");
                        OnBecomeFollower?.Invoke(this, EventArgs.Empty);
                    }

                    // 等待一段时间后再次尝试
                    await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
                }
            }, stoppingToken);

            await m_leadershipTask;
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            s_logger.LogInformation("ConsulLeaderElectionService Stoping");

            // 取消Leader选举任务
            await m_leadershipCts.CancelAsync();

            if (m_leadershipTask != null)
            {
                try
                {
                    await m_leadershipTask;
                }
                catch (OperationCanceledException)
                {
                    // 预期的异常，忽略
                }
            }

            // 如果是Leader，释放锁
            if (m_isLeader)
            {
                try
                {
                    await m_consulClient.KV.Release(new KVPair(m_leaderElectionLockKey) { Session = m_sessionId },
                        cancellationToken);
                    s_logger.LogInformation("Release LeaderLock");
                }
                catch (Exception ex)
                {
                    s_logger.LogError(ex, "Release LeaderLock fail");
                }
            }

            // 销毁Session
            try
            {
                await m_consulClient.Session.Destroy(m_sessionId, cancellationToken);
                s_logger.LogInformation(" DestroySession: {SessionId}", m_sessionId);
            }
            catch (Exception ex)
            {
                s_logger.LogError(ex, "Destroy Session fail: {SessionId}", m_sessionId);
            }

            // 注销服务
            try
            {
                await m_consulClient.Agent.ServiceDeregister(m_serviceKey, cancellationToken);
                s_logger.LogInformation("Already Deregister service: NodeId={NodeId}", m_serverOptions.NodeId);
            }
            catch (Exception ex)
            {
                s_logger.LogError(ex, "Deregister service fail: NodeId={NodeId}", m_serverOptions.NodeId);
            }

            await base.StopAsync(cancellationToken);
        }

        /// <summary>
        /// 当前服务是否是Leader
        /// </summary>
        public bool IsLeader => m_isLeader;

        /// <summary>
        /// 当服务成为Leader时触发的事件
        /// </summary>
        public event EventHandler? OnBecomeLeader;

        /// <summary>
        /// 当服务失去Leader地位时触发的事件
        /// </summary>
        public event EventHandler? OnBecomeFollower;

        private static ILogger<ConsulLeaderElectionService> s_logger = null!;
        private readonly ClusterOptions m_clusterOptions;
        private readonly ServerOptions m_serverOptions;
        private readonly ConsulClient m_consulClient;
        private string m_sessionId = string.Empty;
        private bool m_isLeader;
        private readonly CancellationTokenSource m_leadershipCts = new();
        private Task? m_leadershipTask;
        private readonly string m_serviceKey;
        private readonly string m_leaderElectionLockKey;
        private readonly string m_leaderElectionValue;
    }
}
