// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.Common;

/// <summary>
/// EmptyServerStatusHostedService provider default status implementation
/// </summary>
public class EmptyServerStatusHostedService : IServerStatusHostedService
{
    public Task StartAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    public int GetWeight()
    {
        return 10000;
    }
}
