// Copyright (c) Phoenix All Rights Reserved.

using System.Diagnostics;
using System.Runtime.InteropServices;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Phoenix.Server.Common;

/// <summary>
/// 进程标识服务
/// 负责设置进程的标识信息，如控制台标题（Windows）或进程名（Linux）
/// </summary>
public class ProcessIdentityService : IHostedService
{
    private readonly ILogger<ProcessIdentityService> m_logger;
    private readonly ServerOptions m_serverOptions;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="serverOptions">服务器配置选项</param>
    public ProcessIdentityService(ILogger<ProcessIdentityService> logger, IOptions<ServerOptions> serverOptions)
    {
        m_logger = logger;
        m_serverOptions = serverOptions.Value;
    }

    /// <summary>
    /// 启动服务时设置进程标识
    /// </summary>
    public Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            SetProcessIdentity();
        }
        catch (Exception ex)
        {
            m_logger.LogError(ex, "Set ConsoleTile error");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// 设置进程标识
    /// </summary>
    private void SetProcessIdentity()
    {
        int processId = Process.GetCurrentProcess().Id;

        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            // Windows平台：设置控制台窗口标题，包含Role、NodeId和进程Id
            string title = $"{m_serverOptions.Role}-{m_serverOptions.NodeId} (PID: {processId})";
            Console.Title = title;
        }
    }
}
