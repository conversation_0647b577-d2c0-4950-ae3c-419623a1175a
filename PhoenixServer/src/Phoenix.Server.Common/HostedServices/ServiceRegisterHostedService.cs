/*
// Copyright (c) Phoenix.  All Rights Reserved.

using System.Text.Json;
using dotnet_etcd;
using dotnet_etcd.interfaces;
using Etcdserverpb;
using Google.Protobuf;
using Grpc.Core;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Phoenix.Server.Common;

/// <summary>
///     Etcd service registration.
///     According to the etcd v3 API, when a service is started, we write the service information into etcd to register the service.
///     At the same time, we bind the lease and check whether the service is running normally by keeping the lease alive, thereby achieving health checks.
/// </summary>
public sealed class ServiceRegisterHostedService : IHostedLifecycleService
{
    /// <summary>
    ///     Initializes a new instance of the <see cref="ServiceRegisterHostedService"/> class.
    /// </summary>
    /// <param name="logger">The logger.</param>
    /// <param name="serviceInfoOptions">The service information options.</param>
    /// <param name="etcdOptions">The etcd options.</param>
    /// <param name="serverStatusHostedService">the server status service</param>
    public ServiceRegisterHostedService(ILogger<ServiceRegisterHostedService> logger,
        IOptions<ServerOptions> serviceInfoOptions, IOptions<EtcdOptions> etcdOptions, IServerStatusHostedService serverStatusHostedService)
    {
        m_logger = logger;

        string etcdConnection = etcdOptions.Value.ConnectionString;
        m_logger.LogInformation("creating etcd client, connection={connection}", etcdConnection);
        m_etcdClient = new EtcdClient(etcdOptions.Value.ConnectionString, configureChannelOptions: (options =>
        {
            options.Credentials = ChannelCredentials.Insecure;
        }));

        m_serviceKey = EtcdKeyHelper.GetEtcdKey(etcdOptions.Value.ChannelName, serviceInfoOptions.Value.Name);

        m_serverStatusHostedService = serverStatusHostedService;
        m_serverOptions = serviceInfoOptions.Value;
        m_serverOptions.Weight = serverStatusHostedService.GetWeight();
        GenerateServiceValue();
    }

    public Task StartingAsync(CancellationToken cancellationToken) => Task.CompletedTask;

    /// <summary>
    ///     Starts the service registration task when the application is stared.
    /// </summary>
    /// <param name="cancellationToken">Propagates notification that operations should be canceled.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    public Task StartedAsync(CancellationToken cancellationToken)
    {
        Task.Factory.StartNew(async () =>
            {
                m_logger.LogInformation("Service Register task is starting.");

                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        if (m_leaseId == null)
                        {
                            m_leaseId = await LeaseGrant(cancellationToken);
                        }
                        else
                        {
                            await LeaseKeepAlive(cancellationToken);
                        }

                        await Task.Delay(s_leaseKeepAliveInterval, cancellationToken);
                    }
                    catch (TaskCanceledException)
                    {
                        m_logger.LogDebug("Service Register task is canceled.");
                    }
                    catch (Exception ex)
                    {
                        m_leaseId = null;
                        m_logger.LogError(ex, "An error occurred during Service Register task,register again.");
                    }
                }

                m_logger.LogInformation("Service Register task is stopped.");
            }, cancellationToken,
            TaskCreationOptions.LongRunning,
            TaskScheduler.Default);

        return Task.CompletedTask;
    }

    /// <summary>
    ///     Stops the service registration.
    /// </summary>
    /// <param name="cancellationToken">Propagates notification that operations should be canceled.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    public async Task StoppingAsync(CancellationToken cancellationToken)
    {
        m_logger.LogInformation("Service Register is stopping.");

        if (m_leaseId != null)
        {
            await m_etcdClient.LeaseRevokeAsync(new LeaseRevokeRequest { ID = m_leaseId.Value },
                cancellationToken: cancellationToken);
            m_leaseId = null;
        }
    }

    public Task StoppedAsync(CancellationToken cancellationToken) => Task.CompletedTask;

    public Task StartAsync(CancellationToken cancellationToken) => Task.CompletedTask;

    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;

    /// <summary>
    ///     Keeps the lease alive.
    /// </summary>
    /// <param name="stoppingToken">Propagates notification that operations should be canceled.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    private Task LeaseKeepAlive(CancellationToken stoppingToken)
    {
        long leaseId = m_leaseId!.Value;
        m_logger.LogDebug("lease keep alive starting,Id={id}", leaseId);

        SyncServiceValue(leaseId, stoppingToken);

        return m_etcdClient.LeaseKeepAlive(new LeaseKeepAliveRequest { ID = leaseId },
            response =>
            {
                if (response.TTL == 0L)
                {
                    m_logger.LogWarning("lease keep alive response TTL is zero register again,Id={id}", leaseId);
                    m_leaseId = null;
                }
            }, stoppingToken);
    }

    /// <summary>
    ///     Grants a lease and puts the service information into the etcd store.
    /// </summary>
    /// <param name="stoppingToken">Propagates notification that operations should be canceled.</param>
    /// <returns>The ID of the granted lease.</returns>
    private async Task<long> LeaseGrant(CancellationToken stoppingToken)
    {
        m_logger.LogDebug("lease grant is starting");

        LeaseGrantResponse response = await m_etcdClient.LeaseGrantAsync(
            new LeaseGrantRequest { TTL = LeaseTTL },
            cancellationToken: stoppingToken);
        long leaseId = response.ID;

        await SyncServiceValue(leaseId, stoppingToken);

        m_logger.LogDebug("lease grant and put is ok,leaseId={id} key={key} value={value}", response.ID, m_serviceKey,
            m_serviceValue);
        return response.ID;
    }

    private Task SyncServiceValue(long leaseId, CancellationToken stoppingToken)
    {
        GenerateServiceValue();
        return m_etcdClient.PutAsync(
            new PutRequest
            {
                Key = ByteString.CopyFromUtf8(m_serviceKey),
                Value = ByteString.CopyFromUtf8(m_serviceValue),
                Lease = leaseId
            }, cancellationToken: stoppingToken);
    }

    private void GenerateServiceValue()
    {
        m_serverOptions.Weight = m_serverStatusHostedService.GetWeight();
        m_serviceValue = JsonSerializer.Serialize(m_serverOptions);
    }

    private readonly IServerStatusHostedService m_serverStatusHostedService;

    /// <summary>
    ///     The time-to-live (TTL) value for the lease.
    /// </summary>
    private const long LeaseTTL = 20;

    /// <summary>
    ///     The interval at which to keep the lease alive.
    /// </summary>
    private static readonly TimeSpan s_leaseKeepAliveInterval = TimeSpan.FromSeconds(5);

    /// <summary>
    ///     The etcd client used to interact with the etcd store.
    /// </summary>
    private readonly IEtcdClient m_etcdClient;

    /// <summary>
    ///     The logger used to log messages.
    /// </summary>
    private readonly ILogger<ServiceRegisterHostedService> m_logger;

    /// <summary>
    ///     The key of the service in the etcd store.
    /// </summary>
    private readonly string m_serviceKey;

    /// <summary>
    ///     the server options
    /// </summary>
    private readonly ServerOptions m_serverOptions;

    /// <summary>
    ///     The value of the service in the etcd store.
    /// </summary>
    private string m_serviceValue = null!;

    /// <summary>
    ///     The ID of the lease.
    /// </summary>
    private long? m_leaseId;
}
*/
