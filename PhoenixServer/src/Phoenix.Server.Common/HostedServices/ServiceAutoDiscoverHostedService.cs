/*
using System.Collections.Concurrent;
using System.Text.Json;
using dotnet_etcd;
using dotnet_etcd.interfaces;
using Etcdserverpb;
using Google.Protobuf;
using Grpc.Core;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Mvccpb;
using Phoenix.ProjectSS.Utilities;

namespace Phoenix.Server.Common;

/// <summary>
///     Etcd service discovery.
/// </summary>
public abstract class ServiceAutoDiscoverHostedService : BackgroundService, IRPCServiceProvider
{
    /// <summary>
    ///     Constructor for the ServiceAutoDiscoverHostedService class.
    /// </summary>
    /// <param name="logger">Logger instance for logging.</param>
    /// <param name="etcdOptions">Etcd options for the service.</param>
    protected ServiceAutoDiscoverHostedService(ILogger logger, IOptions<EtcdOptions> etcdOptions)
    {
        m_logger = logger;

        string connection = etcdOptions.Value.ConnectionString;
        m_etcdClient = new EtcdClient(connection, configureChannelOptions: (options =>
        {
            options.Credentials = ChannelCredentials.Insecure;
        }));
        m_logger.LogInformation("create etcd client ok,connection={connection}", connection);

        string etcdChannelName = etcdOptions.Value.ChannelName;
        m_etcdKeyPrefix = EtcdKeyHelper.GetEtcdKeyPrefix(etcdChannelName);
        m_logger.LogInformation("init etcd watch key,key={key}", m_etcdKeyPrefix);
    }

    /// <summary>
    ///     Get service by service name.
    /// </summary>
    /// <param name="serviceName">The name of the service.</param>
    /// <returns>The service corresponding to the provided name.</returns>
    public IRPCService? GetServiceByServiceName(string serviceName)
    {
        m_services.TryGetValue(serviceName, out IRPCService? service);
        return service;
    }

    /// <summary>
    ///     Get random service by service type.
    /// </summary>
    /// <param name="serviceType">The type of the service.</param>
    /// <returns>A random service of the provided type.</returns>
    public IRPCService GetRandomServiceByServiceType(string serviceType)
    {
        KeyValuePair<string, IRPCService> item =
            m_services.RandomItemFromSequenceConditional(kv => kv.Value.Server.Role == serviceType);
        return item.Value;
    }

    /// <summary>
    ///     Retrieves a random service with weight of a specific type.
    /// </summary>
    /// <param name="serviceType">The type of the service to retrieve.</param>
    /// <returns>A random service of the specified type, or null if no such service exists.</returns>
    public IRPCService? GetRandomServiceByServiceTypeWithWeight(string serviceType)
    {
        var tuples = new List<Tuple<IRPCService, int>>();
        foreach (IRPCService rpcService in m_services.Values)
        {
            tuples.Add(new Tuple<IRPCService, int>(rpcService, rpcService.Server.Weight));
        }

        return tuples.RandomWeightedItemFromSequence();
    }

    /// <summary>
    ///     Start the service discovery.
    /// </summary>
    /// <param name="cancellationToken">Propagates notification that operations should be canceled.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    public override async Task StartAsync(CancellationToken cancellationToken)
    {
        // Request a range of keys from the etcd server, with a deadline of 10 seconds from now.
        RangeResponse? response = await m_etcdClient.GetRangeAsync(m_etcdKeyPrefix,
            cancellationToken: cancellationToken,
            deadline: DateTime.UtcNow.AddSeconds(10));

        // Loop through each KeyValue pair in the response.
        foreach (KeyValue kv in response.Kvs)
        {
            // Convert the key and value from bytes to UTF8 strings.
            string key = kv.Key.ToStringUtf8();
            string value = kv.Value.ToStringUtf8();

            try
            {
                // Attempt to parse the service name from the key.
                string? name = EtcdKeyHelper.TryParseServiceNameFromEtcdKey(key);
                if (name == null)
                {
                    // Phoenix.Server.Log a warning if the service name could not be parsed from the key.
                    m_logger.LogWarning("add service failed,name format error,serviceName={name}", name);
                    continue;
                }

                // Add the service to the services dictionary, deserializing the value into a ServerOptions object.
                m_services.TryAdd(name,
                    new RPCServiceEntry(JsonSerializer.Deserialize<ServerOptions>(value, m_jsonSerializeOptions)!));
                m_logger.LogDebug("initial service ok,serviceName={name} key={key} value={value}", name, key, value);
            }
            catch (Exception e)
            {
                // Phoenix.Server.Log a warning if an exception occurred while adding the service.
                m_logger.LogWarning(e, "initial service failed,key={key} value={value}", key, value);
            }
        }

        // Set the etcd watch revision to one more than the revision of the last change to the etcd store.
        m_etcdWatchRevision = response.Header.Revision + 1;

        // Call the base class's StartAsync method.
        await base.StartAsync(cancellationToken);
    }

    /// <summary>
    ///     Execute the service discovery.
    /// </summary>
    /// <param name="stoppingToken">Propagates notification that operations should be canceled.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // Continue executing until a cancellation is requested.
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // Phoenix.Server.Log the start of the watch operation.
                m_logger.LogDebug("start watch etcd key={key} startRevision={revision}", m_etcdKeyPrefix,
                    m_etcdWatchRevision);

                // Create a new watch request for the etcd key prefix.
                var watchRequest = new WatchRequest
                {
                    CreateRequest = new WatchCreateRequest
                    {
                        Key = EtcdClient.GetStringByteForRangeRequests(m_etcdKeyPrefix),
                        RangeEnd = ByteString.CopyFromUtf8(EtcdClient.GetRangeEnd(m_etcdKeyPrefix)),
                        ProgressNotify = true,
                        StartRevision = m_etcdWatchRevision
                    }
                };

                // Start watching the etcd store for changes.
                await m_etcdClient.WatchAsync(watchRequest, OnWatchResponse, cancellationToken: stoppingToken);
            }
            catch (TaskCanceledException)
            {
                // Phoenix.Server.Log a debug message if the watch operation was cancelled.
                m_logger.LogDebug("service register is canceled.");
            }
            catch (RpcException e)
            {
                if (e.StatusCode == StatusCode.Cancelled)
                {
                    // Phoenix.Server.Log a debug message if the watch operation was cancelled.
                    m_logger.LogDebug("service register is canceled.");
                }
                else
                {
                    // Re-throw the exception if it was not a cancellation.
                    throw;
                }
            }
            catch (Exception e)
            {
                // Phoenix.Server.Log an error message if an exception occurred during the watch operation.
                m_logger.LogError(e, "watch failed, will be try next time.");

                // Delay for 5 seconds before trying again.
                await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
            }
        }
    }

    /// <summary>
    ///     Handle the watch response.
    /// </summary>
    /// <param name="response">The watch response.</param>
    private void OnWatchResponse(WatchResponse response)
    {
        // Phoenix.Server.Log a debug message indicating that a watch response was received.
        m_logger.LogDebug("watch key is entered,revision={revision}", response.Header.Revision);

        // Update the etcd watch revision to the revision of the last change to the etcd store.
        m_etcdWatchRevision = response.Header.Revision;

        // Loop through each event in the response.
        foreach (Event responseEvent in response.Events)
        {
            // Convert the key and value from bytes to UTF8 strings.
            string key = responseEvent.Kv.Key.ToStringUtf8();
            string value = responseEvent.Kv.Value.ToStringUtf8();

            // Phoenix.Server.Log a debug message indicating the type of event, and the key and value.
            m_logger.LogDebug("watch event,type={type} key={key} value={value}", responseEvent.Type, key, value);

            // Attempt to parse the service name from the key.
            string? name = EtcdKeyHelper.TryParseServiceNameFromEtcdKey(key);
            if (name == null)
            {
                // Phoenix.Server.Log an error message if the service name could not be parsed from the key.
                m_logger.LogError("parse service name failed");
                continue;
            }

            // Handle the event based on its type.
            if (responseEvent.Type == Event.Types.EventType.Delete)
            {
                // Remove the service from the services dictionary.
                m_services.Remove(name, out _);
                if(EventOnServiceDelete != null)
                {
                    EventOnServiceDelete.Invoke(name);
                }
                m_logger.LogDebug("service is deleted, name={name}", name);
            }
            else if (responseEvent.Type == Event.Types.EventType.Put)
            {
                // Add or update the service in the services dictionary, deserializing the value into a ServerOptions object.
                ServerOptions server = JsonSerializer.Deserialize<ServerOptions>(value, m_jsonSerializeOptions)!;
                m_services.AddOrUpdate(name, new RPCServiceEntry(server), (_, _) => new RPCServiceEntry(server));

                // Phoenix.Server.Log a debug message indicating that a service was added or updated.
                m_logger.LogDebug("service is add or update,serviceInfo={@service}", server);
            }
        }
    }

    public event Action<string>? EventOnServiceDelete = null;

    /// <summary>
    ///     Etcd client for interaction with Etcd server.
    /// </summary>
    private readonly IEtcdClient m_etcdClient;

    /// <summary>
    ///     Prefix for keys in Etcd.
    /// </summary>
    private readonly string m_etcdKeyPrefix;

    /// <summary>
    ///     Options for JSON serialization.
    /// </summary>
    private readonly JsonSerializerOptions m_jsonSerializeOptions =
        new() { Converters = { new ServerOptionsJsonConverter() } };

    /// <summary>
    ///     Logger for logging information.
    /// </summary>
    private readonly ILogger m_logger;

    /// <summary>
    ///     Dictionary of services.
    /// </summary>
    private readonly ConcurrentDictionary<string, IRPCService> m_services = new();

    /// <summary>
    ///     Revision of the watch on Etcd.
    /// </summary>
    private long m_etcdWatchRevision;
}
*/
