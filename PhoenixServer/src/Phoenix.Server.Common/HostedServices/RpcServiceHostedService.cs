/*
// Copyright (c) Phoenix.  All Rights Reserved.

using Grpc.Core;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Phoenix.Server.Common;

/// <summary>
///     Base class for gRPC service hosted services.
/// </summary>
public sealed class RpcServiceHostedService : IHostedService
{
    // /// <summary>
    // ///     Abstract method to register services. Must be implemented in derived classes.
    // /// </summary>
    // /// <param name="serviceCollection">Collection of services to register.</param>
    // protected abstract void RegisterServices(Grpc.Core.Server.ServiceDefinitionCollection serviceCollection);

    /// <summary>
    ///     Channel options for the gRPC server.
    /// </summary>
    private readonly List<ChannelOption> m_channelOptions = new()
    {
        /*
         * GRPC_ARG_HTTP2_MAX_PING_STRIKES
         * This arg controls the maximum number of bad pings that the server will tolerate before sending an HTTP2
         * GOAWAY frame and closing the transport. Setting it to 0 allows the server to accept any number of bad pings.
         #1#
        new ChannelOption("grpc.http2.max_ping_strikes", 0),
        /* Is it permissible to send keepalive pings from the client without any
           outstanding streams. Int valued, 0(false)/1(true).
         #1#
        new ChannelOption("grpc.keepalive_permit_without_calls", 1),
        new ChannelOption("grpc.dns_enable_cares_query", 1),
        new ChannelOption("grpc.dns_ares_query_timeout", 10)
    };

    /// <summary>
    ///     Logger for logging.
    /// </summary>
    private readonly ILogger m_logger;

    /// <summary>
    ///     Server options for the gRPC service.
    /// </summary>
    private readonly ServerOptions m_serverOptions;


    private readonly IEnumerable<ServerServiceDefinition> m_serviceDefinitions;

    /// <summary>
    ///     The gRPC server.
    /// </summary>
    private Grpc.Core.Server? m_rpcServer;

    /// <summary>
    ///     Constructor for the RpcServiceHostedService class.
    /// </summary>
    /// <param name="logger">Logger instance for logging.</param>
    /// <param name="serviceInfoOptions">Server options for the service.</param>
    public RpcServiceHostedService(ILogger<RpcServiceHostedService> logger, IOptions<ServerOptions> serviceInfoOptions,
        IEnumerable<ServerServiceDefinition> serviceDefinitions)
    {
        m_logger = logger;
        m_serviceDefinitions = serviceDefinitions;
        m_serverOptions = serviceInfoOptions.Value;
    }

    /// <summary>
    ///     Starts the gRPC service.
    /// </summary>
    /// <param name="cancellationToken">Propagates notification that operations should be canceled.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    public Task StartAsync(CancellationToken cancellationToken)
    {
        int port = m_serverOptions.Port;
        m_rpcServer = new Grpc.Core.Server(m_channelOptions)
        {
            Ports = { new ServerPort(HostConstants.AnyHost, port, ServerCredentials.Insecure) }
        };

        foreach (ServerServiceDefinition? serviceDefinition in m_serviceDefinitions)
        {
            m_rpcServer.Services.Add(serviceDefinition);
        }

        m_logger.LogInformation("-------- grpc service is starting,host={host} port={port} -------",
            HostConstants.AnyHost, port);
        m_rpcServer.Start();
        return Task.CompletedTask;
    }

    /// <summary>
    ///     Stops the gRPC service.
    /// </summary>
    /// <param name="cancellationToken">Propagates notification that operations should be canceled.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        m_logger.LogInformation("---------- grpc service is stopping -------");
        if (m_rpcServer == null)
        {
            m_logger.LogInformation("---------- grpc service is stopped -------");
            return;
        }

        await m_rpcServer.KillAsync();
        m_logger.LogInformation("---------- grpc service is stopped -------");
    }
}
*/
