using System.Diagnostics;
using Microsoft.Extensions.Options;
using Phoenix.Server.Common;
using Phoenix.Server.Utils;

namespace Phoenix.Server.Model;

public class GameServerConfigureService
{
    public static GameServerConfigureService Instance;
    
    public GameServerConfigureService(IOptions<GamePlayerContextOptions> options)
    {
        Debug.Assert(Instance == null);
        GamePlayerContextOptions = options.Value;
        Instance = this;
    }
    
    /// <summary>
    /// 玩家现场配置
    /// </summary>
    public GamePlayerContextOptions GamePlayerContextOptions { get; init; }
}