// Copyright (c) Phoenix.  All Rights Reserved.

using IdGen;
using Microsoft.Extensions.Options;
using Phoenix.Server.CommonDataStructure;

namespace Phoenix.Server.Common;

/// <summary>
///     Represents a unique identifier generator.
///     This class is responsible for generating unique identifiers.
/// </summary>
public sealed class UniqueIdentifierGenerator : IUniqueIdentifierGenerator
{
    public static IUniqueIdentifierGenerator Instance { get; private set; } = default!;

    /// <summary>
    ///     The ID generator used to generate unique identifiers.
    /// </summary>
    private readonly IIdGenerator<long> m_idGenerator;

    /// <summary>
    ///     Initializes a new instance of the <see cref="UniqueIdentifierGenerator"/> class.
    ///     The ID generator is initialized with an instance ID from the server options.
    /// </summary>
    /// <param name="serviceInfo">The server options, which contain the instance ID.</param>
    public UniqueIdentifierGenerator(IOptions<ServerOptions> serviceInfo)
    {
        Instance = this;
        m_idGenerator = InstanceIdGenerator.CreateInstanceIdGenerator((int)serviceInfo.Value.NodeId);
    }

    /// <summary>
    ///     Creates a unique identifier.
    /// </summary>
    /// <returns>A unique identifier.</returns>
    public long CreateId() => m_idGenerator.CreateId();
}
