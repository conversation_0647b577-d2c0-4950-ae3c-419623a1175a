// Copyright (c) Phoenix All Rights Reserved.

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using Newtonsoft.Json.Linq;
using Phoenix.ConfigData;
using Phoenix.ConfigData.QuestGraph;

namespace Phoenix.Server.Common;

public class ResourceException : Exception
{
    public ResourceException(string message) : base(message)
    {
    }
}


/// <summary>
/// 配置表等相关资源的初始化
/// </summary>
public class ResourceInitializer
{
    public static string SectionName => "ResourcePath";


    public ResourceInitializer(
        IConfiguration configuration,
        ILogger<ResourceInitializer> logger)
    {
        var resourcePath = configuration.GetValue(SectionName, "");
        if (string.IsNullOrEmpty(resourcePath))
        {
            logger.LogCritical("Resource path is not configured");
            return;
        }

        // 加载配置表资源
        try
        {
            ConfigDataManager.instance.LoadAllSyncByFile(resourcePath);
        }
        catch (Exception e)
        {
            throw new ResourceException("Load config resource failed:"+e.Message);
        }

        // 加载任务资源
        try
        {
            foreach (var (k, v)
                     in ConfigDataManager.instance.phoenixHakoniwaQuestGraphMap)
            {
                if (!string.IsNullOrEmpty(v.QuestGraphFile))
                {
                    var questGraphFile = Path.Combine(resourcePath, "QuestGraphData", $"{v.QuestGraphFile}.json");
                    var jo = JObject.Parse(File.ReadAllText(questGraphFile));
                    v.QuestGraph = DeserializeUtility.DeserializeNodeGraph(jo);
                }
            }
        }
        catch (Exception e)
        {
            throw new ResourceException("Load quest resource failed:"+ e.Message);
        }

        logger.LogInformation("Resource path loaded successfully");
    }
}
