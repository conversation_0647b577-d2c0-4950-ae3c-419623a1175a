/*
// Copyright (c) Phoenix.  All Rights Reserved.

using Microsoft.Extensions.Options;
using StackExchange.Redis;

namespace Phoenix.Server.Common;

/// <summary>
///     Interface for the Player Game Server Location Service.
///     This interface provides methods to manage the location of the player game server.
/// </summary>
public interface IPlayerGameServerLocationService
{
    /// <summary>
    ///     Gets the game server name associated with a player ID.
    /// </summary>
    Task<string?> GetOnePlayerIdToGameServerName(Int64 playerId);

    /// <summary>
    ///     Tries to acquire a game server name for a player ID.
    /// </summary>
    Task<string> TryAcquireOnePlayerIdToGameServerName(Int64 playerId, string gameServerName);

    /// <summary>
    ///     Keeps the player ID alive in the server.
    /// </summary>
    Task<bool> KeepAlive(Int64 playerId);

    /// <summary>
    ///     Tries to grant a lease for a player ID and keep it alive in the server.
    /// </summary>
    Task<bool> LeaseGrantKeepAlive(Int64 playerId, string gameServerName);

    /// <summary>
    ///     Removes a player ID to game server name mapping.
    /// </summary>
    Task<bool> RemoveOnePlayerIdToGameServerName(Int64 playerId, string gameServerName);
}

/// <summary>
///     Represents a player game server location service.
///     This class provides methods to manage the location of the player game server.
/// </summary>
public sealed class PlayerGameServerLocationService : IPlayerGameServerLocationService
{
    public static IPlayerGameServerLocationService Instance { get; private set; } = default!;

    public PlayerGameServerLocationService(IOptionsSnapshot<PlayerGameServerLocationRedisSettingsOptions> settings, IServerHost host)
    {
        Instance = this;

        PlayerGameServerLocationRedisSettingsOptions redisSettings =
            settings.Get(PlayerGameServerLocationRedisSettingsOptions.StoreKey);

        ConfigurationOptions options = ConfigurationOptions.Parse(redisSettings.ConnectionString);
        options.ClientName = host.Name;
        m_redis = ConnectionMultiplexer.Connect(options);

        m_acquirePrepared = LuaScript.Prepare(AcquireScript);
        m_deletePrepared = LuaScript.Prepare(DeleteScript);

        m_databaseName = redisSettings.DatabaseName;
        m_defaultExpireSeconds = redisSettings.DefaultExpiredSecond;
        m_keepaliveExpireSeconds = redisSettings.KeepaliveExpiredSecond;
    }

    /// <summary>
    ///     Gets the Redis database.
    /// </summary>
    private IDatabase Database => m_redis.GetDatabase();

    /// <summary>
    ///     Gets the game server name associated with a player ID.
    /// </summary>
    public async Task<string?> GetOnePlayerIdToGameServerName(Int64 playerId)
    {
        RedisValue result =
            await Database.StringGetAsync(RedisKeyHelper.GetPlayerIdToGameServiceNameKey(m_databaseName, playerId));
        if (!result.HasValue)
        {
            return null;
        }

        return result;
    }

    /// <summary>
    ///     Tries to acquire a game server name for a player ID.
    /// </summary>
    public async Task<string> TryAcquireOnePlayerIdToGameServerName(Int64 playerId, string gameServerName)
    {
        RedisResult result =
            await Database.ScriptEvaluateAsync(m_acquirePrepared,
                new
                {
                    key = (RedisKey)RedisKeyHelper.GetPlayerIdToGameServiceNameKey(m_databaseName, playerId),
                    value = gameServerName,
                    expireSeconds = m_defaultExpireSeconds
                });
        return (string)result!;
    }

    /// <summary>
    ///     Keeps the player ID alive in the server.
    /// </summary>
    public Task<bool> KeepAlive(Int64 playerId) =>
        Database.KeyExpireAsync(RedisKeyHelper.GetPlayerIdToGameServiceNameKey(m_databaseName, playerId),
            TimeSpan.FromSeconds(m_keepaliveExpireSeconds));

    /// <summary>
    ///     Tries to grant a lease for a player ID and keep it alive in the server.
    /// </summary>
    public Task<bool> LeaseGrantKeepAlive(Int64 playerId, string gameServerName) =>
        Database.StringSetAsync(RedisKeyHelper.GetPlayerIdToGameServiceNameKey(m_databaseName, playerId),
            gameServerName, TimeSpan.FromSeconds(m_keepaliveExpireSeconds), When.NotExists);

    /// <summary>
    ///     Removes a player ID to game server name mapping.
    /// </summary>
    public async Task<bool> RemoveOnePlayerIdToGameServerName(Int64 playerId, string gameServerName)
    {
        RedisResult result =
            await Database.ScriptEvaluateAsync(m_deletePrepared,
                new
                {
                    key = (RedisKey)RedisKeyHelper.GetPlayerIdToGameServiceNameKey(m_databaseName, playerId),
                    value = gameServerName
                });
        return (int)result! > 0;
    }

    // Lua scripts for acquiring and deleting keys in Redis.
    private const string AcquireScript = @"
if redis.call('EXISTS', @key) == 1 then
    return redis.call('GET', @key)
else
    redis.call('SET', @key, @value, 'EX', @expireSeconds)
    return @value
end";
    private const string DeleteScript = @"
if redis.call('GET', @key) == @value then
    return redis.call('DEL', @key)
else
    return 0
end
";

    /// <summary>
    ///     Prepared Lua script for acquiring a key in Redis.
    /// </summary>
    private readonly LuaScript m_acquirePrepared;

    /// <summary>
    ///     The name of the database in Redis.
    /// </summary>
    private readonly string m_databaseName;

    /// <summary>
    ///     The default expiration time in seconds for keys in Redis.
    /// </summary>
    private readonly int m_defaultExpireSeconds;

    /// <summary>
    ///     Prepared Lua script for deleting a key in Redis.
    /// </summary>
    private readonly LuaScript m_deletePrepared;

    /// <summary>
    ///     The expiration time in seconds for keep-alive keys in Redis.
    /// </summary>
    private readonly int m_keepaliveExpireSeconds;

    /// <summary>
    ///     The Redis connection multiplexer.
    /// </summary>
    private readonly ConnectionMultiplexer m_redis;
}
*/
