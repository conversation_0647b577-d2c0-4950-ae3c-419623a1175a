// Copyright (c) Phoenix.  All Rights Reserved.

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Phoenix.Server.Utils.Security;


namespace Phoenix.Server.Common;

/// <summary>
///     Represents a RSA initializer.
///     This class is responsible for initializing RSA parameters from a key file.
/// </summary>
public sealed class RASInitializer
{
    /// <summary>
    ///     Initializes a new instance of the <see cref="RASInitializer"/> class.
    ///     The RSA parameters are initialized from a key file specified in the configuration.
    /// </summary>
    /// <param name="configuration">The application configuration, which contains the path to the RSA key file.</param>
    /// <param name="logger">The logger used to log information and errors.</param>
    /// <exception cref="NullReferenceException">Thrown when the RSAPath value in the configuration is null.</exception>
    public RASInitializer(IConfiguration configuration, ILogger<RASInitializer> logger)
    {
        // Get the path to the RSA key file from the configuration.
        string? path = configuration.GetValue<string>("RSAPath");
        if (path == null)
        {
            // Throw an exception if the RSAPath value is null.
            throw new NullReferenceException("Configuration RASPath value is null");
        }

        // Phoenix.Server.Log the path to the RSA key file.
        logger.LogInformation("RSAPath={path}", path);

        // Initialize the RSA parameters from the key file.
        RSA.InitRSAParametersFromKeyFile(path);
    }
}
