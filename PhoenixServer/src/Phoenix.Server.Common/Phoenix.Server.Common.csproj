<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>Phoenix.Server.Common</RootNamespace>
    <Configurations>Debug;Release</Configurations>
    <Platforms>x64</Platforms>
    <!--
    <ManagePackageVersionsCentrally>false</ManagePackageVersionsCentrally>
    -->
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" />
    <PackageReference Include="Consul" />
    <PackageReference Include="MongoDB.Driver" />
    <PackageReference Include="StackExchange.Redis" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" />
    <PackageReference Include="Serilog" />
    <PackageReference Include="Serilog.Enrichers.Thread" />
    <PackageReference Include="Serilog.Extensions.Hosting" />
    <PackageReference Include="Serilog.Settings.Configuration" />
    <PackageReference Include="Serilog.Sinks.Async" />
    <PackageReference Include="Serilog.Sinks.Console" />
    <PackageReference Include="Serilog.Sinks.File" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Phoenix.Codecs\Phoenix.Codecs.csproj" />
    <ProjectReference Include="..\ConfigData\ConfigData.csproj" />
    <ProjectReference Include="..\MongoDBWrapper\MongoDBWrapper.csproj" />
    <ProjectReference Include="..\Phoenix.Server.CommonDataStructure\Phoenix.Server.CommonDataStructure.csproj" />
    <ProjectReference Include="..\Utils\Phoenix.Server.Utils.csproj" />
    <PackageReference Include="DotNetty.Common" />
    <PackageReference Include="DotNetty.Handlers" />
    <PackageReference Include="DotNetty.Transport" />
    <PackageReference Include="DotNetty.Transport.Libuv" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Interfaces\**" />
    <Compile Remove="HostedServices\ServiceCollectionExtensions.cs" />
    <Compile Remove="Configuration\ConsulOptions.cs" />
    <Compile Remove="RpcService\CustomResolver.cs" />
    <Compile Remove="RpcService\MailMgr.cs" />
    <Compile Remove="RpcService\MessagePackConfig.cs" />
    <Compile Remove="Services\GamePlayerContextConfigure.cs" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Remove="Interfaces\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Interfaces\**" />
  </ItemGroup>

</Project>
