// Copyright (c) Phoenix.  All Rights Reserved.

using System.Collections.Concurrent;
using IdGen;

namespace Phoenix.Server.Common;

/// <summary>
///     Represents a manager for AwaitableHandle.
/// </summary>
public class AwaitableHandleManager
{
    /// <summary>
    ///     Tokens to be removed.
    /// </summary>
    private readonly List<long> m_tobeRemove = new();

    /// <summary>
    ///     Dictionary associating tokens with AwaitableHandles.
    /// </summary>
    private readonly ConcurrentDictionary<long, AwaitableHandle> m_token2AwaitableHandle = new();

    /// <summary>
    ///     Token generator.
    /// </summary>
    private readonly IIdGenerator<long> m_tokenGenerator = new IdGenerator(0);

    /// <summary>
    ///     Allocates an AwaitableHandle.
    /// </summary>
    /// <param name="timeout">The timeout in seconds.</param>
    /// <returns>The allocated AwaitableHandle.</returns>
    public AwaitableHandle AllocHandle(int timeout = 10)
    {
        long token = m_tokenGenerator.CreateId();
        AwaitableHandle handle = new AwaitableHandle(token, timeout);
        m_token2AwaitableHandle[token] = handle;
        return handle;
    }

    public AwaitableHandle? GetAwaitableHandle(long token)
    {
        return m_token2AwaitableHandle.TryGetValue(token, out AwaitableHandle? handle) ? handle : null;
    }

    /// <summary>
    ///     Sets the cancellation.
    /// </summary>
    /// <param name="token">The token.</param>
    /// <returns>True if the operation was successful; otherwise, false.</returns>
    public bool Cancel(int token)
    {
        if (m_token2AwaitableHandle.TryRemove(token, out AwaitableHandle? handle))
        {
            handle.SetCancel();
            return true;
        }

        return false;
    }

    /// <summary>
    ///     Sets the completion result.
    /// </summary>
    /// <param name="token">The token.</param>
    /// <param name="result">The result.</param>
    /// <returns>True if the operation was successful; otherwise, false.</returns>
    public bool SetResult(long token, object result)
    {
        if (m_token2AwaitableHandle.TryRemove(token, out AwaitableHandle? handle))
        {
            handle.SetResult(result);
            return true;
        }

        return false;
    }

    /// <summary>
    ///     Handles the tick for timeout.
    /// </summary>
    public void Tick()
    {
        DateTime currTime = DateTime.Now;

        // Check for timeout
        foreach (KeyValuePair<long, AwaitableHandle> item in m_token2AwaitableHandle)
        {
            if (item.Value.TimeoutTime <= currTime)
            {
                m_tobeRemove.Add(item.Key);
            }
        }

        // Remove timed out items
        foreach (long token in m_tobeRemove)
        {
            if (m_token2AwaitableHandle.TryRemove(token, out AwaitableHandle? handle))
            {
                handle.SetTimeout();
            }
        }

        m_tobeRemove.Clear();
    }

    /// <summary>
    ///     Stops the manager and clears all handles.
    /// </summary>
    public void Stop()
    {
        foreach (KeyValuePair<long, AwaitableHandle> item in m_token2AwaitableHandle)
        {
            item.Value.SetTimeout();
        }

        m_token2AwaitableHandle.Clear();
    }
}
