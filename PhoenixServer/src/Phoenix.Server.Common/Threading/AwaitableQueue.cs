// Copyright (c) Phoenix.  All Rights Reserved.

using System.Collections.Concurrent;

namespace Phoenix.Server.Common;

/// <summary>
///     Represents an awaitable queue.
/// </summary>
/// <typeparam name="T">The type of elements in the queue.</typeparam>
public class AwaitableQueue<T>
{
    /// <summary>
    ///     Adds an item to the end of the queue.
    /// </summary>
    /// <param name="item">The item to add to the queue.</param>
    public void Enqueue(T item)
    {
        if (m_isShutdown)
        {
            return;
        }

        lock (this)
        {
            TaskCompletionSource<T> result;
            // Try to dequeue a pending task and set its result to the item.
            if (m_pendingTaskQueue.TryDequeue(out result!))
            {
                result.SetResultAsync(item);
            }
            else
            {
                // If there are no pending tasks, enqueue the item.
                m_queue.Enqueue(item);
            }
        }
    }

    /// <summary>
    ///     Removes and returns the item at the beginning of the queue asynchronously.
    /// </summary>
    /// <returns>A Task representing the asynchronous operation. The Task's result is the dequeued item.</returns>
    public async Task<T> DequeueAsync()
    {
        T result = default!;

        if (m_isShutdown)
        {
            return result;
        }

        TaskCompletionSource<T> tcs;
        lock (this)
        {
            // Try to dequeue an item.
            if (m_queue.TryDequeue(out result!))
            {
                return result;
            }

            // If the queue is empty, enqueue a new pending task.
            tcs = new TaskCompletionSource<T>();
            m_pendingTaskQueue.Enqueue(tcs);
        }

        return tcs.Task.IsCompleted ? tcs.Task.Result : await tcs.Task;
    }

    /// <summary>
    ///     Removes and returns the item at the beginning of the queue.
    /// </summary>
    /// <param name="t">When this method returns, if the operation was successful, t contains the dequeued item. If the operation was unsuccessful, t contains the default value for the type of the elements in the queue.</param>
    /// <returns>true if the operation was successful; otherwise, false.</returns>
    public bool TryDequeue(out T? t) => m_queue.TryDequeue(out t);

    /// <summary>
    ///     Shuts down the queue and cancels all pending tasks.
    /// </summary>
    public void Shutdown()
    {
        m_isShutdown = true;

        CancelWaitingDequeue();
    }

    /// <summary>
    ///     Cancels all pending tasks.
    /// </summary>
    public void CancelWaitingDequeue()
    {
        lock (this)
        {
            while (true)
            {
                if (m_pendingTaskQueue.TryDequeue(out TaskCompletionSource<T>? tcs))
                {
                    tcs.SetCanceled();
                }
                else
                {
                    break;
                }
            }
        }
    }

    /// <summary>
    ///     Adds an item to the beginning of the queue.
    /// </summary>
    /// <param name="item">The item to add to the queue.</param>
    public void PushItem2Top(T item)
    {
        lock (this)
        {
            List<T> tempList = m_queue.ToList();
            m_queue.Clear();
            // Insert the item at the beginning of the list.
            tempList.Insert(0, item);
            foreach (T it in tempList)
            {
                m_queue.Enqueue(it);
            }
        }
    }

    /// <summary>
    ///     Queue of pending tasks.
    /// </summary>
    private readonly ConcurrentQueue<TaskCompletionSource<T>> m_pendingTaskQueue = new();

    /// <summary>
    ///     The actual queue of items.
    /// </summary>
    private readonly ConcurrentQueue<T> m_queue = new();

    /// <summary>
    ///     Flag indicating whether the queue is shut down.
    /// </summary>
    private bool m_isShutdown;

    /// <summary>
    ///     Gets the number of elements in the queue.
    /// </summary>
    public int Count => m_queue.Count;

}
