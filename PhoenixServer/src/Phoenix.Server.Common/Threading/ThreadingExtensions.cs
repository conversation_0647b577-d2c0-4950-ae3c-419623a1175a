// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.Common;

/// <summary>
///     Provides extension methods for threading operations.
/// </summary>
public static class ThreadingExtensions
{
    /// <summary>
    ///     Provides an awaitable TryEnter method for a SpinLock.
    /// </summary>
    /// <param name="spinLock">The SpinLock to enter.</param>
    /// <param name="timeoutInMillionSeconds">The timeout for the operation in milliseconds.</param>
    /// <returns>A Task representing the asynchronous operation. The Task's result is true if the lock was successfully entered; otherwise, false.</returns>
    public static async Task<bool> TryEnter(this SpinLock spinLock, int timeoutInMillionSeconds)
    {
        bool hasLock = false;
        DateTime timeLocked = DateTime.Now;
        do
        {
            // Try to enter the lock.
            spinLock.TryEnter(ref hasLock);
            if (hasLock)
            {
                // If the lock was successfully entered, return true.
                return true;
            }

            // If a timeout was specified, yield the current thread.
            if (timeoutInMillionSeconds > 0)
            {
                await Task.Yield();
            }
            // Continue trying to enter the lock until the timeout expires.
        } while ((DateTime.Now - timeLocked).Milliseconds < timeoutInMillionSeconds);

        // If the timeout expired without entering the lock, return false.
        return false;
    }

    /// <summary>
    ///     Provides an asynchronous SetResult method for a TaskCompletionSource.
    ///     This method allows the continuation of the await operation to be executed on a thread pool thread.
    /// </summary>
    /// <typeparam name="T">The type of the result for the TaskCompletionSource.</typeparam>
    /// <param name="tcs">The TaskCompletionSource to set the result for.</param>
    /// <param name="result">The result to set.</param>
    /// <param name="taskScheduler">The TaskScheduler to schedule the continuation on. If null, the default TaskScheduler is used.</param>
    public static void SetResultAsync<T>(this TaskCompletionSource<T> tcs, T result,
        TaskScheduler? taskScheduler = null)
    {
        if (taskScheduler != null)
        {
            // If a TaskScheduler was specified, start a new task on it to set the result.
            Task.Factory.StartNew(() => { tcs.SetResult(result); }, CancellationToken.None,
                TaskCreationOptions.LongRunning, taskScheduler);
        }
        else
        {
            // If no TaskScheduler was specified, start a new task on the default TaskScheduler to set the result.
            Task.Run(() => { tcs.SetResult(result); });
        }
    }
}
