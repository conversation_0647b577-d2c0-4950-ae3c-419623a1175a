// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.Common;

/// <summary>
///     Represents an awaitable handle.
/// </summary>
public class AwaitableHandle
{
    /// <summary>
    ///     Supports task await.
    /// </summary>
    private readonly TaskCompletionSource<bool> m_tcs = new();

    /// <summary>
    ///     Used to store the result data obtained after waiting.
    /// </summary>
    private object? m_result;

    /// <summary>
    ///     Initializes a new instance of the <see cref="AwaitableHandle"/> class.
    /// </summary>
    /// <param name="token">The token.</param>
    /// <param name="timeout">The timeout in seconds.</param>
    public AwaitableHandle(long token, int timeout = 10)
    {
        Token = token;
        TimeoutTime = timeout > 0 ? DateTime.Now.AddSeconds(timeout) : DateTime.MaxValue;
        IsTimeOut = false;
    }

    /// <summary>
    ///     Used to identify a scene of an RPCCall.
    /// </summary>
    public long Token { get; private set; }

    /// <summary>
    ///     The timeout time.
    /// </summary>
    public DateTime TimeoutTime { get; private set; }

    /// <summary>
    ///     Indicates whether it has timed out.
    /// </summary>
    public bool IsTimeOut { get; private set; }

    /// <summary>
    ///     Indicates whether it has been cancelled.
    /// </summary>
    public bool IsCancel { get; private set; }

    /// <summary>
    ///     Initiates waiting.
    /// </summary>
    /// <returns>A Task representing the asynchronous operation.</returns>
    public async Task WaitAsync() => await m_tcs.Task;

    /// <summary>
    ///     Sets the result.
    /// </summary>
    /// <typeparam name="T">The type of the result.</typeparam>
    /// <param name="result">The result.</param>
    /// <param name="taskScheduler">The task scheduler.</param>
    public void SetResult<T>(T? result, TaskScheduler? taskScheduler = null)
        where T : class
    {
        m_result = result;
        m_tcs.SetResultAsync(true, taskScheduler);
    }

    /// <summary>
    ///     Sets the result synchronously.
    /// </summary>
    /// <typeparam name="T">The type of the result.</typeparam>
    /// <param name="result">The result.</param>
    /// <param name="taskScheduler">The task scheduler.</param>
    public void SetResultSync<T>(T result, TaskScheduler? taskScheduler = null)
        where T : class
    {
        m_result = result;
        m_tcs.SetResult(true);
    }

    /// <summary>
    ///     Gets the result.
    /// </summary>
    /// <typeparam name="T">The type of the result.</typeparam>
    /// <returns>The result.</returns>
    public T GetResult<T>()
        where T : class =>
        (m_result as T)!;

    /// <summary>
    ///     Sets the timeout.
    /// </summary>
    public void SetTimeout()
    {
        IsTimeOut = true;
        m_tcs.SetResultAsync(true);
    }

    /// <summary>
    ///     Sets the cancellation.
    /// </summary>
    public void SetCancel()
    {
        IsCancel = true;
        m_tcs.SetResultAsync(true);
    }

    /// <summary>
    ///     Sets the exception.
    /// </summary>
    /// <param name="e">The exception.</param>
    public void SetException(Exception e) => m_tcs.SetException(e);
}
