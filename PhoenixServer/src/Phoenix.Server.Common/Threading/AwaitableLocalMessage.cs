// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.Common;

/// <summary>
///     Represents a local message that can be awaited.
/// </summary>
/// <typeparam name="TReq">The type of the request.</typeparam>
/// <typeparam name="TAck">The type of the acknowledgement.</typeparam>
public class AwaitableLocalMessage<TReq, TAck>
{
    /// <summary>
    ///     The acknowledgement for the message.
    /// </summary>
    public TAck Ack = default!;

    /// <summary>
    ///     The awaitable handle for the message.
    /// </summary>
    private readonly AwaitableHandle m_awaitableHandle;

    /// <summary>
    ///     Initializes a new instance of the <see cref="AwaitableLocalMessage{TReq, TAck}"/> class.
    /// </summary>
    /// <param name="req">The request for the message.</param>
    public AwaitableLocalMessage(TReq req)
    {
        Req = req;
        m_awaitableHandle = new AwaitableHandle(0, 0);
    }

    /// <summary>
    ///     Gets the request for the message.
    /// </summary>
    public TReq Req { get; private set; }

    /// <summary>
    ///     Waits for the message to be processed asynchronously.
    /// </summary>
    /// <returns>A Task representing the asynchronous operation.</returns>
    public async Task WaitAsync() => await m_awaitableHandle.WaitAsync();

    /// <summary>
    ///     Sets the result for the message.
    /// </summary>
    public void SetResult() => m_awaitableHandle.SetResult<object>(null);
}
