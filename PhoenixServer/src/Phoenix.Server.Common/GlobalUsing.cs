
global using userid_t = long;
global using entity_id_t = long;
global using unitid_t = long;
global using nodeid_t = uint;
global using connid_t = uint;
global using hash_idx_t = uint;
global using request_id_t = long;
global using db_job_id_t = uint;
global using ModuleIndexType = int;
global using slot_index_t = uint;
global using int8_t = sbyte;
global using uint8_t = byte;
global using int16_t = short;
global using uint16_t = ushort;
global using int32_t = int;
global using uint32_t = uint;
global using int64_t = long;
global using uint64_t = ulong;
global using size_t = ulong;
