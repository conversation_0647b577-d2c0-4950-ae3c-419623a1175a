// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.Common;

/// <summary>
///     Configuration options for Redis.
/// </summary>
/// <param name="ConnectionString">The connection string to the Redis database</param>
/// <param name="DatabaseName">The name of the Redis database</param>
public record RedisSettingsOptions(string ConnectionString, string DatabaseName)
{
    /// <summary>
    ///     The configuration store name for the game settings in Redis.
    /// </summary>
    public const string StoreKeyGame = "RedisSettings:Game";

    // Resharper disable once UnusedMember.Global
    /// <summary>
    ///     Default constructor for RedisSettingsOptions.
    /// </summary>
    public RedisSettingsOptions() : this(string.Empty, string.Empty)
    {
    }
}

/// <summary>
///     Configuration options for player game server location in Redis.
/// </summary>
/// <param name="ConnectionString">The connection string to the Redis database</param>
/// <param name="DatabaseName">The name of the Redis database</param>
/// <param name="DefaultExpiredSecond">The default expiration time in seconds</param>
/// <param name="KeepaliveExpiredSecond">The keep-alive expiration time in seconds</param>
public sealed record PlayerGameServerLocationRedisSettingsOptions(
    string ConnectionString,
    string DatabaseName,
    int DefaultExpiredSecond,
    int KeepaliveExpiredSecond) : RedisSettingsOptions(ConnectionString, DatabaseName)
{
    /// <summary>
    ///     The configuration store name for player game server location settings in Redis.
    /// </summary>
    public const string StoreKey = "RedisSettings:PlayerGameServerLocation";

    // Resharper disable once UnusedMember.Global
    /// <summary>
    ///     Default constructor for PlayerGameServerLocationRedisSettingsOptions.
    /// </summary>
    public PlayerGameServerLocationRedisSettingsOptions() : this(string.Empty, string.Empty, 60, 60)
    {
    }
}
