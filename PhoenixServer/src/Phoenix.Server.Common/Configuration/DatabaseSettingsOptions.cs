// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.Common;

/// <summary>
///     Configuration options for MongoDB.
/// </summary>
/// <param name="ConnectionString">The connection string to the database, e.g., mongodb://MongoDB:27017</param>
/// <param name="DatabaseName">The name of the database, e.g., GameDB</param>
public sealed record DatabaseSettingsOptions(string ConnectionString, string DatabaseName)
{
    /// <summary>
    ///     The configuration store name for the game database.
    /// </summary>
    public const string StoreKeyGame = "DatabaseSettings:Game";

    /// <summary>
    ///     The configuration store name for the global database.
    /// </summary>
    public const string StoreKeyGlobal = "DatabaseSettings:Global";

    // Resharper disable once UnusedMember.Global
    /// <summary>
    ///     Default constructor for DatabaseSettingsOptions.
    /// </summary>
    public DatabaseSettingsOptions() : this(string.Empty, string.Empty)
    {
    }
}
