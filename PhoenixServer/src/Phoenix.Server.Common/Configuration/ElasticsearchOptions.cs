// Copyright (c) Phoenix. All Rights Reserved.

namespace Phoenix.Server.Common;

/// <summary>
///     Options for configuring Elasticsearch.
/// </summary>
public sealed record ElasticsearchOptions(string Uri, ElasticsearchOptions.DataStreamOptions DataStream)
{
    /// <summary>
    ///     Key name for the configuration storage.
    /// </summary>
    public const string StoreKey = "Elasticsearch";

    /// <summary>
    ///     Default constructor for ElasticsearchOptions.
    /// </summary>
    // Resharper disable once UnusedMember.Global
    public ElasticsearchOptions() : this(string.Empty, new DataStreamOptions())
    {
    }

    /// <summary>
    ///     Options for configuring a data stream in Elasticsearch.
    /// </summary>
    public sealed record DataStreamOptions(string Type, string DataSet, string Namespace)
    {
        /// <summary>
        ///     Default constructor for DataStream.
        /// </summary>
        public DataStreamOptions() : this("logs", "server", "default")
        {
        }
    }
}
