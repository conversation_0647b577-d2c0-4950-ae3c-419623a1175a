// Copyright (c) Phoenix All Rights Reserved.

namespace Phoenix.Server.Common;

public record ClusterOptions
{
    /// <summary>
    ///     The key name for the configuration store.
    /// </summary>
    public const string StoreKey = "Cluster";

    public uint CommanderNodeId { get; init; }

    public string CommanderIp
    {
        get;
        init;
    } = string.Empty;

    public ushort CommanderPort { get; init; }

    public string ConsistentHashRole { get; init; } = string.Empty;

    /// <summary>
    /// Consul service address
    /// </summary>
    public string ConsulAddress { get; set; } = string.Empty;

    /// <summary>
    /// Session TTL time (seconds)
    /// </summary>
    public int ConsulSessionTTL { get; set; } = 10;

    public IDictionary<string, uint> RoleRequireNum
    {
        get;
        init;
    } = new Dictionary<string, uint>();
}
