// Copyright (c) Phoenix.  All Rights Reserved.

using System.Text.Json;
using System.Text.Json.Serialization;

namespace Phoenix.Server.Common;

/// <summary>
///     Defines the server configuration options.
/// </summary>
/// <param name="ZoneId">The zone identifier</param>
/// <param name="Name">The name of the server, e.g., LoginServer_1</param>
/// <param name="Role">The role type of the server, e.g., login, gate, game</param>
/// <param name="Ip">The IP address</param>
/// <param name="Port">The port number</param>
/// <param name="Tag">tag names</param>
/// <param name="NodeId">The unique identifier</param>
/// <param name="ServerHeartbeatInterval">The server heartbeat interval in seconds</param>
/// <param name="ServerHeartbeatTimeout">The server heartbeat timeout in seconds</param>
public record ServerOptions(int ZoneId, string Name, string Role, string Ip, ushort Port, string Tag, uint NodeId, int ServerHeartbeatInterval, int ServerHeartbeatTimeout)
{
    /// <summary>
    ///     The key name for the configuration store.
    /// </summary>
    public const string StoreKey = "Server";

    /// <summary>
    /// The server weight
    /// </summary>
    public int Weight { get; set; }

    /// <summary>
    ///     Default constructor is needed when creating an instance using Activator.CreateInstance/<TOptions />() in IOptions
    ///     pattern.
    ///     If a default constructor is not provided, a System.MissingMethodException will be thrown.
    /// </summary>
    // Resharper disable once UnusedMember.Global
    public ServerOptions() : this(0, string.Empty, string.Empty, string.Empty, 0, string.Empty, 0, 10, 60)
    {
    }
}

/// <summary>
///     JSON converter for ServerOptions.
/// </summary>
public sealed class ServerOptionsJsonConverter : JsonConverter<ServerOptions>
{
    public override bool CanConvert(Type typeToConvert) =>
        typeof(ServerOptions).IsAssignableFrom(typeToConvert);

    /// <summary>
    ///     Reads a ServerOptions object from a JSON stream.
    /// </summary>
    public override ServerOptions Read(
        ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        // Clone the reader to avoid advancing the original reader's position
        Utf8JsonReader readerClone = reader;
        string? typeName = null;

        // Iterate over the JSON stream
        while (readerClone.Read())
        {
            // Stop reading if the end of the JSON object is reached
            if (readerClone.TokenType == JsonTokenType.EndObject)
            {
                break;
            }

            // If a property name is encountered...
            if (readerClone.TokenType == JsonTokenType.PropertyName)
            {
                // Get the property name
                string? propertyName = readerClone.GetString();
                // Advance the reader to the property value
                readerClone.Read();

                // If the property name is "TypeName"...
                if (propertyName == "TypeName")
                {
                    // Get the value of the "TypeName" property
                    typeName = readerClone.GetString();
                    // Stop reading further
                    break;
                }
            }
        }

        // If no "TypeName" property was found, throw an exception
        if (typeName == null)
        {
            throw new Exception("missing type field");
        }

        // If the "TypeName" property value is "DedicatedServer", deserialize as a DSServerOptions object
        /*if (typeName == "DedicatedServer")
        {
            return JsonSerializer.Deserialize<DSServerOptions>(ref reader)!;
        }*/

        // Otherwise, deserialize as a ServerOptions object
        return JsonSerializer.Deserialize<ServerOptions>(ref reader)!;
    }

    /// <summary>
    ///     Writes a ServerOptions object to a JSON stream.
    /// </summary>
    public override void Write(
        Utf8JsonWriter writer, ServerOptions serverOptions, JsonSerializerOptions options) =>
        throw new NotImplementedException();
}
