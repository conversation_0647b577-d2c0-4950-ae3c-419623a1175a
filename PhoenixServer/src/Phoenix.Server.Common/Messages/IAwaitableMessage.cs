// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.Common;

/// <summary>
///     Represents an awaitable message interface.
///     This interface extends the IMessage interface for messages that support asynchronous waiting and result retrieval.
/// </summary>
public interface IAwaitableMessage : IMessage
{
    /// <summary>
    ///     Asynchronously waits for the message to complete.
    ///     This method returns a Task that completes when the message is done.
    /// </summary>
    /// <returns>A Task representing the asynchronous operation.</returns>
    Task WaitAsync();

    /// <summary>
    ///     Gets the result of the message.
    ///     This method is used to retrieve the result after the message has completed.
    /// </summary>
    /// <returns>The result of the message as an object.</returns>
    object GetResult();
}
