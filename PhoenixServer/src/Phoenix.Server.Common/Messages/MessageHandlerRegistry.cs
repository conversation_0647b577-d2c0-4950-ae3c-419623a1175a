using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Utils;
using System.Reflection;
using Microsoft.Extensions.Logging;

namespace Phoenix.Server.Common.MessageHandlerRegistry;

[AttributeUsage(AttributeTargets.Method)]
public class MessageHandlerAttribute : Attribute
{
    public MessageHandlerAttribute(Type type)
    {
        MessageType = type;
    }

    public Type MessageType { get; }
}

public interface IMessageHandlerRegistry
{
    MessageHandlerRegistry.HandlerInfo? TryGetMessageHandler(MsgPackStructBase message);
}

/// <summary>
/// 消息处理器注册表
/// </summary>
public class MessageHandlerRegistry : IMessageHandlerRegistry
{
    public MessageHandlerRegistry()
    {
        if (m_instance != null)
        {
            throw new InvalidOperationException("MessageHandlerRegistry instance already exists");
        }
        m_instance = this;
        InitRegisterMessageHandlers();
    }

    protected static MessageHandlerRegistry m_instance;
    public static MessageHandlerRegistry Instance
    {
        get { return m_instance; }
    }

    // 消息处理委托类型
    public delegate Task MessageHandlerDelegate(object execInstance, MsgPackStructBase message);

    // 消息ID到处理器信息的映射
    private readonly Dictionary<int, HandlerInfo> s_messageIdToHandler = new();

    // 消息类型到组件类型的映射
    private readonly Dictionary<Type, Type> s_messageTypeToComponentType = new();

    // 日志
    protected readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger(typeof(MessageHandlerRegistry));


    protected virtual void InitRegisterMessageHandlers()
    {
    }

    /// <summary>
    /// 注册所有消息处理器
    /// </summary>
    protected void RegisterMessageHandlers(Type messageHandlerType)
    {
        // 查找所有带MessageHandler特性的方法
        var methods = messageHandlerType.GetMethods(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic)
            .Where(m => m.GetCustomAttribute<MessageHandlerAttribute>() != null);

        foreach (var method in methods)
        {
            var attribute = method.GetCustomAttribute<MessageHandlerAttribute>()!;

            // 检查方法签名
            if (method.ReturnType != typeof(Task))
            {
                s_logger.LogWarning("Method {Method} in {Component} has MessageHandler attribute but doesn't return Task",
                    method.Name, messageHandlerType.Name);
                continue;
            }

            var parameters = method.GetParameters();
            if (parameters.Length != 1)
            {
                s_logger.LogWarning("Method {Method} in {Component} has MessageHandler attribute but doesn't have exactly one parameter",
                    method.Name, messageHandlerType.Name);
                continue;
            }

            // 获取消息类型
            Type messageType = attribute.MessageType ?? parameters[0].ParameterType;

            // 检查消息类型是否继承自MsgPackStructBase
            if (!IsSubclassOf(messageType, typeof(MsgPackStructBase)))
            {
                s_logger.LogWarning("Method {Method} in {Component} has MessageHandler attribute but parameter type {ParameterType} is not derived from MsgPackStructBase",
                    method.Name, messageHandlerType.Name, messageType.Name);
                continue;
            }

            // 创建处理器委托
            var handler = CreateHandlerDelegate(method);

            // 注册处理器
            RegisterHandler(messageType, messageHandlerType, handler);

            s_logger.LogInformation("Registered handler {Component}.{Method} for message {MessageType}",
                messageHandlerType.Name, method.Name, messageType.Name);
        }
    }

    /// <summary>
    /// 创建处理器委托
    /// </summary>
    private MessageHandlerDelegate CreateHandlerDelegate(MethodInfo method)
    {
        return (instanceObj, message) =>
        {
            try
            {
                return (Task)method.Invoke(instanceObj, new object[] { message });
            }
            catch (Exception ex)
            {
                s_logger.LogError(ex, "Error invoking message handler {Method} on {InstanceName}",
                    method.Name, instanceObj.GetType().Name);
                return Task.CompletedTask;
            }
        };
    }

    /// <summary>
    /// 注册消息处理器
    /// </summary>
    private void RegisterHandler(Type messageType, Type execType, MessageHandlerDelegate handler)
    {
        try
        {
            int messageId = MsgPackProtoHelper.GetProtoIdByType(messageType);

            if (s_messageIdToHandler.ContainsKey(messageId))
            {
                var existingHandler = s_messageIdToHandler[messageId];
                s_logger.LogWarning("Message {MessageType} (ID: {MessageId}) already has a handler in {ExistingType}, ignoring handler in {ExecType}",
                    messageType.Name, messageId, existingHandler.ExecType.Name, execType.Name);
                return;
            }

            s_messageIdToHandler[messageId] = new HandlerInfo
            {
                ExecType = execType,
                Handler = handler
            };

            s_messageTypeToComponentType[messageType] = execType;
            s_logger.LogDebug("Registered handler for message {MessageType} (ID: {MessageId}) in {ExecType}",
                messageType.Name, messageId, execType.Name);
        }
        catch (Exception ex)
        {
            s_logger.LogError(ex, "Error registering handler for message {MessageType} in {ExecType}",
                messageType.Name, execType.Name);
        }
    }

    /// <summary>
    /// 尝试处理消息
    /// </summary>
    /// <param name="message">要处理的消息</param>
    /// <param name="componentProvider">组件提供者函数</param>
    /// <returns>如果找到处理器并成功处理返回true，否则返回false</returns>
    public bool TryHandleMessage(MsgPackStructBase message, Func<Type, object> componentProvider)
    {
        int messageId = (int)message.ProtoCode;
        if (s_messageIdToHandler.TryGetValue(messageId, out var handlerInfo))
        {
            var component = componentProvider(handlerInfo.ExecType);
            if (component != null)
            {
                try
                {
                    // 异步处理消息，但不等待结果
                    _ = handlerInfo.Handler(component, message);
                    return true;
                }
                catch (Exception ex)
                {
                    s_logger.LogError(ex, "Error handling message {MessageType} with component {ComponentType}",
                        message.GetType().Name, handlerInfo.ExecType.Name);
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 根据消息类型返回处理类
    /// </summary>
    /// <param name="message"></param>
    /// <returns></returns>
    public HandlerInfo? TryGetMessageHandler(MsgPackStructBase message)
    {
        int messageId = (int)message.ProtoCode;
        if (s_messageIdToHandler.TryGetValue(messageId, out var handlerInfo))
        {
            return handlerInfo;
        }

        return null;
    }

    /// <summary>
    /// 检查类型是否是指定基类的子类
    /// </summary>
    protected bool IsSubclassOf(Type type, Type baseType)
    {
       var ret =  type.IsSubclassOf(baseType);
       return ret;
    }

    /// <summary>
    /// 处理器信息
    /// </summary>
    public class HandlerInfo
    {
        /// <summary>
        /// 处理该消息的类型
        /// </summary>
        public Type ExecType { get; init; }

        /// <summary>
        /// 消息处理委托
        /// </summary>
        public MessageHandlerDelegate Handler { get; init; }
    }
}
