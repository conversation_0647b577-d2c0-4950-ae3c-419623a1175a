// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.Common;

/// <summary>
///     Represents a local message interface.
///     This interface extends the IMessage interface for messages that are local to the server.
/// </summary>
public interface ILocalMessage : IMessage
{
}

/// <summary>
///     Represents a local message that is sent on each timer tick.
///     This class is a singleton, with a single instance that is reused for each timer tick.
/// </summary>
public sealed class LocalMessageTimerTick : ILocalMessage
{
    /// <summary>
    ///     The singleton instance of the LocalMessageTimerTick class.
    /// </summary>
    public static readonly LocalMessageTimerTick Instance = new();

    /// <summary>
    ///     Gets the correlation ID of the message.
    ///     The correlation ID is a unique identifier that is generated anew for each timer tick.
    /// </summary>
    public string CorrelationId => Guid.NewGuid().ToString();
}

/// <summary>
///     Represents a local message that is sent on kickoff.
/// </summary>
public sealed class LocalMessageKickoff : ILocalMessage
{
    /// <summary>
    ///     The singleton instance of the LocalMessageTimerTick class.
    /// </summary>
    public static readonly LocalMessageKickoff Instance = new();

    /// <summary>
    ///     Gets the correlation ID of the message.
    ///     The correlation ID is a unique identifier that is generated anew for each timer tick.
    /// </summary>
    public string CorrelationId => Guid.NewGuid().ToString();
}

/// <summary>
///     Represents a local message that carries an action.
///     This class is used to send an action as a message within the server.
/// </summary>
public sealed class LocalMessageAction : ILocalMessage
{
    /// <summary>
    ///     Initializes a new instance of the <see cref="LocalMessageAction"/> class.
    ///     The action for the message is set during initialization.
    /// </summary>
    /// <param name="action">The action to be carried by the message.</param>
    public LocalMessageAction(Action action) => Action = action;

    /// <summary>
    ///     Gets the action carried by the message.
    /// </summary>
    public Action Action { get; }

    /// <summary>
    ///     Gets the correlation ID of the message.
    ///     The correlation ID is a unique identifier that is generated anew for each action message.
    /// </summary>
    public string CorrelationId => Guid.NewGuid().ToString();
}
