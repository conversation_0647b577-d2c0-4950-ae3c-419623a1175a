// Copyright (c) Phoenix.All Rights Reserved.

using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Server.Common;

public class ClientMessage : IMessage
{
    public ClientMessage(string correlationId, MsgPackStructBase message)
    {
        CorrelationId = correlationId;
        //ChannelCtx = channelCtx;
        Message = message;
    }

    public override string ToString()
    {
        return $"CorrelationId={CorrelationId} Message={Message}";
    }

    public string CorrelationId { get; private set; }

    //public IChannelHandlerContext ChannelCtx { get; private set; }
    public MsgPackStructBase Message { get; private set; }
}
