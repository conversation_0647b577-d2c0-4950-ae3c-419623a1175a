// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.Common;

/// <summary>
///     Represents an abstract class for awaitable messages.
///     This class implements the IAwaitableMessage interface and provides a base for specific types of awaitable messages.
/// </summary>
public abstract class AwaitableMessage : IAwaitableMessage
{
    /// <summary>
    ///     The awaitable handle for the message.
    /// </summary>
    private readonly AwaitableHandle m_awaitableHandle;

    /*/// <summary>
    ///     Initializes a new instance of the <see cref="AwaitableMessage"/> class.
    ///     The request and correlation ID for the message are set during initialization.
    /// </summary>
    /// <param name="req">The request to be processed by the message.</param>
    /// <param name="correlationId">The correlation ID of the message.</param>
    protected AwaitableMessage(Google.Protobuf.IMessage req, string correlationId)
    {
        Req = req;
        m_awaitableHandle = new AwaitableHandle(0, 0);
        CorrelationId = correlationId;
    }

    /// <summary>
    ///     Gets the request to be processed by the message.
    /// </summary>
    public Google.Protobuf.IMessage Req { get; private set; }*/

    /// <summary>
    ///     Asynchronously waits for the message to complete.
    /// </summary>
    /// <returns>A Task representing the asynchronous operation.</returns>
    public async Task WaitAsync() => await m_awaitableHandle.WaitAsync();

    /// <summary>
    ///     Gets the result of the message.
    /// </summary>
    /// <returns>The result of the message as an object.</returns>
    public object GetResult() => m_awaitableHandle.GetResult<object>();

    /// <summary>
    ///     Gets the correlation ID of the message.
    /// </summary>
    public string CorrelationId { get; }

    /// <summary>
    ///     Sets the result of the message synchronously.
    /// </summary>
    /// <param name="result">The result to be set.</param>
    public void SetResultSync(object result) => m_awaitableHandle.SetResultSync(result);

    /// <summary>
    ///     Sets the result of the message.
    /// </summary>
    /// <param name="result">The result to be set.</param>
    public void SetResult(object result) => m_awaitableHandle.SetResult(result);

    /// <summary>
    ///     Sets the exception for the message.
    /// </summary>
    /// <param name="e">The exception to be set.</param>
    public void SetException(Exception e) => m_awaitableHandle.SetException(e);
}
