// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.Common;

/// <summary>
///     Represents a message interface.
///     This interface defines a message with a correlation ID.
/// </summary>
public interface IMessage
{
    /// <summary>
    ///     Gets the correlation ID of the message.
    ///     The correlation ID is a unique identifier that can be used to trace a message through a system.
    /// </summary>
    string CorrelationId { get; }
}
