// Copyright (c) Phoenix.All Rights Reserved.

using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Utils;

namespace Phoenix.Server.Common;

public class RpcReqMessage : IMessage
{
    public RpcReqMessage(string correlationId, RpcRequestInfo reqCtx, string? svcName, string? funcName, object[]? paramList, MsgPackStructBase? reqMessage)
    {
        CorrelationId = correlationId;
        ReqCtx = reqCtx;
        SvcName = svcName;
        FuncName = funcName;
        ParamList = paramList;
        ReqMessage = reqMessage;
    }

    public override string ToString()
    {
        if (ReqMessage != null)
        {
            return $"ReqId={ReqCtx.ReqId} SvcName={SvcName} FunName={FuncName} ParamList={ParamList?.JoinWithSeparator()} ReqMessage={ReqMessage}";
        }

        return $"ReqId={ReqCtx.ReqId} SvcName={SvcName} FunName={FuncName} ParamList={ParamList?.JoinWithSeparator()}";
    }

    public string CorrelationId { get; private set; }

    public RpcRequestInfo ReqCtx { get; private set; }

    public string? SvcName { get; private set; }

    public string? FuncName { get; private set; }

    public object[]? ParamList { get; private set; }

    public MsgPackStructBase? ReqMessage { get; private set; }
}

public sealed class ActorTickMessage : IMessage
{
    public static readonly ActorTickMessage Instance = new ActorTickMessage();
    private ActorTickMessage()
    {
    }

    public string CorrelationId { get; } = "";
}
