// Copyright (c) Phoenix All Rights Reserved.

namespace Phoenix.Server.Common;

public class ActorActionMessage : IMessage
{
    public ActorActionMessage(string correlationId, Func<Task> action)
    {
        CorrelationId = correlationId;
        Action = action;
    }

    public override string ToString()
    {
        return $"Target={Action.Target} Method={Action.Method.Name}";
    }

    public Func<Task> Action { get; }
    public string CorrelationId { get; }
}
