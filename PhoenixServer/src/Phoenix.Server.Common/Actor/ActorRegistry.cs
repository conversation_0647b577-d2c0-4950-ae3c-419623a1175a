// Copyright (c) Phoenix.All Rights Reserved.

using System.Collections.Concurrent;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Phoenix.Server.Utils;

namespace Phoenix.Server.Common.Actor;

public sealed class ActorRegistry : SingletonBase<ActorRegistry>
{
    public void RegisterActor<T>(IActor<T> actor)
    {
        if (actor is null)
        {
            throw new ArgumentNullException(nameof(actor));
        }

        if (typeof(T) == typeof(string))
        {
            if (actor is IActor<string> actorWithStringKey && !m_actorsWithStringKey.TryAdd(actorWithStringKey.GetActorId(), actorWithStringKey))
            {
                s_logger.LogError("Actor with key={key} already registered.", actorWithStringKey.GetActorId());
            }
        }
        else
        {
            if (actor is IActor<long> actorWithLongKey && !m_actorsWithLongKey.TryAdd(actorWithLongKey.GetActorId(), actorWith<PERSON>ong<PERSON><PERSON>))
            {
                s_logger.LogError("Actor with key={key} already registered.", actorWithLongKey.GetActorId());
            }
        }
    }

    public void UnRegisterActor(dynamic actorId)
    {
        if (actorId is string)
        {
            m_actorsWithStringKey.TryRemove(actorId, out IActor<string> _);
        }
        else if (actorId is long)
        {
            m_actorsWithLongKey.TryRemove(actorId, out IActor<long> _);
        }
    }

    public IActor<string>? GetActor(string actorId)
    {
        m_actorsWithStringKey.TryGetValue(actorId, out IActor<string>? actor);
        return actor;
    }

    public IActor<long>? GetActor(long actorId)
    {
        m_actorsWithLongKey.TryGetValue(actorId, out IActor<long>? actor);
        return actor;
    }

    /// <summary>
    /// Get statistics for all registered actors
    /// </summary>
    /// <returns>A string containing actor statistics</returns>
    public string GetActorStatistics()
    {
        int totalActorCount = m_actorsWithStringKey.Count + m_actorsWithLongKey.Count;
        int stringKeyActorCount = m_actorsWithStringKey.Count;
        int longKeyActorCount = m_actorsWithLongKey.Count;

        // Count by type
        var actorCountByType = new Dictionary<string, int>();
        // Count by type and state
        var actorCountByTypeAndState = new Dictionary<string, Dictionary<string, int>>();

        // Count string key actors
        foreach (var actor in m_actorsWithStringKey.Values)
        {
            string typeName = actor.GetType().Name;
            // Count by type
            if (!actorCountByType.TryGetValue(typeName, out int count))
            {
                actorCountByType[typeName] = 1;
                actorCountByTypeAndState[typeName] = new Dictionary<string, int>();
            }
            else
            {
                actorCountByType[typeName] = count + 1;
            }

            // Count by state
            int stateValue = actor.GetState();
            string state = stateValue switch
            {
                0 => "Init",
                1 => "Active",
                2 => "Inactive",
                _ => $"Unknown({stateValue})"
            };

            var stateDict = actorCountByTypeAndState[typeName];
            if (!stateDict.TryGetValue(state, out int stateCount))
            {
                stateDict[state] = 1;
            }
            else
            {
                stateDict[state] = stateCount + 1;
            }
        }

        // Count long key actors
        foreach (var actor in m_actorsWithLongKey.Values)
        {
            string typeName = actor.GetType().Name;
            // Count by type
            if (!actorCountByType.TryGetValue(typeName, out int count))
            {
                actorCountByType[typeName] = 1;
                actorCountByTypeAndState[typeName] = new Dictionary<string, int>();
            }
            else
            {
                actorCountByType[typeName] = count + 1;
            }

            // Count by state
            int stateValue = actor.GetState();
            string state = stateValue switch
            {
                0 => "Init",
                1 => "Active",
                2 => "Inactive",
                _ => $"Unknown({stateValue})"
            };

            var stateDict = actorCountByTypeAndState[typeName];
            if (!stateDict.TryGetValue(state, out int stateCount))
            {
                stateDict[state] = 1;
            }
            else
            {
                stateDict[state] = stateCount + 1;
            }
        }

        // Build result string
        var result = new System.Text.StringBuilder();
        result.AppendLine($"Total Actor Count: {totalActorCount}");
        result.AppendLine($"String Key Actor Count: {stringKeyActorCount}");
        result.AppendLine($"Long Key Actor Count: {longKeyActorCount}");
        result.AppendLine();

        result.AppendLine("Statistics by Type:");
        foreach (var typeStat in actorCountByType.OrderByDescending(x => x.Value))
        {
            result.AppendLine($"  {typeStat.Key}: {typeStat.Value}");

            // Add state statistics for this type
            if (actorCountByTypeAndState.TryGetValue(typeStat.Key, out var stateDict))
            {
                foreach (var stateStat in stateDict.OrderByDescending(x => x.Value))
                {
                    result.AppendLine($"    - {stateStat.Key}: {stateStat.Value}");
                }
            }
        }

        return result.ToString();
    }

    private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<ActorRegistry>();

    private readonly ConcurrentDictionary<string, IActor<string>> m_actorsWithStringKey = new();
    private readonly ConcurrentDictionary<long, IActor<long>> m_actorsWithLongKey = new();
}
