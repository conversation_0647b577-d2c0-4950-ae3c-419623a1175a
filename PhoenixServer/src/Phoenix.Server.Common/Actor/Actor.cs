// Copyright (c) Phoenix.All Rights Reserved.

using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Threading.Channels;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using Phoenix.Server.Utils;

namespace Phoenix.Server.Common.Actor;

public abstract class Actor<T> : IActor<T>
{
    protected Actor()
    {
        TaskCreationOptions creationOptions = TaskCreationOptions.DenyChildAttach;
        if (IsLongRunning)
        {
            creationOptions |= TaskCreationOptions.LongRunning;
        }
        m_workTask = Task.Factory.StartNew(WorkProcess, CancellationToken.None, creationOptions, TaskScheduler.Default);
    }

    public abstract T GetActorId();

    public int GetState()
    {
        return m_state;
    }

    public void PostMessage(IMessage message)
    {
        m_messageQueue.Writer.TryWrite(message);
    }

    public void PostAction(Func<Task> action)
    {
        m_messageQueue.Writer.TryWrite(new ActorActionMessage("", action));
    }

    protected virtual Task ProcessMessage(IMessage message)
    {
        return message switch
        {
            ClientMessage clientMessage => HandleClientMessage(clientMessage),
            RpcReqMessage rpcReqMessage => HandleRpcReqMessage(rpcReqMessage),
            ActorActionMessage actorActionMessage => actorActionMessage.Action(),
            ActorTickMessage => ProcessTick(),
            _ => Task.CompletedTask
        };
    }

    protected virtual Task HandleClientMessage(ClientMessage clientMessage)
    {
        s_logger.LogError("Not HandleClientMessage, ActorId={ActorId}, Message={Message}", GetActorId(), clientMessage);
        return Task.CompletedTask;
    }

    protected virtual Task HandleRpcReqMessage(RpcReqMessage rpcMessage)
    {
        // handle rpc message
        if (rpcMessage.ReqMessage != null)
        {
            return RpcModuleMethodFinder.Instance.ExecuteModuleMethod(rpcMessage.SvcName!, rpcMessage.FuncName!, rpcMessage.ReqCtx, rpcMessage.ReqMessage);
        }
        else
        {
            return RpcModuleMethodFinder.Instance.ExecuteModuleMethod(rpcMessage.SvcName!, rpcMessage.FuncName!, rpcMessage.ParamList!);
        }
    }

    protected virtual async Task ProcessTick()
    {
        await Tick();
    }

    protected virtual Task Tick()
    {
        return Task.CompletedTask;
    }

    public virtual Task Active()
    {
        if (Interlocked.CompareExchange(ref m_state, ActorState.Active, ActorState.Init) != ActorState.Init)
        {
            return Task.CompletedTask;
        }

        ActorRegistry.Instance.RegisterActor(this);

        if (TickInternal > 0)
        {
            m_tickTimerName = GetActorId()!.ToString();
            s_actorTimerManager.Schedule(m_tickTimerName!, () =>
            {
                PostMessage(ActorTickMessage.Instance);
            }, TimeSpan.Zero, TimeSpan.FromMilliseconds(TickInternal));
        }
        s_logger.LogDebug("Actor Active, ActorId={ActorId}", GetActorId());
        return Task.CompletedTask;
    }

    public virtual async Task Inactive()
    {
        if (Interlocked.CompareExchange(ref m_state, ActorState.Inactive, ActorState.Active) != ActorState.Active)
        {
            return;
        }

        if (m_tickTimerName != null)
        {
            s_actorTimerManager.Cancel(m_tickTimerName);
        }

        m_messageQueue.Writer.TryComplete();
        await m_workTask;

        ActorRegistry.Instance.UnRegisterActor(GetActorId()!);
        s_logger.LogDebug("Actor Inactive, ActorId={ActorId}", GetActorId());
    }

    protected virtual Task OnWorkException(Exception ex)
    {
        s_logger.LogError("WorkException={Exception}", ex.StackTrace);
        return Task.CompletedTask;
    }

    protected virtual Task OnMessageException(Exception ex, IMessage message)
    {
        s_logger.LogError("MessageException={Exception} StackTrace={ExceptionStackTrace}", ex.Message, ex.StackTrace);
        return Task.CompletedTask;
    }

    private async Task<bool> WorkProcess()
    {
        try
        {
            var sw = Stopwatch.StartNew();
            await foreach (var message in m_messageQueue.Reader.ReadAllAsync())
            {
                m_lastProcessedMessage = message;
                m_lastProcessedTime = DateTime.Now;
                m_loggerCorrelationId = message.CorrelationId;
                // using (LogContext.PushProperty(LogPropertyNameConstants.CorrelationId, m_loggerCorrelationId))
                {
                    try
                    {
                        sw.Restart();
                        if (message is not ActorTickMessage)
                        {
                            s_logger.LogTrace("[Actor ProcessMessage Begin], ActorId={ActorId} Message={Message}", GetActorId(), message);
                        }
                        await ProcessMessage(message).ConfigureAwait(false);
                        if (message is not ActorTickMessage)
                        {
                            s_logger.LogTrace("[Actor ProcessMessage End], ActorId={ActorId} Message={Message}", GetActorId(), message);
                        }

                        sw.Stop();
                        if (sw.ElapsedMilliseconds > 20000) // 记录处理时间超过100ms的消息
                        {
                            s_logger.LogWarning("Slow message processing: {Message} took {ElapsedMs}ms, ActorId={ActorId}",
                                message, sw.ElapsedMilliseconds, GetActorId());
                        }
                    }
                    catch (Exception ex)
                    {
                        sw.Stop();
                        s_logger.LogError(ex, "Error processing message: {MessageType}, ElapsedTime={ElapsedMs}ms, ActorId={ActorId}",
                            message.GetType().Name, sw.ElapsedMilliseconds, GetActorId());
                        await OnMessageException(ex, message).ConfigureAwait(false);
                        break;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            await OnWorkException(ex);
        }

        return true;
    }

    private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<Actor<T>>();

    private string m_loggerCorrelationId = null!;
        // 替换现有的无界队列
    private readonly Channel<IMessage> m_messageQueue =
    Channel.CreateBounded<IMessage>(new BoundedChannelOptions(1000)
    {
        FullMode = BoundedChannelFullMode.Wait,
        SingleWriter = false,
        SingleReader = true
    });

    private static readonly TimerManager s_actorTimerManager = new TimerManager(TimeSpan.FromMilliseconds(100), 512, -1);

    private IMessage? m_lastProcessedMessage;
    private DateTime m_lastProcessedTime;

    protected int TickInternal;
    protected bool IsLongRunning = false;
    private readonly Task m_workTask;
    private string? m_tickTimerName;
    private int m_state;
}
