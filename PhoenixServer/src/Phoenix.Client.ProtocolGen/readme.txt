========================================================================
    生成文件项目：LibClientProtocolGen 项目概述
========================================================================

应用程序向导已为您创建了此 LibClientProtocolGen 项目。

本文件概要介绍组成 LibClientProtocolGen 项目的每个文件的内容。


LibClientProtocolGen.vcxproj
    这是使用应用程序向导生成的 VC++ 项目的主项目文件，其中包含生成该文件的 Visual C++ 的版本信息，以及有关使用应用程序向导选择的平台、配置和项目功能的信息。

LibClientProtocolGen.vcxproj.filters
    这是使用“应用程序向导”生成的 VC++ 项目筛选器文件。它包含有关项目文件与筛选器之间的关联信息。在 IDE 中，通过这种关联，在特定节点下以分组形式显示具有相似扩展名的文件。例如，“.cpp”文件与“源文件”筛选器关联。

此项目允许通过调用在向导中输入的命令在 Visual Studio 中进行生成/清除/重新生成。生成命令可以是 nmake 或任何其他所用的工具。

此项目不包含任何文件，因此在“解决方案资源管理器”没有任何显示。

/////////////////////////////////////////////////////////////////////////////
