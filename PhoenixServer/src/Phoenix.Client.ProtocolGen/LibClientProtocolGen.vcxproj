<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{D7770264-95DA-4DB3-BDF1-AC36E3C04DB6}</ProjectGuid>
    <Keyword>MakeFileProj</Keyword>
    <WindowsTargetPlatformVersion>10.0.16299.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v141</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Makefile</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v141</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <NMakePreprocessorDefinitions>WIN32;_DEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakeBuildCommandLine>nmake all SolutionDir=$(SolutionDir) SolutionPath=$(SolutionPath) DevEnvDir="$(DevEnvDir)</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>nmake clean all SolutionDir=$(SolutionDir) SolutionPath=$(SolutionPath) DevEnvDir="$(DevEnvDir)</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>nmake clean SolutionDir=$(SolutionDir) SolutionPath=$(SolutionPath) DevEnvDir="$(DevEnvDir)</NMakeCleanCommandLine>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <NMakePreprocessorDefinitions>WIN32;NDEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <NMakeBuildCommandLine>nmake all SolutionDir=$(SolutionDir) SolutionPath=$(SolutionPath) DevEnvDir="$(DevEnvDir)</NMakeBuildCommandLine>
    <NMakeReBuildCommandLine>nmake clean all SolutionDir=$(SolutionDir) SolutionPath=$(SolutionPath) DevEnvDir="$(DevEnvDir)</NMakeReBuildCommandLine>
    <NMakeCleanCommandLine>nmake clean SolutionDir=$(SolutionDir) SolutionPath=$(SolutionPath) DevEnvDir="$(DevEnvDir)</NMakeCleanCommandLine>
  </PropertyGroup>
  <ItemDefinitionGroup>
  </ItemDefinitionGroup>
  <ItemGroup>
    <Text Include="readme.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="MakeFile" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>