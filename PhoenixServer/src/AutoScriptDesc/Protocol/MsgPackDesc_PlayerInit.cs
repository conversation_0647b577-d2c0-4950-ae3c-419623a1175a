namespace Phoenix.AutoScriptDesc
{
    [MsgPackModule(CategoryType = EProtoCategoryType.Protocol, ProtoStart = 1000, ProtoEnd = 1499)]
    public class MsgPackDesc_PlayerInit { }

    [MsgPackClass(HasToken = true, Source = ProtocolSource.Client, IsShared = true)]
    public class PlayerInfoInitReq
    {
    }

    [MsgPackClass(HasToken = true)]
    public class PlayerInfoInitAck
    {
        [MsgPackProperty]
        public int ErrCode;
    }


    [MsgPackClass(HasToken = true, Source = ProtocolSource.Client)]
    public class CharacterCreateReq
    {
        [MsgPackProperty]
        public string NickName;
    }

    [MsgPackClass(HasToken = true)]
    public class CharacterCreateAck
    {
        [MsgPackProperty]
        public int ErrCode;
    }

    [MsgPackClass]
    [DataClass]
    public class TestInfo
    {
        [MsgPackProperty]
        [DataProperty]
        public string id;

        [DataProperty]
        [MsgPackProperty]
        public int score;
    }

    [MsgPackClass]
    [DataClass]
    public class PlayerCompDataBasicInfo
    {
        [MsgPackProperty]
        [DataProperty(BsonElement = "Name")]
        public string Name;

        [MsgPackProperty]
        [DataProperty(BsonElement = "Level")]
        public int Level;

        [MsgPackProperty]
        [DataProperty(BsonElement = "Exp")]
        public int Exp;

        [MsgPackProperty]
        [DataProperty(BsonElement = "PlayerId")]
        public Int64 PlayerId;

        [MsgPackProperty]
        [DataProperty(BsonElement = "Gender")]
        public int Gender;

        // [MsgPackProperty]
        // [DataProperty(BsonElement = "Test")]
        // public TestInfo TestInfo;
        //
        // [MsgPackProperty]
        // [DataProperty(BsonElement = "T")]
        // public DateTime CreateTime;
    }

    [MsgPackClass]
    public class PlayerBasicCompDataNtf
    {
        [MsgPackProperty]
        public PlayerCompDataBasicInfo BasicInfo;
    }

    [MsgPackClass(IsShared = true)]
    public class PlayerDataSectionSyncEndNtf
    {
    }
}
