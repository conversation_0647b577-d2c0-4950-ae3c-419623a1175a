using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Phoenix.AutoScriptDesc
{
    [MsgPackModule(CategoryType = EProtoCategoryType.Protocol, ProtoStart = 3000, ProtoEnd = 3999)]
    public class MsgPackDesc_Hakoniwa { }

    /// <summary>
    /// 坐标
    /// </summary>
    [MsgPackClass]
    public class PosInfo
    {
        [MsgPackProperty][DataProperty] public float X;
        [MsgPackProperty][DataProperty] public float Y;
        [MsgPackProperty][DataProperty] public float Z;
    }


    /// <summary>
    /// 任务完成条件
    /// </summary>
    [MsgPackClass]
    public class HakoniwaQuestCondInfo
    {
        /// <summary>
        /// 当前进度
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public int Progress { get; set; }

        /// <summary>
        /// 条件类型
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public int CondType { get; set; }

        /// <summary>
        /// 目标进度
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public int TargetProgress { get; set; }
    }

    /// <summary>
    /// 箱庭任务数据
    /// </summary>
    [MsgPackClass]
    public class HakoniwaQuestInfo
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public int QuestId { get; set; }

        /// <summary>
        /// 任务状态 0:未开始 1:进行中 2:已完成 3:已失败
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public int QuestState { get; set; }

        /// <summary>
        /// 任务完成条件列表
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public List<HakoniwaQuestCondInfo> QuestCondInfos = new();

        /// <summary>
        /// 任务开始时间
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public long StartTime { get; set; }

        /// <summary>
        /// 任务完成时间
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public long CompleteTime { get; set; }
    }

    /// <summary>
    /// 箱庭宝箱数据
    /// </summary>
    [MsgPackClass]
    public class HakoniwaTreasureInfo
    {
        /// <summary>
        /// 宝箱ID
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public int TreasureId { get; set; }

        /// <summary>
        /// 宝箱类型
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public int TreasureType { get; set; }
    }

    /// <summary>
    /// 当前正在探索的箱庭
    /// </summary>
    [MsgPackClass]
    public class ExploringHakoniwaInfo
    {
        /// <summary>
        /// 箱庭ID
        /// </summary>
        [MsgPackProperty] [DataProperty] public int HakoniwaId;

        /// <summary>
        /// 进行到的任务
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public List<HakoniwaQuestInfo> Quests = new();

        /// <summary>
        /// 箱庭开始时间
        /// </summary>
        [MsgPackProperty] [DataProperty]
        public DateTime CreateTime;

        /// <summary>
        /// 解锁的地点ID列表
        /// </summary>
        [MsgPackProperty] [DataProperty] public List<int> UnlockLocationIds = new();

        [MsgPackProperty] [DataProperty] public int SceneId;
        [MsgPackProperty] [DataProperty] public PosInfo PosInfo;
    }

    /// <summary>
    /// 已完成的箱庭数据
    /// </summary>
    [MsgPackClass]
    public class CompletedHakoniwaInfo
    {
        /// <summary>
        /// 箱庭故事ID
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public int HakoniwaId { get; set; }

        /// <summary>
        /// 完成的任务列表
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public List<int> CompletedQuestIds = new();
    }

    /// <summary>
    /// 箱庭总数据
    /// </summary>
    [MsgPackClass]
    [DataClass]
    public class PlayerCompDataHakoniwaInfo
    {
        /// <summary>
        /// 当前正在探索的箱庭列表
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public List<ExploringHakoniwaInfo> ExploringHakoniwaList = new ();

        /// <summary>
        /// 已完成的箱庭数据列表
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public List<CompletedHakoniwaInfo> CompletedHakoniwaList = new ();
    }

    [MsgPackClass]
    public class PlayerHakoniwaCompDataNtf
    {
        [MsgPackProperty]
        public PlayerCompDataHakoniwaInfo HakoniwaInfo;
    }

    /// <summary>
    /// 新建一个箱庭
    /// </summary>
    [MsgPackClass(HasToken = true, Source = ProtocolSource.Client)]
    public class HakoniwaCreateReq
    {
        [MsgPackProperty] public int HakoniwaId; // 箱庭ID
    }

    [MsgPackClass]
    public class HakoniwaCreateAck
    {
        [MsgPackProperty] public int ErrCode; // 错误码
        [MsgPackProperty] public int HakoniwaId; // 箱庭ID
        [MsgPackProperty] public ExploringHakoniwaInfo HakoniwaInfo; // 箱庭信息
    }

    /// <summary>
    /// 重新进入箱庭
    /// </summary>
    [MsgPackClass(HasToken = true, Source = ProtocolSource.Client)]
    public class HakoniwaEnterReq
    {
        [MsgPackProperty] public int HakoniwaId; // 箱庭ID
        [MsgPackProperty] public int EntryLocationId; // 进入的地点ID
    }

    [MsgPackClass]
    public class HakoniwaEnterAck
    {
        [MsgPackProperty] public int ErrCode; // 错误码
        [MsgPackProperty] public int HakoniwaId; // 箱庭ID
        [MsgPackProperty] public int EntryLocationId; // 进入的地点ID
    }

    [MsgPackClass(HasToken = true, Source = ProtocolSource.Client)]
    public class HakoniwaGiveUpReq
    {
        /// <summary>
        /// 箱庭ID
        /// </summary>
        [MsgPackProperty] public int HakoniwaId;
    }

    [MsgPackClass]
    public class HakoniwaGiveUpAck
    {
        /// <summary>
        /// 错误码
        /// </summary>
        [MsgPackProperty] public int ErrCode;
        /// <summary>
        /// 箱庭ID
        /// </summary>
        [MsgPackProperty] public int HakoniwaId;
    }
}
