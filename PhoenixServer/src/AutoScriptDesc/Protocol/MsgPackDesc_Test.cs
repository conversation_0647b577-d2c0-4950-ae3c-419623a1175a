// Copyright (c) Phoenix All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Phoenix.AutoScriptDesc
{
    [MsgPackModule(CategoryType = EProtoCategoryType.Protocol, ProtoStart = 50, ProtoEnd = 99)]
    public class MsgPackDesc_Test { }

    [MsgPackClass(HasToken = true)]
    public class MsgPack_Test_Req
    {
        [MsgPackProperty]
        public Int64 UidTarget;
    }

    [MsgPackClass(HasToken = true)]
    public class MsgPack_Test_Ack
    {
        [MsgPackProperty]
        public int ErrCode;
    }

    [MsgPackClass(HasToken = true, Source = ProtocolSource.Client)]
    public class MsgPack_Echo_Req
    {
        [MsgPackProperty]
        public string Content;
    }

    [MsgPackClass(HasToken = true)]
    public class MsgPack_Echo_Ack
    {
        [MsgPackProperty]
        public string Content;
    }
}

