using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Phoenix.AutoScriptDesc
{
    [MsgPackModule(CategoryType = EProtoCategoryType.Protocol, ProtoStart = 2000, ProtoEnd = 2999)]
    public class MsgPackDesc_Word { }

    /// <summary>
    /// 世界信息数据段通知
    /// </summary>
    [MsgPackClass]
    public class PlayerWorldCompDataNtf
    {
        /// <summary>
        /// 世界信息
        /// </summary>
        [MsgPackProperty] public PlayerCompDataWorldInfo WorldInfo;
    }

    [MsgPackClass]
    [DataClass]
    public class PlayerCompDataWorldInfo
    {
        /// <summary>
        /// 已完成的故事列表
        /// </summary>
        [MsgPackProperty]
        [DataProperty]
        public List<int> CompletedStoryIds = new();
    }
}
