//using Phoenix.ConfigData;

using System;

namespace Phoenix.AutoScriptDesc
{
    [MsgPackModule(CategoryType = EProtoCategoryType.Protocol, ProtoStart = 4000, ProtoEnd = 4999)]
    public class MsgPackDesc_Battle { }


    [MsgPackClass(HasToken = true, Source = ProtocolSource.Client)]
    public class MsgPack_BattleVerify_Req
    {
        //[MsgPackProperty]
        //public Phoenix.Battle.BattleRecord battleRecord;
    }

    [MsgPackClass(HasToken = true, Source = ProtocolSource.Client)]
    public class MsgPack_BattleVerify_Ack
    {
        [MsgPackProperty]
        public SByte Result;
    }
}
