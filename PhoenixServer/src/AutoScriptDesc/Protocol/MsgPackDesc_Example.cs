using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Phoenix.AutoScriptDesc
{
    [MsgPackModule(CategoryType = EProtoCategoryType.Protocol, ProtoStart = 0, ProtoEnd = 49)]
    public class MsgPackDesc_Example { }

    [MsgPackClass(HasToken = true, Source = ProtocolSource.Client)]
    public class MsgPack_Example_Req
    {
        [MsgPackProperty]
        public Int64 UidTarget;
    }

    [MsgPackClass(HasToken = true)]
    public class MsgPack_Example_Ack
    {
        [MsgPackProperty]
        public int ErrCode;
    }

    [MsgPackClass]
    public class MsgPack_ExampleData_Ntf
    {
        [MsgPackProperty]
        public MsgPack_DB_ExampleData ExampleData;
    }

    [MsgPackClass]
    [DataClass]
    public class MsgPack_DB_ExampleData
    {
        [MsgPackProperty]
        [DataProperty]
        public SByte SByteValue;

        [MsgPackProperty]
        [DataProperty]
        public Byte ByteValue;

        [MsgPackProperty]
        [DataProperty]
        public Int16 Int16Value;

        [MsgPackProperty]
        [DataProperty]
        public UInt16 UInt16Value;

        [MsgPackProperty]
        [DataProperty]
        public Int32 Int32Value;

        [MsgPackProperty]
        [DataProperty]
        public UInt32 UInt32Value;

        [MsgPackProperty]
        [DataProperty]
        public Int64 Int64Value;

        [MsgPackProperty]
        [DataProperty]
        public UInt64 UInt64Value;

        [MsgPackProperty]
        [DataProperty]
        public float FloatValue;

        [MsgPackProperty]
        [DataProperty]
        public Single SingleValue;

        [MsgPackProperty]
        [DataProperty]
        public Double DoubleValue;

        [MsgPackProperty(DefaultValue = "AAA")]
        [DataProperty]
        public String StringValue;

        [MsgPackProperty(DefaultValue = false)]
        [DataProperty]
        public Boolean BooleanValue;

        [MsgPackProperty]
        [DataProperty]
        public List<UInt64> ListExample;

        [MsgPackProperty]
        [DataProperty]
        public Dictionary<UInt64, Double> DictExample;

        [MsgPackProperty]
        [DataProperty]
        public List<MsgPack_DB_Item> ListItemExample;

        [MsgPackProperty]
        [DataProperty]
        public Dictionary<String, MsgPack_DB_Item> DictItemExample;
    }

    [MsgPackClass(HasToken = true, Source = ProtocolSource.Client)]
    public class MsgPack_ExampleExternal_Req
    {
        [MsgPackProperty]
        public Boolean IsExternal;
    }

    [MsgPackClass(HasToken = true)]
    public class MsgPack_ExampleExternal_Ack
    {
        [MsgPackProperty]
        public int ErrCode;

        [MsgPackProperty]
        public String strExternalInfo;
    }
}
