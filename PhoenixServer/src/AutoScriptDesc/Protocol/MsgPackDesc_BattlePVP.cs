//using Phoenix.Battle;

using System;

namespace Phoenix.AutoScriptDesc
{
    [MsgPackModule(CategoryType = EProtoCategoryType.Protocol, ProtoStart = 6000, ProtoEnd = 6999)]
    public class MsgPackDesc_BattlePVP { }


    [MsgPackClass(HasToken = true, Source = ProtocolSource.Client)]
    public class MsgPack_BattleCommand_Req
    {
        [MsgPackProperty]
        public byte[] Command;
    }


    [MsgPackClass(HasToken = true)]
    public class MsgPack_BattleCommand_Ack
    {
        [MsgPackProperty]
        public Int64 ErrCode;
    }

    [MsgPackClass]
    public class MsgPack_BattleCommand_Ntf
    {
        [MsgPackProperty]
        public byte[] Command;
    }
}
