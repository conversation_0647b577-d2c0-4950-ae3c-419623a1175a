// Copyright (c) Phoenix All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Phoenix.AutoScriptDesc;

[MsgPackModule(CategoryType = EProtoCategoryType.Protocol, ProtoStart = 7000, ProtoEnd = 7999)]
public class MsgPackDesc_Bag { }

[MsgPackClass(HasToken = true, Source = ProtocolSource.Client)]
public class MsgPack_BagInfo_Req
{
    [MsgPackProperty]
    public Int64 Uid;
}

[MsgPackClass(HasToken = true)]
public class MsgPack_BagInfo_Ack
{
    [MsgPackProperty]
    public int ErrCode;

    [MsgPackProperty]
    public MsgPack_DB_BagData BagData;
}

[MsgPackClass]
[DataClass]
public class MsgPack_DB_BagData
{
    [MsgPackProperty]
    [DataProperty]
    public List<MsgPack_DB_Item> ListItemExample;

    [MsgPackProperty]
    [DataProperty]
    public Dictionary<String, MsgPack_DB_Item> DictItemExample;
}

