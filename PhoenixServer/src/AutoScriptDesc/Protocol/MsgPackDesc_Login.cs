namespace Phoenix.AutoScriptDesc
{
    [MsgPackModule(CategoryType = EProtoCategoryType.Protocol, ProtoStart = 800, ProtoEnd = 999)]
    public class MsgPackDesc_Login { }

    [MsgPackClass(HasToken = true, Source = ProtocolSource.Client)]
    public class LoginByAuthTokenReq
    {
        [MsgPackProperty]
        public string AuthToken;

        [MsgPackProperty]
        public string ClientVersion;

        [MsgPackProperty]
        public string ClientDeviceId;

        [MsgPackProperty]
        public string Localization;

        [MsgPackProperty]
        public string AesKey;

    }


    [MsgPackClass(HasToken = true)]
    public class LoginByAuthTokenAck
    {
        [MsgPackProperty]
        public int ErrCode;

        [MsgPackProperty]
        public string SessionToken;
    }

    [MsgPackClass(HasToken = true, Source = ProtocolSource.Client)]
    public class LoginBySessionTokenReq
    {
        [MsgPackProperty]
        public string SessionToken;

        [MsgPackProperty]
        public string ClientVersion;

        [MsgPackProperty]
        public string Localization;

        [MsgPackProperty]
        public string GameService;
    }

    [MsgPackClass(HasToken = true)]
    public class LoginBySessionTokenAck
    {
        [MsgPackProperty]
        public int ErrCode;

        [MsgPackProperty]
        public string GameService;

        [MsgPackProperty]
        public Byte XORCode;
    }

    [MsgPackClass]
    public class ServerDisconnectNtf
    {
        [MsgPackProperty]
        public int ReasonCode;
    }

    [MsgPackClass]
    public class MsgPack_Logout_Req
    {

    }
}
