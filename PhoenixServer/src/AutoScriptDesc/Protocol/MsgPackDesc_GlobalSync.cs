// Copyright (c) Phoenix All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Phoenix.AutoScriptDesc;
[MsgPackModule(CategoryType = EProtoCategoryType.Protocol, ProtoStart = 65000, ProtoEnd = 65535)]
public class MsgPackDesc_GlobalSync { }

[MsgPackClass(Source = ProtocolSource.Client, IsShared = true)]
public class MsgPack_HeartBeat_Req
{
}

[MsgPackClass]
public class MsgPack_HeartBeat_Ack
{
    [MsgPackProperty]
    public Int64 CurrentServerTime;
}

[MsgPackClass(Source = ProtocolSource.Client)]
public class MsgPack_ServerTime_Req
{
}

[MsgPackClass]
public class MsgPack_ServerTime_Ack
{
    [MsgPackProperty]
    public Int64 CurrentServerTime;
}


