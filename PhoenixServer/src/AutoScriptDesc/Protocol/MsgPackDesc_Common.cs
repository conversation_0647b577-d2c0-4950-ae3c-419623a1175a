// Copyright (c) Phoenix All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Phoenix.AutoScriptDesc
{
    [MsgPackModule(CategoryType = EProtoCategoryType.Protocol, ProtoStart = 1500, ProtoEnd = 1599)]
    public class MsgPackDesc_Common { }

    [MsgPackClass]
    [DataClass]
    public class MsgPack_DB_Item
    {
        [MsgPackProperty]
        [DataProperty]
        public Int64 Id;

        [MsgPackProperty]
        [DataProperty]
        public Int32 Type;

        [MsgPackProperty]
        [DataProperty]
        public Int64 Count;

        [MsgPackProperty]
        [DataProperty]
        public String Name;
    }
}

