using System;

namespace Phoenix.AutoScriptDesc
{
    // ==================== DB Data Class Property Start ====================
    public enum ExportTargetType
    {
        All = 0x0,
        Server = 0x1,
        Client = 0x2,
    }

    public enum BsonFlags
    {
        None = 0x0,
        BsonIgnore = 0x01,
        BsonId = 0x02,
    }

    public enum SerializeFlags
    {
        None = 0x0,
        SerializeIgnore = 0x01,
        SerializeObject = 0x02,
        SerializeKeyAll = 0x03,
        SerializeKeyServer = 0x04,
        SerializeKeyClient = 0x05,
    }

    public enum PersistFlags
    {
        None = 0x0,
        PersistImmediately = 0x1,
    }

    public enum OPLogFlags
    {
        None = 0x0,
        SyncAll = 0x1,
        SyncServer = 0x2,
        SyncClient = 0x3,
    }

    [AttributeUsage(AttributeTargets.Class)]
    public class DataClassAttribute : Attribute
    {
        public string ClassType { get; set; }

        public string ParentClassType { get; set; }

        public string InterfaceClassType { get; set; }

        public SerializeFlags SerializeFlag { get; set; }

        public Boolean IsPartial { get; set; }

        public ExportTargetType ExportType { get; set; }
    }

    [AttributeUsage(AttributeTargets.Field | AttributeTargets.Property)]
    public class DataPropertyAttribute : Attribute
    {
        public string KeyWords { get; set; }

        public string PropertyType { get; set; }

        public BsonFlags BsonFlag { get; set; }

        public string BsonElement { get; set; }

        public PersistFlags PersistFlag { get; set; }

        public SerializeFlags SerializeFlag { get; set; }

        public OPLogFlags OPLogFlag { get; set; }

        public object DefaultValue { get; set; }

        public bool NewObject { get; set; }
    }

    // ==================== DB Data Class Property End ====================


}
