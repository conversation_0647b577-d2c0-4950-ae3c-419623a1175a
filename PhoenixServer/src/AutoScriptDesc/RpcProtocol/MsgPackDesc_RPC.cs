// Copyright (c) Phoenix.All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Phoenix.AutoScriptDesc;

[MsgPackModule(CategoryType = EProtoCategoryType.RpcProtocol, ProtoStart = 600, ProtoEnd = 799)]
public class MsgPackDesc_RPC { }

[MsgPackClass(IsPartial = true)]
public class ForwardClientMessageToServerNtf // C_FORWARD_CLIENT_MSG
{
    [MsgPackProperty]
    public Int64 PlayerId = 0;

    [MsgPackProperty]
    public UInt32 FromGate = 0;

    [MsgPackProperty]
    public int MsgProtoCode;

    [MsgPackProperty]
    public byte[] Data = null;
}

[MsgPackClass(IsPartial = true)]
public class ForwardMessageToClientNtf // S_FORWARD_CLIENT_MSG
{
    [MsgPackProperty]
    public Int64 PlayerId = 0;

    [MsgPackProperty]
    public UInt32 SrcHost = 0;

    [MsgPackProperty]
    public int MsgProtoCode;

    [MsgPackProperty]
    public byte[] Data = null;

    [MsgPackProperty]
    public Byte Flag = 0;
}

[MsgPackClass]
public class MsgPack_S_ForwardClientMsgBroadcast_Proto // S_FORWARD_CLIENT_MSG_BROADCAST
{
    [MsgPackProperty]
    public List<Int64> UidList = null;

    [MsgPackProperty]
    public Int32 SrcHost = 0;

    [MsgPackProperty]
    public byte[] Data = null;

    [MsgPackProperty]
    public bool IsBroadcastAll;
}


