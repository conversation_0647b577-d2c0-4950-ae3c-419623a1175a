// Copyright (c) Phoenix.All Rights Reserved.

using System;
using System.Collections.Generic;

namespace Phoenix.AutoScriptDesc;

[MsgPackModule(CategoryType = EProtoCategoryType.RpcProtocol, ProtoStart = 200, ProtoEnd = 399)]
public class MsgPackDesc_Core { }

[MsgPackClass]
public class MsgPack_S_CheckConsistentDigest_Proto // S_CHECK_CONSISTENT_DIGEST
{
    [MsgPackProperty]
    public List<MsgPack_S_CheckConsistentDigestInfo> Digests = new();
}

[MsgPackClass]
public class MsgPack_S_CheckConsistentDigestInfo // S_CONSISTENT_DIGEST_INFO
{
    [MsgPackProperty]
    public string Role;
    [MsgPackProperty]
    public uint Digest;
}

[MsgPackClass]
public class MsgPack_S_EngineRpc_Proto // S_ENGINE_RPC_RESPONSE
{

}

[MsgPackClass]
public class MsgPack_S_RpcConnect_Proto //S_RPC_CONNECT
{
    [MsgPackProperty]
    public Int32 Magic = 0;
    [MsgPackProperty]
    public byte[] AuthToken = null;
    [MsgPackProperty]
    public MsgPack_S_ServerNodeInfo_Proto NodeInfo = null;
    [MsgPackProperty]
    public Int64 ProcId = 0;
    [MsgPackProperty]
    public bool IsReady;
}

[MsgPackClass]
public class MsgPack_S_RpcEstablish_Proto // S_RPC_ESTABLISH
{
    [MsgPackProperty]
    public UInt32 NodeId = 0;

    [MsgPackProperty]
    public Int32 ErrorCode = 0;
}

[MsgPackClass]
public class MsgPack_S_GetConsistentDigest_Proto // S_GET_CONSISTENT_DIGEST
{
    [MsgPackProperty]
    public string Role;
}

[MsgPackClass]
public class MsgPack_S_NodesReady_Proto // S_NODES_READY
{
    [MsgPackProperty]
    public List<UInt32> ReadyNodeList = new();
}

[MsgPackClass]
public class MsgPack_S_NodeHeartbeat_Req // S_NODE_HEARTBEAT
{
    [MsgPackProperty]
    public Int64 Timestamp;
}

[MsgPackClass]
public class MsgPack_S_NodeHeartbeat_Ack // S_NODE_HEARTBEAT_RESP
{
    [MsgPackProperty]
    public Int64 Timestamp;
}

[MsgPackClass]
public class MsgPack_S_NodeInfoList_Proto // S_NODE_INFO_LIST
{
    [MsgPackProperty]
    public List<MsgPack_S_ServerNodeInfo_Proto> NodeInfoList = new();
}

[MsgPackClass]
public class MsgPack_S_NodeJoin_Ntf // S_NODE_JOIN_NOTIFY
{
    [MsgPackProperty]
    public MsgPack_S_ServerNodeInfo_Proto NodeInfo;
}

[MsgPackClass]
public class MsgPack_S_NodeLeave_Ntf // S_NODE_LEAVE_NOTIFY
{
    [MsgPackProperty]
    public UInt32 NodeId;


}

[MsgPackClass]
public class MsgPack_S_NodeReady_Proto // S_NODE_READY
{
    [MsgPackProperty]
    public UInt32 NodeId;
}

[MsgPackClass]
public class MsgPack_S_RegisterMailBox_Req // S_REGISTER_MAILBOX
{
    [MsgPackProperty]
    public string MailboxName;

    [MsgPackProperty]
    public UInt32 NodeId;

    [MsgPackProperty]
    public bool IsOverride;

    [MsgPackProperty]
    public bool IsBroadcastRegister;
}

[MsgPackClass]
public class MsgPack_S_RegisterMailBox_Ack // S_REGISTER_MAILBOX_RESP
{
    [MsgPackProperty]
    public string MailboxName;

    [MsgPackProperty]
    public int ErrorCode;

    [MsgPackProperty]
    public UInt32 RouterNodeId;
}

[MsgPackClass]
public class MsgPack_S_RpcByMailBox_Proto // S_RPC_BY_MAILBOX
{
    [MsgPackProperty]
    public string MailboxName;

    [MsgPackProperty]
    public MsgPack_S_Rpc_Req RpcReq;
}

[MsgPackClass]
public class MsgPack_S_Rpc_Req //S_RPC_REQUEST
{
    [MsgPackProperty]
    public Int64 ReqId;

    [MsgPackProperty]
    public Int32 Flag;

    [MsgPackProperty]
    public UInt32 SrcNode;

    [MsgPackProperty]
    public UInt32 HiddenSrcNode;

    [MsgPackProperty]
    public string Uri;

    [MsgPackProperty]
    public string Func;

    [MsgPackProperty]
    public Int32 MsgProtoCode;

    [MsgPackProperty]
    public byte[] Data;
}

[MsgPackClass]
public class MsgPack_S_Rpc_Ack //S_RPC_RESPONSE
{
    [MsgPackProperty]
    public Int64 ReqId = 0;

    [MsgPackProperty]
    public Int32 Flag = 0;

    [MsgPackProperty]
    public Int32 ErrorCode = 0;

    [MsgPackProperty]
    public UInt32 ReqNode = 0;

    [MsgPackProperty]
    public UInt32 HiddenReqNode = 0;

    [MsgPackProperty]
    public UInt32 RespNodeId = 0;

    [MsgPackProperty]
    public Int32 MsgProtoCode = 0;

    [MsgPackProperty]
    public byte[] RespData = null;
}

[MsgPackClass]
public class MsgPack_S_RpcToNode_Proto // S_RPC_TO_NODE
{
    [MsgPackProperty]
    public UInt32 DestNodeId = 0;

    [MsgPackProperty]
    public MsgPack_S_Rpc_Req RpcReq;
}

[MsgPackClass]
public class MsgPack_S_RpcToPlayer_Proto // S_RPC_TO_PLAYER
{
    [MsgPackProperty]
    public bool IsSpacePlayer;

    [MsgPackProperty]
    public Int64 Uid = 0;

    [MsgPackProperty]
    public MsgPack_S_Rpc_Req RpcReq;
}

[MsgPackClass]
public class MsgPack_S_RpcToPlayerRouter_Proto // S_RPC_TO_PLAYER_ROUTER
{
    [MsgPackProperty]
    public bool IsSpacePlayer;

    [MsgPackProperty]
    public Int64 Uid = 0;

    [MsgPackProperty]
    public MsgPack_S_Rpc_Req RpcReq = null;
}

[MsgPackClass]
public class MsgPack_S_ServerNodeInfo_Proto // S_SERVER_NODE_INFO
{
    [MsgPackProperty]
    public UInt32 NodeId = 0;

    [MsgPackProperty]
    public String Role = "";

    [MsgPackProperty]
    public String Ip = "";

    [MsgPackProperty]
    public UInt32 Port = 0;

    [MsgPackProperty]
    public UInt32 RealmId = 0;

    [MsgPackProperty]
    public bool IsReady;

    [MsgPackProperty]
    public byte[] ExtraData = null;

    [MsgPackProperty]
    public String Tag = "";
}

[MsgPackClass]
public class MsgPack_S_TransUpdateConsistentHash_Req // S_TRANS_UPDATE_CONSISTENT_HASH
{
    [MsgPackProperty]
    public Int64 TransId = 0;

    [MsgPackProperty]
    public byte TransState = 0;

    [MsgPackProperty]
    public string Role = "";

    [MsgPackProperty]
    public byte[] ConsistentHashData = null;
}

[MsgPackClass]
public class MsgPack_S_TransUpdateConsistentHash_Ack // S_TRANS_UPDATE_CONSISTENT_HASH_ACK
{
    [MsgPackProperty]
    public Int64 TransId = 0;

    [MsgPackProperty]
    public byte TransState = 0;

    [MsgPackProperty]
    public string Role = "";
}

[MsgPackClass]
public class MsgPack_S_UnregisterMailBox_Req // S_UNREGISTER_MAILBOX
{
    [MsgPackProperty]
    public String MailboxName = "";
}

[MsgPackClass]
public class MsgPack_S_UnregisterMailBox_Ack //S_UNREGISTER_MAILBOX_RESP
{
    [MsgPackProperty]
    public string MailboxName = "";

    [MsgPackProperty]
    public Int32 ErrorCode = 0;
}

[MsgPackClass]
public class MsgPack_S_UpdateConsistentHash_Proto // S_UPDATE_CONSISTENT_HASH
{
    [MsgPackProperty]
    public string Role = "";

    [MsgPackProperty]
    public byte[] ConsistentHashData = null;
}

[MsgPackClass]
public class MsgPack_S_BroadcastData_Proto //S_BROADCAST_DATA
{
    [MsgPackProperty]
    public string Key;

    [MsgPackProperty]
    public byte[] Data;
}
