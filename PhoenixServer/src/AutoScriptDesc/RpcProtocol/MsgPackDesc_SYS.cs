// Copyright (c) Phoenix.All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Phoenix.AutoScriptDesc;

[MsgPackModule(CategoryType = EProtoCategoryType.RpcProtocol, ProtoStart = 400, ProtoEnd = 599)]
public class MsgPackDesc_SYS { }


[MsgPackClass]
public class MsgPack_C_CallBasePlayerMethod_Proto // C_CALL_BASE_PLAYER_METHOD
{
    [MsgPackProperty]
    public String Func = "";

    [MsgPackProperty]
    public byte[] Args = null;

    [MsgPackProperty]
    public Int32 FuncIndex = 0;
}

[MsgPackClass]
public class MsgPack_C_CallEntityMethod_Proto // C_CALL_ENTITY_METHOD
{
    [MsgPackProperty]
    public Int64 EntityId = 0;

    [MsgPackProperty]
    public string Func = "";

    [MsgPackProperty]
    public byte[] Args = null;

    [MsgPackProperty]
    public Int32 FuncIndex = 0;

    [MsgPackProperty]
    public SByte ForwardType = 0;
}


[MsgPackClass]
public class MsgPack_S_BasicEntityCreate_Proto // S_BASIC_ENTITY_CREATE
{
    [MsgPackProperty]
    public Int64 EntityId = 0;

    [MsgPackProperty]
    public string Classname = "";

    [MsgPackProperty]
    public Int64 PrefabId = 0;

    [MsgPackProperty]
    public byte[] Initdata = null;

    [MsgPackProperty]
    public string ModelId = "";
}

[MsgPackClass]
public class MsgPack_S_CallClientMethod_Proto // S_CALL_CLIENT_METHOD
{
    [MsgPackProperty]
    public Int64 EntityId = 0;

    [MsgPackProperty]
    public string FuncOrIndex = "";

    [MsgPackProperty]
    public byte[] Args = null;
}

[MsgPackClass]
public class MsgPack_S_EntityCreawte_Proto // S_ENTITY_CREATE
{
    [MsgPackProperty]
    public Int64 EntityId = 0;

    [MsgPackProperty]
    public string Classname = "";

    [MsgPackProperty]
    public byte[] Initdata = null;
}

[MsgPackClass]
public class MsgPack_S_EntityDestroy_Proto // S_ENTITY_DESTROY
{
    [MsgPackProperty]
    public Int64 EntityId = 0;
}

[MsgPackClass]
public class MsgPack_S_EntityMigrate_Proto // S_ENTITY_MIGRATE
{

}

[MsgPackClass]
public class MsgPack_S_EntityMigrateReply_Proto // S_ENTITY_MIGRATE_REPLY
{

}

[MsgPackClass]
public class MsgPack_S_ForwardEntityRpc_Proto // S_FORWARD_ENTITY_RPC
{
    [MsgPackProperty]
    public Int64 EntityId = 0;

    [MsgPackProperty]
    public Int64 Uid = 0;

    [MsgPackProperty]
    public string Funcname = "";

    [MsgPackProperty]
    public byte[] ArgsData = null;
}


