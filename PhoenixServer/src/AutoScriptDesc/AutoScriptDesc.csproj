<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>Phoenix.AutoScriptDesc</AssemblyName>
    <RootNamespace>Phoenix.AutoScriptDesc</RootNamespace>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="BasicComponentDesc.cs" />
    <Compile Remove="BattleInitDataDesc.cs" />
    <Compile Remove="BattleRecordDesc.cs" />
    <Compile Remove="BattleTeamDecisionConditionInfoDesc.cs" />
    <Compile Remove="BattleTeamDecisionDataDesc.cs" />
    <Compile Remove="BattleTeamDecisionInfoDesc.cs" />
    <Compile Remove="BattleTeamDecisionItemDataDesc.cs" />
    <Compile Remove="BattleTeamDecisionItemInfoDesc.cs" />
    <Compile Remove="BattleTeamDecisionOriginSelectInfoDesc.cs" />
    <Compile Remove="BattleTeamDecisionSkillSelectInfoDesc.cs" />
    <Compile Remove="DBItemDesc.cs" />
    <Compile Remove="DBPlayerBagDesc.cs" />
    <Compile Remove="DBPlayerDataBasicDesc.cs" />
    <Compile Remove="DBPlayerDataDesc.cs" />
    <Compile Remove="PlayerInitDataDesc.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MongoDB.Bson" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ConfigData\ConfigData.csproj" />
    <ProjectReference Include="..\BattleExecuter\BattleExecuter.csproj" />
    <ProjectReference Include="..\PropSourceGeneratorClient\PropSourceGeneratorClient.csproj" />
  </ItemGroup>

</Project>
