using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Phoenix.AutoScriptDesc
{
    [AttributeUsage(AttributeTargets.Class)]
    public class ComponentClassAttribute : Attribute
    {
        public string ClassType { get; set; }

        public string ParentClassType { get; set; }

        public string ComponentName { get; set; }

        public string OwnerEntity { get; set; }

        public string LinkData { get; set; }
    }

    [AttributeUsage(AttributeTargets.Field)]
    public class ComponentPropertyAttribute : Attribute
    {
        
    }
}
