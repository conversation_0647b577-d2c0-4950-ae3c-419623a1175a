using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Phoenix.AutoScriptDesc
{
    public enum EProtoCode
    {
        // C2S Client to Server
        // S2C Server to Client

        // 0 ~ 999
        COMMON_START = 0,
    }

    public enum EMsgPackOpType
    {
        // Do nothing
        OP_NULL = 0,

        // Excute default protocol logic
        OP_DEFAULT,

        // Call Client <-> Server Func
        OP_FUNC,

        // Clear all elements
        OP_CLEAR,

        // Erase key (for array, the key is index)
        OP_ERASE,

        // Set key-value
        OP_UPDATE,

        // Array operation
        OP_A_APPEND,
        OP_A_INSERT,
        OP_BITSET_SET,
        OP_BITSET_RESET,
        OP_SET_ADD,
        OP_SET_REMOVE,


        OP_END
    }

    public enum EProtoCategoryType
    {
        Invalid = 0, //无效协议
        Protocol = 1, //客户端协议
        RpcProtocol = 2, //服务器间协议
    }

    [AttributeUsage(AttributeTargets.Class)]
    public class MsgPackModuleAttribute : Attribute
    {
        public EProtoCategoryType CategoryType
        {
            get;
            set;
        }

        public Int32 ProtoStart
        {
            get;
            set;
        }

        public Int32 ProtoEnd
        {
            get;
            set;
        }
    }

    [AttributeUsage(AttributeTargets.Class)]
    public class MsgPackClassAttribute : Attribute
    {
        public Boolean HasToken
        {
            get;
            set;
        }

        public ProtocolSource Source
        {
            get;
            set;
        } = ProtocolSource.Internal;

        public Boolean IsShared
        {
            get;
            set;
        }

        public Boolean IsPartial
        {
            get;
            set;
        }
    }

    [AttributeUsage(AttributeTargets.Field | AttributeTargets.Property)]
    public class MsgPackPropertyAttribute : Attribute
    {
        public string PropertyType
        {
            get;
            set;
        }

        public SerializeFlags SerializeFlag
        {
            get;
            set;
        }

        public object DefaultValue
        {
            get;
            set;
        }

        public bool NewObject
        {
            get;
            set;
        }
    }

    public enum ProtocolSource
    {
        /// <summary>
        /// 客户端发起的协议
        /// </summary>
        Client = 0,

        /// <summary>
        /// 服务器发起的协议
        /// </summary>
        Server = 1,

        /// <summary>
        /// 双向协议（客户端和服务器都可以发起）
        /// </summary>
        Bidirectional = 2,

        /// <summary>
        /// 内部协议（仅在服务器内部使用）
        /// </summary>
        Internal = 3
    }
}
