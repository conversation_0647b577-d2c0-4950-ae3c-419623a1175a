using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mos.AutoScriptDesc
{
    public enum TeamDecisionOriginSelectPatternType
    {
        None = 0,
        Index = 1,
        Fit = 2,
    }

    public enum EntityElementId
    {
        None = 0,
        Metal = 1,
        Wood = 2,
        Water = 3,
        Fire = 4,
        Earth = 5,
    }

    public enum EntityCareerId
    {
        None = 0,
        Warrior = 1,
        <PERSON> = 2,
        Fighter = 3,
        Assassin = 4,
        Skywarrior = 5,
        Immortal = 6,
        <PERSON> = 7,
        <PERSON><PERSON> = 8,
        Physician = 9,
        <PERSON> = 10,
    }

    public enum EntityRespType
    {
        None = 0,
        Attack = 1,
        Deffend = 2,
        Assist = 3,
    }

    public enum TeamDecisionSkillSelectType
    {
        None = 0,
        Priority = 1,
        Rid = 2,
        Tag = 3,
    }

    public enum TeamDecisionConditionFuncId
    {
        None = 0,
        TargetActorRid = 1,
        TargetCampRef = 2,
        TargetCareer = 3,
        TargetHpValue = 4,
        TargetHpRate = 5,
        TargetPhysicalAttack = 6,
        TargetPhysicalDeffence = 7,
        TargetMagicalAttack = 8,
        TargetMagicalDeffence = 9,
        TargetDistance = 10,
        TargetInDanger = 11,
        TargetOutAssistGuard = 12,
        TargetCountInSkillRange = 13,
        TargetCountInjuredInSkillRange = 14,
        TargetCounterDistLessThanSkillRange = 15,
        TargetMark = 16,
        TargetElementRef = 17,
        SelfEntityRepc = 18,
        SelfHpRate = 19,
        SelfTeamEnergy = 20,
        SelfLeftEntityCount = 21,
        SelfCareerNotExist = 22,
        SelfFriendInDanger = 23,
        PosOrigin = 24,
        PosDistanceToTarget = 25,
        PosDistanceToOrigin = 26,
        PosOutDanger = 27,
        PosInAssistGuard = 28,
        PosDistanceToMark = 29,
        PosAssistGuardCount = 30,
        PosActorCountInRhombRange = 31,
        PosDistanceToActor = 32,
    }

    public enum BattleCampRefType
    {
        None = 0,
        Friendly = 1,
        Rival = 2,
    }

    public enum TeamDecisionMarkId
    {
        None = 0,
        Focus = 1,
        Control = 2,
        Protect = 3,
        Move = 4,
    }

    public enum TeamDecisionCompareType
    {
        None = 0,
        Less = 1,
        Greater = 2,
        LessEqual = 3,
        GreaterEqual = 4,
        Smallest = 5,
        Greatest = 6,
    }

    public enum ElementRefType
    {
        None = 0,
        Strong = 1,
        Weak = 2,
        Same = 3,
        Different = 4,
    }

    public enum TeamDecisionConditionRuleType
    {
        None = 0,
        Priority = 1,
        Only = 2,
        Most = 3,
        Condition = 4,
    }

    public enum SkillTagType
    {
        None = 0,
        AttackSingle = 1,
        AttackArea = 2,
        HealSingle = 3,
        HealArea = 4,
        AssistGuard = 5,
        AttachBuff = 6,
        AttachDebuff = 7,
        NormalAttack = 8,
        Summon = 9,
        Wanguifan = 10,
        BossAoe = 11,
    }

}
