// Copyright (c) Phoenix.All Rights Reserved.

using Phoenix.Server.CommonDataStructure;
using Phoenix.Server.Utils;

namespace Phoenix.Server.World;

internal sealed class PlayerProxy
{
    public long PlayerId;
    public PlayerProxyChannel BaseChannel = new();
    public string LoginKey = string.Empty;

    public PlayerProxy(long playerId, PlayerState lOADING, uint gameNodeId = 0, uint gateNodeId = 0)
    {
        PlayerId = playerId;
        BaseChannel.GameNodeId = gameNodeId;
        BaseChannel.GateNodeId = gateNodeId;
    }

    internal void ResetLoginKey()
    {
        // reset every time when client relogin
        LoginKey = IdGenerator.Instance.GenGUID_String();
    }
}
