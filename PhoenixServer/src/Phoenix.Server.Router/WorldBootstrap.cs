// Copyright (c) Phoenix.All Rights Reserved.

using Microsoft.Extensions.Hosting;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Common;
using Phoenix.Server.Router;
using Phoenix.Server.Utils;

namespace Phoenix.Server.World;

public class WorldBootstrap : SingletonBase<WorldBootstrap>, IHostedService
{
    private void BindDelegates()
    {
        RpcService.Instance.DelegateOnRpcMessageRequest += OnRpcMessageRequest;
        RpcService.Instance.DelegateOnRpcRequest += OnRpcRequest;
        RpcService.Instance.RegisterPlayerRouterFunc((playerId) => WorldPlayerRegistry.Instance.GetGameBasePlayerNode(playerId), false);
        RpcService.Instance.DelegateOnNodeLeave += OnNodeLeave;
        RpcService.Instance.DelegateOnWorldHashChanged += OnWorldResharding;
    }

    private void OnWorldResharding(string hashName, ConsistentHash.ConsistentHashImmutable chash)
    {
        WorldPlayerRegistry.Instance.OnWorldResharding(hashName, chash);
    }

    private void OnNodeLeave(uint nodeId, string role)
    {
        if (role == RoleName.ROLE_GAME)
        {
            WorldPlayerRegistry.Instance.OnNodeLeave(nodeId, role);
        }
    }

    private void OnRpcRequest(string uri, string funcName, object[] paramList, RpcRequestInfo reqInfo)
    {
        object[] args = new object[paramList.Length + 1];
        args[0] = reqInfo;
        for (int i = 0; i < paramList.Length; ++i)
        {
            args[i + 1] = paramList[i];
        }
        if (uri == RpcUriConstants.WORLD_PLAYER_REGISTRY_URI)
        {
            var rpcReqMessage = new RpcReqMessage(reqInfo.CorrelationId, reqInfo, "WorldPlayerRegistry", funcName, args, null);
            WorldPlayerRegistry.Instance.PostMessage(rpcReqMessage);
        }
    }

    private void OnRpcMessageRequest(string uri, string funcName, MsgPackStructBase message, RpcRequestInfo reqInfo)
    {
        if (uri == RpcUriConstants.WORLD_PLAYER_REGISTRY_URI)
        {
            var rpcReqMessage = new RpcReqMessage(reqInfo.CorrelationId, reqInfo, "WorldPlayerRegistry", funcName, null, message);
            WorldPlayerRegistry.Instance.PostMessage(rpcReqMessage);
        }
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        BindDelegates();
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}
