// Copyright (c) Phoenix.All Rights Reserved.

using System.Collections.Concurrent;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Phoenix.GameModel.Server;
using Phoenix.Server.Common;
using Phoenix.Server.Common.Actor;
using Phoenix.Server.CommonDataStructure;
using Phoenix.Server.Model;
using Phoenix.Server.World;

// ReSharper disable UnusedMember.Local

namespace Phoenix.Server.Router;

[RpcModule(Name = "WorldPlayerRegistry")]
internal sealed class WorldPlayerRegistry : Actor<string>, IHostedService
{
    public static WorldPlayerRegistry Instance
    {
        get;
        private set;
    } = null!;

    public WorldPlayerRegistry(IGameDatabaseService gameDatabaseService)
    {
        Instance = this;
        TickInternal = 100;
        IsLongRunning = true;
        m_gameDatabaseService = gameDatabaseService;

        RpcModuleMethodFinder.Instance.RegModule(this);
    }

    public void OnWorldResharding(string hashName, ConsistentHash.ConsistentHashImmutable chash)
    {
        s_logger.LogInformation("OnWorldResharding HashName={HashName}", hashName);
        PostAction(() =>
        {
            m_lastReshardingTime = RpcService.Instance.CurrentMilliseconds;
            return Task.CompletedTask;
        });
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        await Active();
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        m_isStopped = true;
        await Inactive();
    }

    public override string GetActorId()
    {
        return "WorldPlayerRegistry";
    }

    protected override Task Tick()
    {
        TickPlayerDestroy();
        return Task.CompletedTask;
    }

    private void TickPlayerDestroy()
    {
        long now = RpcService.Instance.CurrentMilliseconds;
        foreach (var pair in m_playerId2DestroyBeginTime)
        {
            if (now - pair.Value > PlayerDestroyMaxWaitTimeMillisSeconds)
            {
                m_playerIdDestroying.Add(pair.Key);
            }
        }

        foreach (long playerId in m_playerIdDestroying)
        {
            m_playerId2DestroyBeginTime.Remove(playerId);
            RetryZombiePlayerLogout(playerId);
        }

        m_playerIdDestroying.Clear();
    }

    private void RetryZombiePlayerLogout(long playerId)
    {
        // retry destroy residual zombie player proxy which is failed last trial
        if (m_playerId2Proxy.TryGetValue(playerId, out PlayerProxy? player))
        {
            if (player.BaseChannel.State != PlayerState.BEING_DESTROYED)
            {
                s_logger.LogError(
                    "RetryZombiePlayerLogout failed. PlayerState is not destroying. PlayerId={PlayerId}, PlayerState={PlayerState}",
                    playerId, player.BaseChannel.State);
                // retry
            }
        }
    }

    public void OnNodeLeave(uint nodeId, string role)
    {
        s_logger.LogInformation("OnNodeLeave. NodeId={NodeId}, Role={Role}", nodeId, role);
        PostAction(() =>
        {
            if (role == RoleName.ROLE_GAME)
            {
                List<long> playerIdsNeedRemove = new();
                foreach (var pair in m_playerId2Proxy)
                {
                    if (pair.Value.BaseChannel.GameNodeId == nodeId)
                    {
                        playerIdsNeedRemove.Add(pair.Key);
                    }
                }

                foreach (long playerId in playerIdsNeedRemove)
                {
                    RemovePlayerProxy(playerId);
                }
            }

            return Task.CompletedTask;
        });
    }

    public uint GetGameBasePlayerNode(long playerId)
    {
        var proxy = m_playerId2Proxy.GetValueOrDefault(playerId);
        return proxy != null ? proxy.BaseChannel.GameNodeId : 0;
    }

    private PlayerState GetPlayerState(long playerId)
    {
        var proxy = m_playerId2Proxy.GetValueOrDefault(playerId);
        return proxy != null ? proxy.BaseChannel.State : PlayerState.OFFLINE;
    }

    private void SetPlayerState(long playerId, PlayerState state)
    {
        if (m_playerId2Proxy.TryGetValue(playerId, out PlayerProxy? proxy))
        {
            proxy.BaseChannel.State = state;
        }
    }

    private PlayerProxy GetOrCreatePlayerProxy(long playerId)
    {
        if (m_playerId2Proxy.TryGetValue(playerId, out PlayerProxy? playerProxy))
        {
            return playerProxy;
        }

        var proxy = new PlayerProxy(playerId, PlayerState.OFFLINE);
        m_playerId2Proxy[playerId] = proxy;
        return proxy;
    }

    private void RemovePlayerProxy(long playerId)
    {
        s_logger.LogTrace("RemovePlayerProxy. PlayerId={PlayerId}", playerId);
        m_playerId2Proxy.Remove(playerId, out _);
        m_playerId2DestroyBeginTime.Remove(playerId);
    }

    [RpcMethod(Name = "ReqWizardPlayerLogin", Role = RoleName.ROLE_GATE)]
    private Task ReqWizardPlayerLogin(RpcRequestInfo reqInfo, long playerId, string name)
    {
        Task.Run(async () =>
        {
            s_logger.LogInformation("ReqWizardPlayerLogin. PlayerId={PlayerId}, Name={Name}", playerId, name);
            var playerModel = await m_gameDatabaseService.GetPlayerAsync(playerId);
            if (playerModel == null)
            {
                playerModel = new GamePlayerData { Id = playerId };
                playerModel.Basic.PlayerId = playerId;
                playerModel.Basic.Name = name;
                try
                {
                    await m_gameDatabaseService.CreatePlayerAsync(playerModel);
                    reqInfo.SendResponse(
                        new Dictionary<object, object> { { RpcConstants.ERR_CODE, LoginErrorCode.OK } });
                }
                catch (Exception e)
                {
                    if (e is MongoDuplicateKeyException)
                    {
                        s_logger.LogError("login an existent player. PlayerId={PlayerId} Exception={Exception}",
                            playerId, e.Message);
                        reqInfo.SendResponse(new Dictionary<object, object>
                        {
                            { RpcConstants.ERR_CODE, LoginErrorCode.ERR_DUPLICATE }
                        });
                        return;
                    }

                    s_logger.LogError("insert_newborn_failed. PlayerId={PlayerId} Exception={Exception}", playerId,
                        reqInfo.NodeId);
                    reqInfo.SendResponse(new Dictionary<object, object>
                    {
                        { RpcConstants.ERR_CODE, LoginErrorCode.ERR_DB_FAIL }
                    });
                }
            }
            else
            {
                reqInfo.SendResponse(new Dictionary<object, object> { { RpcConstants.ERR_CODE, LoginErrorCode.OK } });
            }
        });
        return Task.CompletedTask;
    }

    [RpcMethod(Name = "ReqPlayerLogin", Role = RoleName.ROLE_GATE)]
    private async Task ReqPlayerLogin(RpcRequestInfo reqInfo, long playerId, uint gameNodeId)
    {
        /*
           RPC reqeust player login to game.
             Two usual login situation：
             1. PlayerState.OFFLINE -> PlayerState.LOADING -> PlayerState.ONLINE
             2. PlayerState.ONLINE -> PlayerState.REBINDING -> PlayerState.ONLINE
             other situation
             3. PlayerState.LOADING -> reject
             4. PlayerState.REBINDING -> reject
        */
        if (m_isStopped)
        {
            s_logger.LogInformation("ReqPlayerLogin failed, ERR_REQ_IN_ZONE_SHUTDOWN, PlayerId={PlayerId} ", playerId);

            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_REQ_IN_ZONE_SHUTDOWN }
            });
            return;
        }

        if (playerId == 0)
        {
            s_logger.LogInformation("ReqPlayerLogin failed, ERR_INVALID_ARGS, PlayerId={PlayerId} ", playerId);
            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_INVALID_ARGS }
            });
            return;
        }

        var playerState = GetPlayerState(playerId);
        s_logger.LogInformation("ReqPlayerLogin, PlayerId={PlayerId}, playerState={playerState}", playerId,
            playerState);

        UInt32 nodeId = RpcService.Instance.PickNode(RpcService.Instance.MyNode.NodeInfo.Role, playerId);
        if (nodeId != RpcService.Instance.MyNode.NodeInfo.NodeId)
        {
            s_logger.LogInformation(
                "ReqPlayerLogin failed, ERR_WORLD_RESHARDING, PlayerId={PlayerId}, PlayerState={PlayerState}, ExpectNode={ExpectNode}",
                playerId, playerState, nodeId);

            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_WORLD_RESHARDING }
            });
            return;
        }

        if (playerState == PlayerState.OFFLINE && RpcService.Instance.CurrentMilliseconds -
            m_lastReshardingTime < WaitReshardingTimeMillisSeconds)
        {
            // waiting for player proxy recovery just in cluster failover process
            // reject login request
            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_WORLD_RESHARDING }
            });
            return;
        }

        // dispatch according to current login state transition.-------------------------
        if (playerState == PlayerState.OFFLINE)
        {
            GetOrCreatePlayerProxy(playerId);
            SetPlayerState(playerId, PlayerState.LOADING);
            _ = Task.Run(async () =>
            {
                await LoginOnStateOffline(reqInfo, playerId, gameNodeId);
            });
        }
        else if (playerState == PlayerState.ONLINE)
        {
            SetPlayerState(playerId, PlayerState.REBINDING);
            // player request relogin by another client
            _ = Task.Run(async () =>
            {
                await LoginOnStateOnline(reqInfo, playerId);
            });
        }
        else if (playerState == PlayerState.LOADING)
        {
            // When the player on the game has not been loaded, another login request is received
            s_logger.LogError(
                "ReqPlayerLogin failed, player_loading. PlayerId={PlayerId}, FromGateNodeId={FromGateNodeId}", playerId,
                reqInfo.NodeId);
            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_PLAYER_IS_LOADING }
            });
        }
        else if (playerState == PlayerState.REBINDING)
        {
            s_logger.LogError(
                "ReqPlayerLogin failed, player_rebind_in_progress, PlayerId={PlayerId}, FromGateNodeId={FromGateNodeId}",
                playerId, reqInfo.NodeId);
            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_PLAYER_IS_REBINDING }
            });
        }
        else if (playerState == PlayerState.MIGRATING)
        {
            s_logger.LogError(
                "ReqPlayerLogin failed, player_migrating, PlayerId={PlayerId}, FromGateNodeId={FromGateNodeId}",
                playerId, reqInfo.NodeId);
            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_PLAYER_IS_MIGRATING }
            });
        }
        else if (playerState == PlayerState.BEING_DESTROYED)
        {
            s_logger.LogError("ReqPlayerLogin failed, player_being_destroyed, PlayerId={PlayerId}", playerId);
            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_PLAYER_IS_DESTROYING }
            });
        }
        else
        {
            s_logger.LogError("ReqPlayerLogin failed. unknown player state, playerState={playerState}", playerState);
            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_PLAYER_STATE }
            });
        }
    }

    private async Task LoginOnStateOffline(RpcRequestInfo reqInfo, long playerId, uint gameNodeId)
    {
        /*
          Most usual player login situation.
          When requesting login, the player is offline(not in any game node), this is the most usual case for player login.
            :param req: use to reply rpc
            :param uid: use to get PlayerProxy
            :param need_login: use to offine load
            :param game_node: use to login on the specified game
        */

        // Step 1: allocate a game_node to create player
        if (gameNodeId == 0)
        {
            gameNodeId = await PlayerNodeAllocator.AllocateGameNodeForPlayer(playerId);
            if (gameNodeId == 0)
            {
                s_logger.LogError("allocate game node failed, no game node available, PlayerId={PlayerId}", playerId);
                RemovePlayerProxy(playerId);
                reqInfo.SendResponse(new Dictionary<object, object>
                {
                    { RpcConstants.ERR_CODE, LoginErrorCode.ERR_NO_GAME_NODE_AVAILABLE }
                });
                return;
            }
        }

        // Step 2: create player in game
        var rpcTarget = RpcManager.CreateNodeTarget(RpcUriConstants.BASE_PLAYER_MGR_URI, gameNodeId);
        var resp = await RpcManager.Instance.AsyncCall(rpcTarget, "ReqCreatePlayer",
            new object[] { playerId, reqInfo.NodeId });
        if (resp.RetCode != RpcErrCode.RPC_NO_ERROR)
        {
            s_logger.LogError("ReqCreatePlayer failed, PlayerId={PlayerId} RetCode={RetCode}", playerId, resp.RetCode);
            RemovePlayerProxy(playerId);
            return;
        }

        var res = resp.Res as Dictionary<object, object>;
        if (res != null && resp.RetCode != RpcErrCode.RPC_NO_ERROR || (int)res![RpcConstants.ERR_CODE] != 0)
        {
            OnLoadPlayerOnGameFailed(resp, playerId, reqInfo, gameNodeId);
            return;
        }

        // check player exist after async load_player
        var playerProxy = m_playerId2Proxy[playerId];
        s_logger.LogInformation(
            "load player on game succeed. PlayerId={PlayerId} ReqNodeId={ReqNodeId} GameNodeId={GameNodeId}", playerId,
            reqInfo.NodeId, gameNodeId);

        // once the base player is loaded in game, it is regared as online in world_router perspective,
        // no mathers whether the client connection is established or not
        playerProxy.BaseChannel.State = PlayerState.ONLINE;
        playerProxy.BaseChannel.GameNodeId = gameNodeId;
        playerProxy.BaseChannel.GateNodeId = reqInfo.NodeId;
        // Final Step: tell base channel gate to finish login
        ResponseGateLogin(reqInfo, playerProxy, LoginErrorCode.OK);
    }

    private async Task LoginOnStateOnline(RpcRequestInfo reqInfo, long playerId)
    {
        /* login rebinding

         cause request is from other client connection. kickout the old client

         */
        // GameBasePlayer is created and is online. login request from other gate is received
        // so remove the old client_session first
        var player = m_playerId2Proxy[playerId];

        uint oldGateNodeId = player.BaseChannel.GateNodeId;

        var target = RpcManager.CreateNodeTarget(RpcUriConstants.GATE_CLIENT_SESSION_MGR_URI, oldGateNodeId);
        // even from the same gate, unbind player client first.
        s_logger.LogInformation(
            "player relogin, will unbind old connection, PlayerId={PlayerId}, OldGateNodeId={OldGateNodeId}, ReqGateNodeId={ReqGateNodeId}",
            playerId, oldGateNodeId, reqInfo.NodeId);
        var resp = await RpcManager.Instance.AsyncCall(target, "UnbindPlayerClient", [playerId, false]);
        if (resp.RetCode != RpcErrCode.RPC_NO_ERROR)
        {
            s_logger.LogError(
                "player relogin, will unbind old connection, unbind player client error. PlayerId={PlayerId}, OldGateNodeId={OldGateNodeId}, ReqGateNodeId={ReqGateNodeId}, ret_code={resp.ret_code}",
                playerId, oldGateNodeId, reqInfo.NodeId, resp.RetCode);
            player.BaseChannel.State = PlayerState.ONLINE;
            ResponseGateLogin(reqInfo, player, LoginErrorCode.RECONNECT_ERR_KICK_OLD_GATE_FAIL, true);
            return;
        }

        s_logger.LogInformation(
            "player relogin, will unbind old connection, PlayerId={PlayerId}, OldGateNodeId={OldGateNodeId}, ReqGateNodeId={ReqGateNodeId}",
            playerId, oldGateNodeId, reqInfo.NodeId);

        player.BaseChannel.GateNodeId = reqInfo.NodeId;
        player.BaseChannel.State = PlayerState.ONLINE;
        ResponseGateLogin(reqInfo, player, LoginErrorCode.OK, true);
    }

    private void OnLoadPlayerOnGameFailed(RpcResponseHeader resp, long playerId, RpcRequestInfo reqInfo,
        uint gameNodeId)
    {
        /* request create_player in game node timeout or failed
          usual cause:
              -game node fail(crash/ down)
              -player loading in progress or exist(which means the login request is duplicated)

            destroy the proxy context and response to gate

            */

        RemovePlayerProxy(playerId);

        int errCode = 0;
        if (resp.RetCode != 0)
        {
            errCode = LoginErrorCode.LOGIN_LOAD_PLAYER_TIMEOUT;
        }
        else
        {
            if (resp.Res is Dictionary<object, object> result)
            {
                errCode = (int)result[RpcConstants.ERR_CODE];
            }
        }

        string errMsg = LoginErrorCode.ERR_CODE_MSG.GetValueOrDefault(errCode, "UnknownErrCode");
        s_logger.LogError(
            "on_load_player_on_game_failed PlayerId={PlayerId}, GameNode={GameNode} ErrCode={ErrCode}, ErrMsg={ErrMsg}",
            playerId, gameNodeId, errCode, errMsg);

        reqInfo.SendResponse(new Dictionary<object, object> { { RpcConstants.ERR_CODE, errCode } });
    }

    private void ResponseGateLogin(RpcRequestInfo reqInfo, PlayerProxy player, int errCode, bool relogin = false)
    {
        // response final result to gate login request.

        player.BaseChannel.GateNodeId = reqInfo.NodeId;
        player.ResetLoginKey();
        string loginKey = player.LoginKey;
        s_logger.LogInformation(
            "ResponseGateLogin. PlayerId={PlayerId} GameNodeId={GameNodeId} LoginKey={LoginKey} Relogin={Relogin}",
            player.PlayerId, player.BaseChannel.GameNodeId, player.LoginKey, relogin);

        var info = new Dictionary<object, object>
        {
            { RpcConstants.ERR_CODE, errCode },
            { "game_node", player.BaseChannel.GameNodeId },
            { "login_key", loginKey },
            { "relogin", relogin }
        };
        reqInfo.SendResponse(info);
    }

    [RpcMethod(Name = "ReqPlayerLogout", Role = RoleName.ROLE_GAME)]
    private Task ReqPlayerLogout(RpcRequestInfo reqInfo, long playerId, int reasonCode)
    {
        // request player logout from game. (cause of player online keepalive timeout.)
        if (!m_playerId2Proxy.TryGetValue(playerId, out var player))
        {
            s_logger.LogError(
                "ReqPlayerLogout failed. ERR_WORLD_PLAYER_REGISTRY_NOT_FOUND, PlayerId={PlayerId}, ReqGameNodeId={ReqNodeId}",
                playerId, reqInfo.NodeId);
            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_WORLD_PLAYER_REGISTRY_NOT_FOUND }
            });
            return Task.CompletedTask;
        }

        if (player.BaseChannel.State == PlayerState.REBINDING && reasonCode == 0)
        {
            // player will become connected soon (unless reason_code != 0 for maintainence)
            s_logger.LogError(
                "ReqPlayerLogout reject. PlayerState REBINDING, PlayerId={PlayerId}, ReqGameNodeId={ReqNodeId}",
                playerId, reqInfo.NodeId);
            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_PLAYER_IS_REBINDING }
            });
            return Task.CompletedTask;
        }

        if (player.BaseChannel.State == PlayerState.BEING_DESTROYED &&
            reasonCode != LoginErrorCode.KICK_OUT_RETRY_ZOMBIE_DESTROY)
        {
            s_logger.LogError(
                "ReqPlayerLogout reject. PlayerState BEING_DESTROYED, PlayerId={PlayerId}, ReqGameNodeId={ReqNodeId}",
                playerId, reqInfo.NodeId);
            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_PLAYER_IS_DESTROYING }
            });
            return Task.CompletedTask;
        }

        // Set state to begin destroyed.
        player.BaseChannel.State = PlayerState.BEING_DESTROYED;
        // record destroying player to avoid zombie state
        m_playerId2DestroyBeginTime[playerId] = RpcService.Instance.CurrentMilliseconds;

        uint gameNode = player.BaseChannel.GameNodeId;

        // space_node = player.space_channel.player_node
        reqInfo.SendResponse(new Dictionary<object, object> { { RpcConstants.ERR_CODE, LoginErrorCode.OK } });
        s_logger.LogInformation(
            "ReqPlayerLogout. PlayerId={PlayerId}, FromNodeId={FromNodeId}, GameNodeId={GameNodeId}, spaceNode={spaceNode}",
            playerId, reqInfo.NodeId, gameNode, 0);
        return Task.CompletedTask;
    }

    [RpcMethod(Name = "ReqPlayerLogoutDone", Role = RoleName.ROLE_GAME)]
    private Task ReqPlayerLogoutDone(RpcRequestInfo reqInfo, long playerId)
    {
        // request to remove player proxy after player logout from game.
        if (!m_playerId2Proxy.TryGetValue(playerId, out var player))
        {
            s_logger.LogError(
                "ReqPlayerLogoutDone failed. player proxy not found, PlayerId={PlayerId}, FromNodeId={NodeId}",
                playerId, reqInfo.NodeId);
            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_WORLD_PLAYER_REGISTRY_NOT_FOUND }
            });
            return Task.CompletedTask;
        }

        uint gameNode = player.BaseChannel.GameNodeId;
        s_logger.LogInformation(
            "ReqPlayerLogoutDone. PlayerId={PlayerId}, FromNodeId={NodeId} GameNodeId={GameNodeId}, SpaceNode={SpaceNode}",
            playerId, reqInfo.NodeId, gameNode, 0);

        if (player.BaseChannel.State != PlayerState.BEING_DESTROYED)
        {
            s_logger.LogError(
                "ReqPlayerLogoutDone reject. PlayerState is not BEING_DESTROYED, PlayerId={PlayerId}, GameNode={GameNode}, PlayerState={PlayerState}",
                playerId, gameNode, player.BaseChannel.State);

            m_playerId2DestroyBeginTime.Remove(playerId);
            reqInfo.SendResponse(new Dictionary<object, object>
            {
                { RpcConstants.ERR_CODE, LoginErrorCode.ERR_PLAYER_STATE }
            });
            return Task.CompletedTask;
        }

        RemovePlayerProxy(playerId);

        reqInfo.SendResponse(new Dictionary<object, object> { { RpcConstants.ERR_CODE, LoginErrorCode.OK } });
        return Task.CompletedTask;
    }

    [RpcMethod(Name = "ReqRestorePlayerProxy", Role = RoleName.ROLE_GAME)]
    private Task ReqRestorePlayerProxy(RpcRequestInfo reqInfo, bool isSpacePlayerProxy,
        Dictionary<object, object> playerProxyInfo)
    {
        var info = playerProxyInfo; //  as Dictionary<object, object>;
        // restore player_proxy upload from game_node.
        //  This may happen right after some world_router nodes is down OR dynamically add a new node

        //foreach (var info in infoList)
        {
            long playerId = (long)info["player_id"];
            uint gameNodeId = (uint)info["game_node_id"];
            uint gateNodeId = (uint)info["gate_node_id"];
            var state = (PlayerState)info["state"];

            s_logger.LogInformation("ReqRestorePlayerProxy. PlayerId={PlayerId} GameNodeId={GameNodeId}",
                playerId, reqInfo.NodeId);

            if (!m_playerId2Proxy.TryGetValue(playerId, out var playerProxy))
            {
                // TODO fix
                playerProxy = new PlayerProxy(0, PlayerState.OFFLINE);
                m_playerId2Proxy[playerId] = playerProxy;
            }

            if (isSpacePlayerProxy)
            {
                /*if not player_proxy.space_channel.player_node:
                    player_proxy.space_channel.player_node = info["game_node_id"]
                    player_proxy.space_channel.gate = info["gate_node_id"]
                    player_proxy.space_channel.state = PlayerState.ONLINE*/
            }
            else
            {
                playerProxy.BaseChannel.GameNodeId = gameNodeId;
                playerProxy.BaseChannel.GateNodeId = gateNodeId;
                playerProxy.BaseChannel.State = state;
            }
        }

        reqInfo.SendResponse(new Dictionary<object, object> { { RpcConstants.ERR_CODE, LoginErrorCode.OK } });

        return Task.CompletedTask;
    }

    [RpcMethod(Name = "ReqDeletePlayerProxy", Role = RoleName.ROLE_GAME)]
    private Task ReqDeletePlayerProxy(RpcRequestInfo reqInfo, long playerId)
    {
        // delete player proxy from world_router
        s_logger.LogInformation("ReqDeletePlayerProxy. PlayerId={PlayerId}", playerId);
        RemovePlayerProxy(playerId);
        reqInfo.SendResponse(new Dictionary<object, object> { { RpcConstants.ERR_CODE, LoginErrorCode.OK } });
        return Task.CompletedTask;
    }

    [RpcMethod(Name = "ReqCheckPlayerProxy", Role = RoleName.ROLE_GAME)]
    private Task ReqCheckPlayerProxy(RpcRequestInfo reqInfo, List<long> playerIdList)
    {
        // check player proxy exist in world_router
        List<long> res = [];
        foreach (long playerId in playerIdList)
        {
            if (!m_playerId2Proxy.ContainsKey(playerId))
            {
                res.Add(playerId);
            }
        }

        if (res.Count > 0)
            s_logger.LogInformation("ReqCheckPlayerProxy. miss player PlayerIds={PlayerIds}", res);
        reqInfo.SendResponse(res);
        return Task.CompletedTask;
    }

    private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<WorldPlayerRegistry>();

    public readonly long PlayerDestroyMaxWaitTimeMillisSeconds = 120 * 1000;

    private bool m_isStopped;

    private long m_lastReshardingTime;

    private const int WaitReshardingTimeMillisSeconds = 3000;

    private readonly ConcurrentDictionary<long, PlayerProxy> m_playerId2Proxy = new();

    private readonly Dictionary<long, long> m_playerId2DestroyBeginTime = new();

    /// <summary>
    ///     The service used to interact with the game database.
    /// </summary>
    private readonly IGameDatabaseService m_gameDatabaseService;

    private readonly List<long> m_playerIdDestroying = new();
}
