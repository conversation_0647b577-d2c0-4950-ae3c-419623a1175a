// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.CommonDataStructure.Common;

/// <summary>
///     Represents an account information with authentication ID and player ID.
/// </summary>
/// <param name="AuthId">The authentication ID of the account.</param>
/// <param name="PlayerId">the player ID of the account.</param>
public sealed record AccountInfo(string AuthId, long PlayerId);
