// Copyright (c) Phoenix.  All Rights Reserved.

using Phoenix.Server.CommonDataStructure;

namespace Phoenix.Server.CommonDataStructure.Common;

/// <summary>
///     Represents a result returned by a method, containing an error code.
/// </summary>
public class ReturnResult
{
    /// <summary>
    ///     Gets or sets the error code for the result.
    /// </summary>
    /// <value>
    ///     The error code. Default value is <see cref="ErrCode.ErrCodeOk" />.
    /// </value>
    public ErrCode ErrCode { get; set; } = ErrCode.ErrCodeOk;
}
