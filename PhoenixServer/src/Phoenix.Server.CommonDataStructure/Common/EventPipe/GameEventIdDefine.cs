// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.CommonDataStructure.Common;

/// <summary>
///     Represents categories of events.
/// </summary>
[Flags]
public enum EventCategory
{
    /// <summary>
    ///     Represents an invalid category.
    /// </summary>
    Invalid = 0,

    /// <summary>
    ///     Represents a basic category.
    /// </summary>
    Basic = 0x01,

    /// <summary>
    ///     Represents a dedicated server category.
    /// </summary>
    DedicatedServer = 0x02,

    /// <summary>
    ///     Represents all categories.
    /// </summary>
    All = Basic | DedicatedServer
}

/// <summary>
///     Represents identifiers for game events.
/// </summary>
[Serializable]
public enum GameEventIdDefine
{
    None = 0,

    /// <summary>
    ///     Represents a player level up event.
    /// </summary>
    PlayerLevelUp,

    /// <summary>
    ///     Represents a player level or experience change event.
    /// </summary>
    PlayerLevelExpChange,

    /// <summary>
    ///     Represents a quest finish event.
    /// </summary>
    QuestFinish,

    /// <summary>
    ///     Represents an entity gather event.
    /// </summary>
    EntityGather,

    /// <summary>
    ///     Represents an event of adding an item to the bag.
    /// </summary>
    AddBagItem,

    /// <summary>
    ///     Represents an event of using an item.
    /// </summary>
    UseItem,

    /// <summary>
    ///     Represents a battle finished event.
    /// </summary>
    BattleFinished,

    /// <summary>
    ///     Represents an event of finishing a story.
    /// </summary>
    FinishStory,

    /// <summary>
    ///     Represents an event of entering a zone.
    /// </summary>
    EnterZone,

    /// <summary>
    ///     Represents an event of triggering an interaction.
    /// </summary>
    InteractionExTriggered,

    /// <summary>
    ///     Represents an event of activating a teleport location.
    /// </summary>
    ActivateTeleportLocationId,

    /// <summary>
    ///     Represents a currency change event.
    /// </summary>
    AddCurrency
}
