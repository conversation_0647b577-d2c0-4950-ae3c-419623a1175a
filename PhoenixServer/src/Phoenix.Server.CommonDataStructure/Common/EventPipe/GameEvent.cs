// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.CommonDataStructure.Common;

/// <summary>
///     Represents a game event.
/// </summary>
[Serializable]
public abstract class GameEvent
{
    /// <summary>
    ///     Represents the maximum number of categories.
    /// </summary>
    public const int CategoryMax = 31;

    /// <summary>
    ///     Represents the category of the event.
    /// </summary>
    public EventCategory EventCategory;

    /// <summary>
    ///     Represents the identifier of the event.
    /// </summary>
    public GameEventIdDefine EventId;

    /// <summary>
    ///     Initializes a new instance of the GameEvent class.
    /// </summary>
    /// <param name="category">The category of the event.</param>
    /// <param name="id">The identifier of the event.</param>
    protected GameEvent(EventCategory category, GameEventIdDefine id)
    {
        EventCategory = category;
        EventId = id;
        PlayerId = string.Empty;
    }

    /// <summary>
    ///     Initializes a new instance of the GameEvent class.
    /// </summary>
    /// <param name="category">The category of the event.</param>
    /// <param name="id">The identifier of the event.</param>
    /// <param name="playerId">The identifier of the player.</param>
    protected GameEvent(EventCategory category, GameEventIdDefine id, string playerId)
    {
        EventCategory = category;
        EventId = id;
        PlayerId = playerId;
    }

    /// <summary>
    ///     Gets or sets the identifier of the player.
    /// </summary>
    public string PlayerId { get; set; }
}

/// <summary>
///     Represents a default game event.
/// </summary>
[Serializable]
public class GameEventDefault<T> : GameEvent
{
    /// <summary>
    ///     Initializes a new instance of the GameEventDefault class.
    /// </summary>
    /// <param name="category">The category of the event.</param>
    /// <param name="id">The identifier of the event.</param>
    /// <param name="eventParams">The parameters of the event.</param>
    public GameEventDefault(EventCategory category, GameEventIdDefine id, params T[] eventParams)
        : base(category, id) =>
        EventParams = eventParams.ToArray();

    /// <summary>
    ///     Gets or sets the parameters of the event.
    /// </summary>
    public T[] EventParams { get; set; }
}
