// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.CommonDataStructure.Common;

/// <summary>
///     Represents an interface for game event listeners.
/// </summary>
public interface IGameEventPipeListener
{
    /// <summary>
    ///     Gets the category mask of the listener.
    /// </summary>
    /// <returns>The category mask of the listener.</returns>
    EventCategory GetCategoryMask();

    /// <summary>
    ///     Handles the received event.
    /// </summary>
    /// <param name="e">The received event.</param>
    void OnEvent(GameEvent e);
}

/// <summary>
///     Represents an abstract class for game event pipes.
/// </summary>
public abstract class GameEventPipe
{
    /// <summary>
    ///     Adds a listener to the pipe.
    /// </summary>
    /// <param name="listener">The listener to add.</param>
    public abstract void AddListener(IGameEventPipeListener listener);

    /// <summary>
    ///     Removes a listener from the pipe.
    /// </summary>
    /// <param name="listener">The listener to remove.</param>
    public abstract void RemoveListener(IGameEventPipeListener listener);

    /// <summary>
    ///     Pushes an event to the pipe.
    /// </summary>
    /// <param name="logicEvent">The event to push.</param>
    public abstract void PushEvent(GameEvent logicEvent);
}

/// <summary>
///     Represents the default implementation of a game event pipe.
/// </summary>
[Serializable]
public class GameEventPipeDefault : GameEventPipe
{
    /// <summary>
    ///     The list of listeners in the pipe.
    /// </summary>
    protected List<IGameEventPipeListener> m_listenerList = new();

    /// <summary>
    ///     Adds a listener to the pipe.
    /// </summary>
    /// <param name="listener">The listener to add.</param>
    public override void AddListener(IGameEventPipeListener listener) => m_listenerList.Add(listener);

    /// <summary>
    ///     Removes a listener from the pipe.
    /// </summary>
    /// <param name="listener">The listener to remove.</param>
    public override void RemoveListener(IGameEventPipeListener listener) => m_listenerList.Remove(listener);

    /// <summary>
    ///     Pushes an event to the pipe.
    /// </summary>
    /// <param name="logicEvent">The event to push.</param>
    public override void PushEvent(GameEvent logicEvent)
    {
        EventCategory currMask = logicEvent.EventCategory;
        // Loop through the list of listeners.
        foreach (IGameEventPipeListener listener in m_listenerList.ToList())
        {
            // If the listener's category mask matches the event's category, handle the event.
            if ((listener.GetCategoryMask() & currMask) != 0)
            {
                listener.OnEvent(logicEvent);
            }
        }
    }
}
