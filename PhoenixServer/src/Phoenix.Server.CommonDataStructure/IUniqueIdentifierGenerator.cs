// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.CommonDataStructure;

/// <summary>
///     Defines an interface for a unique identifier generator.
/// </summary>
public interface IUniqueIdentifierGenerator
{
    /// <summary>
    ///     Creates a unique identifier.
    /// </summary>
    /// <returns>A long value representing the unique identifier.</returns>
    long CreateId();
}
