// Copyright (c) Phoenix.  All Rights Reserved.

using System.Diagnostics;

namespace Phoenix.Server.CommonDataStructure;

/// <summary>
///     Represents a cache object.
/// </summary>
public class CacheObject
{
    /// <summary>
    ///     Initializes a new instance of the CacheObject class.
    /// </summary>
    public CacheObject() => Dirty = false;

    /// <summary>
    ///     Gets or sets a value indicating whether the cache is dirty.
    /// </summary>
    public bool Dirty { get; set; }
}

/// <summary>
///     Represents a cache that can be updated.
/// </summary>
/// <typeparam name="T">The type of the data in the cache.</typeparam>
public class UpdateCache<T> : CacheObject where T : class
{
    /// <summary>
    ///     Gets or sets the data in the cache.
    /// </summary>
    public T? Data;

    /// <summary>
    ///     Initializes a new instance of the UpdateCache class.
    /// </summary>
    /// <param name="data">The initial data for the cache.</param>
    public UpdateCache(T? data) => Data = data;

    /// <summary>
    ///     Checks if the cache is valid.
    /// </summary>
    /// <returns>True if the cache is valid; otherwise, false.</returns>
    public bool IsValid() => Data != null;

    /// <summary>
    ///     Sets the cache as invalid.
    /// </summary>
    public void SetInvalid()
    {
        Data = null;
        Dirty = true;
    }

    /// <summary>
    ///     Sets the data in the cache.
    /// </summary>
    /// <param name="data">The data to set.</param>
    public void SetData(T? data)
    {
        Data = data;
        Dirty = true;
    }
}

/// <summary>
///     Represents a list of caches that can be updated.
/// </summary>
/// <typeparam name="T">The type of the data in the caches.</typeparam>
public class UpdateCacheList<T> where T : class
{
    /// <summary>
    ///     The list of free indices in the cache list.
    /// </summary>
    private readonly LinkedList<int> m_freeIndices;

    /// <summary>
    ///     Initializes a new instance of the UpdateCacheList class.
    /// </summary>
    public UpdateCacheList()
    {
        Caches = new List<UpdateCache<T>>();
        m_freeIndices = new LinkedList<int>();
    }

    /// <summary>
    ///     Gets the count of caches in the list.
    /// </summary>
    public int Count => Caches.Count;

    /// <summary>
    ///     Gets the count of valid caches in the list.
    /// </summary>
    public int ValidCount => Caches.Count - m_freeIndices.Count;

    /// <summary>
    ///     Gets or sets the list of caches.
    /// </summary>
    public List<UpdateCache<T>> Caches { get; set; }

    /// <summary>
    ///     Initializes a new cache with the given data.
    /// </summary>
    /// <param name="data">The data to initialize the cache with.</param>
    /// <returns>The index of the new cache in the list.</returns>
    public int Init(T data)
    {
        int index = ValidCount;
        UpdateCache<T> cache = new(data);
        return Add(cache, false);
    }

    /// <summary>
    ///     Initializes the cache list from a list of data.
    /// </summary>
    /// <param name="datas">The list of data to initialize the cache list with.</param>
    /// <param name="validDatas">The list of valid data in the cache list.</param>
    public void Init(List<T?> datas, out List<KeyValuePair<int, T>> validDatas)
    {
        validDatas = new List<KeyValuePair<int, T>>();
        for (int i = 0; i < datas.Count; i++)
        {
            T? info = datas[i];
            UpdateCache<T> cache = new(info);
            Caches.Add(cache);
            if (info == null)
            {
                m_freeIndices.AddFirst(i);
            }
            else
            {
                validDatas.Add(new KeyValuePair<int, T>(i, info));
            }
        }
    }

    /// <summary>
    ///     Adds a new cache with the given data to the list.
    /// </summary>
    /// <param name="data">The data to add to the cache.</param>
    /// <returns>The index of the added cache in the list.</returns>
    public int Add(T data)
    {
        UpdateCache<T> cache = new(data);
        return Add(cache);
    }

    /// <summary>
    ///     Adds a cache to the list.
    /// </summary>
    /// <param name="cache">The cache to add.</param>
    /// <param name="dirty">Whether the cache is dirty.</param>
    /// <returns>The index of the added cache in the list.</returns>
    public int Add(UpdateCache<T> cache, bool dirty = true)
    {
        int index;
        if (m_freeIndices.Count == 0)
        {
            index = ValidCount;
            Caches.Add(cache);
        }
        else
        {
            index = m_freeIndices.First();
            m_freeIndices.RemoveFirst();
            Caches[index] = cache;
        }

        cache.Dirty = dirty;

        return index;
    }

    /// <summary>
    ///     Removes the cache at the given index.
    /// </summary>
    /// <param name="index">The index of the cache to remove.</param>
    /// <returns>Whether the removal was successful.</returns>
    public bool Remove(int index)
    {
        if (!IsIndexValid(index))
        {
            return false;
        }

        UpdateCache<T> cache = Caches[index];
        if (!cache.IsValid())
        {
            return false;
        }

        Remove(cache, index);
        return true;
    }

    /// <summary>
    ///     Sets the data of the cache at the given index.
    /// </summary>
    /// <param name="index">The index of the cache to set the data of.</param>
    /// <param name="data">The data to set.</param>
    /// <returns>Whether the operation was successful.</returns>
    public bool Set(int index, T data)
    {
        if (!IsIndexValid(index))
        {
            return false;
        }

        UpdateCache<T> cache = Find(index)!;
        cache.Data = data;
        cache.Dirty = true;
        return true;
    }

    /// <summary>
    ///     Removes the given cache.
    /// </summary>
    /// <param name="cache">The cache to remove.</param>
    /// <param name="index">The index of the cache to remove.</param>
    private void Remove(UpdateCache<T> cache, int index)
    {
        cache.SetInvalid();
        m_freeIndices.AddFirst(index);
    }

    /// <summary>
    ///     Finds the cache at the given index.
    /// </summary>
    /// <param name="index">The index of the cache to find.</param>
    /// <returns>The cache at the given index, or null if the index is invalid.</returns>
    public UpdateCache<T>? Find(int index)
    {
        if (!IsIndexValid(index))
        {
            return null;
        }

        return Caches[index];
    }

    /// <summary>
    ///     Clears the cache list.
    /// </summary>
    public void Clear()
    {
        UpdateCache<T> cache;
        for (int i = 0; i < Caches.Count; ++i)
        {
            cache = Caches[i];
            if (cache.IsValid())
            {
                Remove(cache, i);
            }
        }

        Debug.Assert(ValidCount == 0);
    }

    /// <summary>
    ///     Gets all valid data in the cache list.
    /// </summary>
    /// <returns>A list of all valid data in the cache list.</returns>
    public List<T> GetAllVaildDatas()
    {
        List<T> datas = new();

        foreach (UpdateCache<T> cache in Caches)
        {
            if (cache.IsValid())
            {
                datas.Add(cache.Data!);
            }
        }

        return datas;
    }

    /// <summary>
    ///     Iterates over all valid items in the cache list.
    /// </summary>
    /// <returns>An enumerator that can be used to iterate over all valid items in the cache list.</returns>
    public IEnumerable<T> IterateAllValidItems()
    {
        foreach (UpdateCache<T> cache in Caches)
        {
            if (cache.IsValid())
            {
                yield return cache.Data!;
            }
        }
    }

    /// <summary>
    ///     Gets all valid caches in the cache list.
    /// </summary>
    /// <returns>A list of all valid caches in the cache list.</returns>
    public List<UpdateCache<T>> GetAllVaildCaches()
    {
        List<UpdateCache<T>> retCaches = new();

        foreach (UpdateCache<T> cache in Caches)
        {
            if (cache.IsValid())
            {
                retCaches.Add(cache);
            }
        }

        return retCaches;
    }

    /// <summary>
    ///     Gets all dirty caches in the cache list.
    /// </summary>
    /// <returns>A list of all dirty caches in the cache list.</returns>
    public List<UpdateCache<T>> GetAllDirtyCaches()
    {
        List<UpdateCache<T>> retCaches = new();

        foreach (UpdateCache<T> cache in Caches)
        {
            if (cache.Dirty)
            {
                retCaches.Add(cache);
            }
        }

        return retCaches;
    }

    /// <summary>
    ///     Checks whether the given index is valid.
    /// </summary>
    /// <param name="index">The index to check.</param>
    /// <returns>Whether the given index is valid.</returns>
    private bool IsIndexValid(int index) => index >= 0 && index < Caches.Count;
}
