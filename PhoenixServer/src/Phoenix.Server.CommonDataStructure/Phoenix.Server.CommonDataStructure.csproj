<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>Phoenix.Server.CommonDataStructure</RootNamespace>
    <Configurations>Release;Debug</Configurations>
    <Platforms>x64</Platforms>
  </PropertyGroup>

  <PropertyGroup>
    <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
    <CompilerGeneratedFilesOutputPath>..\SourceGeneratorFiles</CompilerGeneratedFilesOutputPath>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Common\World\**" />
    <EmbeddedResource Remove="Common\World\**" />
    <None Remove="Common\World\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="IdGen" />
    <PackageReference Include="MongoDB.Bson" />
    <PackageReference Include="Riok.Mapperly" />
    <PackageReference Include="Serilog" />
  </ItemGroup>

  <!-- Code Analyzers -->
  <ItemGroup Condition=" '$(Configuration)' == 'Debug' ">
    <PackageReference Include="SerilogAnalyzer" PrivateAssets="All" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Utils\Phoenix.Server.Utils.csproj" />
  </ItemGroup>

</Project>
