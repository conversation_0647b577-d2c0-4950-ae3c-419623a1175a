// Copyright (c) Phoenix.All Rights Reserved.

namespace Phoenix.Server.CommonDataStructure;

public enum PlayerState
{
    // player is offline, not in server
    OFFLINE,

    // player is loading data from db in progress
    LOADING,

    // !!!deprecated state in world_router, a loaded state but not online is not needed anymore,
    // it will regarded as ONLINE.
    // set from loading -> online in latest login process
    // LOADED,
    // player is loaded and regard as activated in server
    ONLINE,

    // client request login from another gate while player is online in server
    REBINDING,

    // player is being destroyed, maybe doing persisting and cleanup work
    BEING_DESTROYED,

    // player object is migrating between space node in progress
    MIGRATING
}
