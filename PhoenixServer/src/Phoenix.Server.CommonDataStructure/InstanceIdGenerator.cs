// Copyright (c) Phoenix.  All Rights Reserved.

using IdGen;

namespace Phoenix.Server.CommonDataStructure;

/// <summary>
///     InstanceIdGenerator
/// </summary>
public static class InstanceIdGenerator
{
    /// <summary>
    ///     TimestampBits: 31 max value is 2147483647 time = 2147483647 / 86400 / 365 = 68 years
    ///     GeneratorIdBits: 15 max value is 32767
    ///     SequenceBits: 17 max value is 131,071 generate 131,071 id per second
    /// </summary>
    private static readonly IdStructure DefaultIdStructure = new(
        31,
        15,
        17);

    /// <summary>
    ///     epoch time 2023-01-01 00:00:00
    /// </summary>
    private static readonly DateTime Epoch = new(2023, 1, 1, 0, 0, 0, DateTimeKind.Utc);

    /// <summary>
    ///     instanceId generator by snowflake algorithm
    /// </summary>
    /// <param name="generatorId"></param>
    /// <param name="strategy"></param>
    /// <returns></returns>
    public static IIdGenerator<long> CreateInstanceIdGenerator(int generatorId,
        SequenceOverflowStrategy strategy = SequenceOverflowStrategy.SpinWait)
    {
        IdGeneratorOptions options = new()
        {
            TimeSource = new DefaultTimeSource(Epoch, TimeSpan.FromSeconds(1)),
            IdStructure = DefaultIdStructure,
            SequenceOverflowStrategy = strategy
        };

        return new IdGenerator(generatorId, options);
    }
}
