// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.CommonDataStructure;

/// <summary>
///     Represents an authentication token.
/// </summary>
public class AuthToken
{
    /// <summary>
    ///     Creates a new instance of the AuthToken class.
    /// </summary>
    /// <param name="rawToken">The raw token string.</param>
    /// <param name="isFake">Indicates whether the token is fake.</param>
    private AuthToken(string rawToken, bool isFake)
    {
        string[] szInfo = rawToken.Split('|');

        PlatformAuthId = szInfo[0];
        PlatformToken = szInfo[1];
        PlatformName = szInfo[2];
        PlatformId = szInfo[3];
        DeviceId = szInfo[4];
        Signature = szInfo[5];
    }

    /// <summary>
    ///     Gets the platform authentication ID.
    /// </summary>
    public string PlatformAuthId { get; }

    /// <summary>
    ///     Gets the device ID.
    /// </summary>
    public string DeviceId { get; }

    /// <summary>
    ///     Gets the platform token.
    /// </summary>
    public string PlatformToken { get; }

    /// <summary>
    ///     Gets the platform name.
    /// </summary>
    public string PlatformName { get; }

    /// <summary>
    ///     Gets the platform ID.
    /// </summary>
    public string PlatformId { get; }

    /// <summary>
    ///     Gets the signature.
    /// </summary>
    public string Signature { get; }

    /// <summary>
    ///     Creates a new instance of the AuthToken class from a raw token string.
    /// </summary>
    /// <param name="rawToken">The raw token string.</param>
    /// <param name="isFake">Indicates whether the token is fake.</param>
    /// <returns>A new instance of the AuthToken class.</returns>
    public static AuthToken From(string rawToken, bool isFake) => new(rawToken, isFake);

    /// <summary>
    ///     Returns a string that represents the current object.
    /// </summary>
    /// <returns>A string that represents the current object.</returns>
    public override string ToString() =>
        $"{PlatformAuthId}|{PlatformToken}|{PlatformName}|{PlatformId}|{DeviceId}|{Signature}";
}

/// <summary>
///     Provides methods to generate authentication tokens.
/// </summary>
public static class AuthTokenGenerator
{
    /// <summary>
    ///     Generates a raw authentication token.
    /// </summary>
    /// <param name="platformAuthId">The platform authentication ID.</param>
    /// <param name="platformToken">The platform token.</param>
    /// <param name="platformName">The platform name.</param>
    /// <param name="platformId">The platform ID.</param>
    /// <param name="deviceId">The device ID.</param>
    /// <returns>A raw authentication token.</returns>
    /// <remarks>
    ///     The signature is currently a placeholder and needs to be replaced with a real signature.
    /// </remarks>
    public static string GenRawAuthToken(string platformAuthId, string platformToken, string platformName,
        string platformId, string deviceId)
    {
        // TODO sign
        string signature = "xxxxxxxx";
        return $"{platformAuthId}|{platformToken}|{platformName}|{platformId}|{deviceId}|{signature}";
    }
}
