// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.CommonDataStructure;

/// <summary>
///     Represents a session token.
/// </summary>
public class SessionToken
{
    /// <summary>
    ///     The session's valid timestamp in seconds.
    /// </summary>
    public string TimeStampSeconds;

    /// <summary>
    ///     Creates a new instance of the SessionToken class.
    /// </summary>
    /// <param name="authId">The authentication ID.</param>
    /// <param name="playerId">The player ID.</param>
    /// <param name="deviceId">The device ID.</param>
    /// <param name="platformAuthId">The platform authentication ID.</param>
    /// <param name="platformName">The platform name.</param>
    /// <param name="platformId">The platform ID.</param>
    /// <param name="timeStampSeconds">The session's valid timestamp in seconds.</param>
    /// <param name="signature">The signature.</param>
    private SessionToken(string authId, Int64 playerId, string deviceId, string platformAuthId, string platformName,
        string platformId, string timeStampSeconds, string signature)
    {
        AuthId = authId;
        PlayerId = playerId;
        DeviceId = deviceId;
        PlatformAuthId = platformAuthId;
        PlatformName = platformName;
        PlatformId = platformId;
        TimeStampSeconds = timeStampSeconds;
        Signature = signature;
    }

    /// <summary>
    ///     Gets the authentication ID.
    /// </summary>
    public string AuthId { get; }

    /// <summary>
    ///     Gets the player ID.
    /// </summary>
    public Int64 PlayerId { get; }

    /// <summary>
    ///     Gets the device ID.
    /// </summary>
    public string DeviceId { get; }

    /// <summary>
    ///     Gets the platform authentication ID.
    /// </summary>
    public string PlatformAuthId { get; }

    /// <summary>
    ///     Gets the platform name.
    /// </summary>
    public string PlatformName { get; }

    /// <summary>
    ///     Gets the platform ID.
    /// </summary>
    public string PlatformId { get; }

    /// <summary>
    ///     Gets the signature.
    /// </summary>
    public string Signature { get; }

    /// <summary>
    ///     Tries to parse a string into a SessionToken.
    /// </summary>
    /// <param name="s">The string to parse.</param>
    /// <returns>A SessionToken if the string could be parsed, null otherwise.</returns>
    /// <remarks>
    ///     The string is expected to be a session token that consists of the authentication ID, player ID, device ID, platform
    ///     authentication ID, platform name, platform ID, timestamp, and signature, separated by '|'.
    ///     If the string does not contain at least 8 parts, null is returned.
    /// </remarks>
    public static SessionToken? TryParse(string s)
    {
        string[] subs = s.Split('|');
        if (subs.Length < 8)
        {
            return null;
        }

        Int64 playerId = Int64.Parse(subs[1]);
        return new SessionToken(subs[0], playerId, subs[2], subs[3], subs[4], subs[5], subs[6], subs[7]);
    }

    /// <summary>
    ///     Gets the token without the signature.
    /// </summary>
    /// <param name="rawToken">The raw token.</param>
    /// <returns>The token without the signature.</returns>
    /// <remarks>
    ///     The signature is expected to be the last part of the token, separated by '|'.
    /// </remarks>
    public string GetTokenWithoutSignature(string rawToken)
    {
        int index = rawToken.LastIndexOf('|');
        return rawToken.Substring(0, index);
    }
}
