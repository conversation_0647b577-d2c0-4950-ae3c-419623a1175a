// Copyright (c) Phoenix.  All Rights Reserved.

namespace Phoenix.Server.CommonDataStructure;

public enum ErrCode
{
    ErrCodeOk = 0,
    /// <summary>
    /// player name duplicate
    /// </summary>
    ErrCodePlayerNameDuplicate = 1,
    /// <summary>
    /// invalid sender
    /// </summary>
    ErrCodeInvalidSender = 2,
    /// <summary>
    /// character is already exist
    /// </summary>
    ErrCodeCharacterAlreadyExist = 3,
    /// <summary>
    /// character not exist
    /// </summary>
    ErrCodeCharacterNotExist = 4,
    /// <summary>
    /// sign verify fail
    /// </summary>
    ErrCodeSignVerifyFail = 5,
    /// <summary>
    /// session token is expired
    /// </summary>
    ErrCodeSessionTokenExpired = 6,
    /// <summary>
    /// player context on another game service
    /// </summary>
    ErrCodePlayerOnAnotherGameService = 7,
    /// <summary>
    /// player context status check fail
    /// </summary>
    ErrCodeCtxStatusCheckFail = 8,
    /// <summary>
    /// request invalid
    /// </summary>
    ErrCodeRequestInvalid = 9,
    /// <summary>
    /// player not request playerinitreq
    /// </summary>
    ErrCodeEnterGameNotFinish = 10,
    /// <summary>
    /// player context not existed
    /// </summary>
    ErrPlayerContextNotExisted = 11,
    /// <summary>
    /// player not existed
    /// </summary>
    ErrPlayerNotExisted = 12,
    /// <summary>
    /// 非法创角名字
    /// </summary>
    ErrPlayerNameInvalid = 13,
    /// <summary>
    /// 主角等级低于指定值
    /// </summary>
    ErrCodePlayerLevelLessThan = 14,
    /// <summary>
    /// 已领取主角等级奖励
    /// </summary>
    ErrCodeHasClaimedPlayerLevelReward = 15,
    /// <summary>
    /// 无效的主角冒险等级
    /// </summary>
    ErrCodeInvalidPlayerLevel = 16,
    /// <summary>
    ///货币不足
    /// </summary>
    ErrCodeNotEnoughMoney = 17,
    /// <summary>
    /// 未领取之前等级的主角等级奖励
    /// </summary>
    ErrCodeNotClaimedPrevPlayerLevelReward = 18,
    /// <summary>
    /// session token is expired
    /// </summary>
    ErrCodeLoginErrSessionTokenFormatInvalid = 19,
    /// <summary>
    /// dedicated server Err
    /// </summary>
    ErrCodeDSGroupIsFull = 100,
    /// <summary>
    /// ds group is full
    /// </summary>
    ErrCodeDSGroupNotExist = 101,
    /// <summary>
    /// 未找到 playerState 数据信息
    /// </summary>
    ErrCodeDSNotFoundPlayerState = 102,
    /// <summary>
    /// 切换武器失败
    /// </summary>
    ErrCodeDSSwitchWeaponFailed = 103,
    /// <summary>
    /// Err that do not want user to known the reason
    /// </summary>
    ErrCodeInternalServerErr = 10000,
    ErrCodeInvalidConfigId = 10001,
    ErrCodeInvalidClientParam = 10002,
    ErrCodeConditionNotMatch = 10003,
    ErrCodePlayerOffline = 10004,
    ErrCodePlayerNotLoginDsGroup = 10005,
    ErrCodeEnterWorldInviteNotExist = 10006,
    ErrCodeEnterWorldInviteTimeout = 10007,
    ErrCodePlayerLoginDsGroupAlready = 10008,
    ErrCodeDedicatedServerNotExisted = 10009,
    ErrCodeAwaitableMessageTimeout = 10010,
    ErrCodeMessageException = 10011,
    /// <summary>
    /// config Err
    /// </summary>
    ErrCodeConfigErr = 10012,
    /// <summary>
    /// npc not found
    /// </summary>
    ErrCodeNpcNotFound = 10013,
    /// <summary>
    /// teleport failed
    /// </summary>
    ErrCodeTeleportFailed = 10014,
    /// <summary>
    /// set time paused failed, state same
    /// </summary>
    ErrCodeSetTimePauseFailedStateSame = 10015,
    /// <summary>
    /// set time paused failed, not allowed
    /// </summary>
    ErrCodeSetTimePauseFailedOnlyStandalone = 10016,
    /// <summary>
    /// interaction collect 20000~20050
    /// </summary>
    ErrCodeCollectInterval = 20000,
    /// <summary>
    /// config dont allow gather
    /// </summary>
    ErrCodeCollectCantGather = 20001,
    /// <summary>
    /// check conditions failed
    /// </summary>
    ErrCodeCollectCheckCondi = 20002,
    /// <summary>
    /// interact time limited
    /// </summary>
    ErrCodeInteractTimeLimited = 20003,
    /// <summary>
    /// cant find interaction ex info
    /// </summary>
    ErrCodeCantFindInteractionExInfo = 20004,
    /// <summary>
    /// cant find interaction ex detail info
    /// </summary>
    ErrCodeCantFindInteractionExDetailInfo = 20005,
    /// <summary>
    /// cant find collection config
    /// </summary>
    ErrCodeCantFindCollectionInfo = 20006,
    /// <summary>
    /// config dont allow gather
    /// </summary>
    ErrCodeCollectCantGatherOfConfigDefine = 20007,
    /// <summary>
    /// config gather times less than zero
    /// </summary>
    ErrCodeCollectGatherTimesOfConfigLessThanZero = 20008,
    /// <summary>
    /// interactive state invalid
    /// </summary>
    ErrCodeInteractiveStateInvalid = 20009,
    /// <summary>
    /// chest  20051~20060
    /// </summary>
    ErrCodeInvalidChestId = 200501,
    /// <summary>
    /// chest already open
    /// </summary>
    ErrCodeChestIdOpened = 200502,
    /// <summary>
    /// creature 20060~20100
    /// </summary>
    ErrCodeCreatureNotValid = 20060,
    /// <summary>
    /// max lv, cannot lv up
    /// </summary>
    ErrCodeCreatureReachMaxLv = 20061,
    /// <summary>
    /// region bag Err code 100000 ~ 100999
    /// </summary>
    ErrCodeItemIdInvalid = 100000,
    ErrCodeBagUpperLimit = 100001,
    ErrCodeItemNotExisted = 100002,
    ErrCodeItemNotEnough = 100003,
    ErrCodeItemCanNotUse = 100004,
    ErrCodeItemUseCD = 100005,
    ErrCodeItemLocked = 100006,
    /// <summary>
    /// 非光印背包道具.
    /// </summary>
    ErrCodeNotLightSealBagItem = 100007,
    /// <summary>
    ///光印背包道具等级未达到突破等级
    /// </summary>
    ErrCodeLightSealBagItemLevelNotEqualBreakthroughLevel = 100008,
    ///光印背包道具等级已经突破到达最大值
    /// </summary>
    ErrCodeLightSealBagItemLevelGreaterThanMaxBreakthroughLevel = 100009,
    /// <summary>
    ///光印背包道具未突破等级对应的配置数据
    /// </summary>
    ErrCodeLightSealBagItemNotFoundBreakthroughLevelConf = 100010,
    /// <summary>
    ///消耗物品列表为空.
    /// </summary>
    ErrCodeConsumeItemsEmpty = 100011,
    /// <summary>
    ///光印背包道具等级已经到达最大值
    /// </summary>
    ErrCodeLightSealBagItemLevelIsMax = 100012,
    /// <summary>
    ///非添加光印经验的道具
    /// </summary>
    ErrCodeNotAddLightSealExpItem = 100013,
    /// <summary>
    /// 添加数量为负数
    /// </summary>
    ErrCodeAddNegativeCount = 100014,
    ErrCodeItemTypeInvalid = 100015,
    ErrCodeBundleIdInvalid = 100016,
    /// <summary>
    /// BundleId不允许嵌套使用
    /// </summary>
    ErrCodeBundleIdNested = 100017,
    /// <summary>
    /// region gm Err code 101000 ~ 101999
    /// </summary>
    ErrCodeGmCmdInvalid = 101000,
    ErrCodeGmCmdNotFound = 101001,
    /// <summary>
    /// region quest Err code 102000 ~ 102999
    /// </summary>
    ErrCodeQuestAcceptFailedConditionLess = 102000,
    ErrCodeQuestAcceptFailedAlreadyAccepted = 102001,
    ErrCodeQuestAcceptFailedAlreadyFinished = 102002,
    ErrCodeQuestNotExisted = 102003,
    ErrCodeQuestNodeFinishFailedNotExisted = 102004,
    ErrCodeQuestNodeFinishFailedAlreadyFinish = 102005,
    ErrCodeQuestNodeFinishFailedNotSupportClient = 102006,
    ErrCodeQuestNotFinished = 102007,
    ErrCodeQuestAccepted = 102008,
    ErrCodeQuestFinished = 102009,
    ErrCodeQuestIsAcceptableAlready = 102010,
    ErrCodeQuestPreAcceptConditionFailed = 102011,
    ErrCodeQuestFollowingNotSet = 102012,
    /// <summary>
    /// region illustration Err code 103000 ~ 103099
    /// </summary>
    ErrCodeIllustrationAlreadyUnlock = 103000,
    ErrCodeIllustrationClientUnlockNotSupport = 103001,
    ErrCodeIllustrationLightDustCollectRewardGotAlready = 103002,
    ErrCodeIllustrationLightDustCollectRewardCountLess = 103003,
    /// <summary>
    /// region entity Err code -103100 ~ -103299
    /// </summary>
    ErrCodeEntityGatherNotSupport = 103100,
    /// <summary>
    /// region trigger Err code 103300 ~ 103399
    /// </summary>
    ErrCodeTriggerEventIdInvalid = 103300,
    ErrCodeTriggerEventParamterInvalid = 103301,
    /// <summary>
    /// region trigger Err code 103400 ~ 103499
    /// </summary>
    ErrCodeInvalidStoryId = 103400,
    ErrCodeNotStartedStory = 103401,
    ErrCodeStoryNotHasNode = 103402,
    ErrCodeStoryCantGotoFinishNode = 103403,
    ErrCodeStoryInvalidBranchIndex = 103404,
    ErrCodeStoryFindPathFailed = 103406,
    ErrCodeStoryFindPathBusyLoop = 103407,
    ErrCodeStoryIdNotEqual = 103408,
    /// <summary>
    /// 该对话无法重复完成
    /// </summary>
    ErrCodeStoryNotRepeated = 103409,
    /// <summary>
    /// region trigger Err code 104000 ~ 104999
    /// </summary>
    ErrKickedByServerLoginConflict = 104000,
    /// <summary>
    /// region weapon Err code 105000 ~ 105999
    /// </summary>
    ErrCodeInvalidWeaponId = 10500,
    /// <summary>
    /// 未拥有该武器
    /// </summary>
    ErrCodeNotHasWeapon = 10501,
    /// <summary>
    /// 无效的武器光印槽位下标值，有效值 [0,1,2]
    /// </summary>
    ErrCodeInvalidWeaponLightSealSlotIndex = 10502,
    /// <summary>
    /// 武器光印槽位还未开启
    /// </summary>
    ErrCodeNotOpenedWeaponLightSealSlot = 10503,
    /// <summary>
    /// 光印脱下失败，光印id不匹配
    /// </summary>
    ErrCodeLightSealPutOffFailedItemIdNotEqual = 10504,
    /// <summary>
    /// 未找到光印槽位下标信息
    /// </summary>
    ErrCodeNotFountLightSealSlotIndex = 10505,
    /// <summary>
    /// 武器系统还未开启
    /// </summary>
    ErrCodeNotOpenWeaponSystem = 10506,
    /// <summary>
    /// 光印叠影已经到达最大值
    /// </summary>
    ErrCodeMaxLightSealStrengthenLevel = 10507,
    /// <summary>
    /// Waypoints
    /// </summary>
    ErrCodeWaypointIndexNotExist = 10601,
    /// <summary>
    /// battle
    /// </summary>
    ErrCodeVerifyFail = 1000,
    ErrCodeBattleException = 1001,
    ErrCodeTargetPlayerNotExist = 2000,
    ErrCodeTargetPlayerIsSelf = 2001,
}
