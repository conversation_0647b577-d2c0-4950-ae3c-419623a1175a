// Copyright (c) Phoenix.  All Rights Reserved.

using Phoenix.Server.Utils;
using Phoenix.Server.Utils.Security;

namespace Phoenix.Server.CommonDataStructure;

/// <summary>
///     This class is responsible for generating session tokens.
/// </summary>
public static class SessionTokenGenerator
{
    /// <summary>
    ///     Generates a session token.
    /// </summary>
    /// <param name="authId">The authentication ID.</param>
    /// <param name="playerId">The player ID.</param>
    /// <param name="platformAuthId">The platform authentication ID.</param>
    /// <param name="platformName">The platform name.</param>
    /// <param name="platformId">The platform ID.</param>
    /// <param name="deviceId">The device ID.</param>
    /// <returns>A session token.</returns>
    /// <remarks>
    ///     The session token is a string that consists of the authentication ID, player ID, platform authentication ID,
    ///     platform name, platform ID, device ID, timestamp, and a signature.
    ///     The timestamp is the current UTC time plus one hour, in seconds.
    ///     The signature is generated by signing the token without the signature using RSA.
    /// </remarks>
    public static string GenToken(string authId, Int64 playerId, string platformAuthId, string platformName,
        string platformId, string deviceId)
    {
        // Get the current UTC time plus one hour, in seconds.
        long timeStamp = DateTimeHelper.GetTimestampSeconds(DateTime.UtcNow.AddSeconds(3600));

        // Construct the token without the signature.
        string tokenWithoutSign =
            $"{authId}|{playerId}|{platformAuthId}|{platformName}|{platformId}|{deviceId}|{timeStamp}";

        // Generate the signature by signing the token without the signature using RSA.
        string signature = RSA.SignData(tokenWithoutSign);

        // Return the session token, which is the token without the signature plus the signature.
        return tokenWithoutSign + "|" + signature;
    }
}
