<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="..\BattleExecuterLink\**\*.cs" />
  </ItemGroup>

  <PropertyGroup>
    <NoWarn>CS8600</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <WarningLevel>0</WarningLevel>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="MessagePack" />
    <PackageReference Include="MessagePackAnalyzer">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <ProjectReference Include="..\BaseCore\BaseCore.csproj" />
    <ProjectReference Include="..\Battle\Battle.csproj" />
    <ProjectReference Include="..\ConfigData\ConfigData.csproj" />

    <PackageReference Include="Newtonsoft.Json" />
  </ItemGroup>

</Project>
