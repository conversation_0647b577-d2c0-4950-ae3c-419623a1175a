using Newtonsoft.Json;
using System.Text;
using Phoenix.Battle;
using Phoenix.ConfigData;

namespace BattleUnitTests
{
    public class BattleTests
    {

        private string ConfigDataDir = Path.Combine("..", "..", "..", "..", "ResoucreLink");
        private string BattleProtoDir = Path.Combine("..", "..", "..", "..", "BattleProtoLink");

        [SetUp]
        public void Setup()
        {

        }

        [Test]
        public void TestBattleRecordCheck()
        {
            /*var record = LoadBattleRecord(@"./TestBattleRecord/DebugRecord.txt");
            if (record == null)
            {
                return;
            }

            // ConfigDataManager.instance.LoadAllSync(OnLoadBufferByKey);
            ConfigDataManager.instance.LoadAllSyncByFile(ConfigDataDir);
            var infoGetter = CreateBattleInfoGetter();
            BattleExecuterCheck executer = new BattleExecuterCheck();
            executer.Init(record.battleInitData, infoGetter, null);
            try
            {
                executer.TickToLast(record);
                var report = executer.battle.CreateReport();
                Assert.That(report, Is.Not.Null);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            finally
            {
                executer.UnInit();
            }*/
        }

        [Test]
        public void TestBattlePVP()
        {
            /*var record = LoadBattleRecord(@"./TestBattleRecord/DebugRecord.txt");
            if (record == null)
            {
                return;
            }

            // ConfigDataManager.instance.LoadAllSync(OnLoadBufferByKey);
            ConfigDataManager.instance.LoadAllSyncByFile(ConfigDataDir);
            var infoGetter = CreateBattleInfoGetter();
            BattleExecuterCheck executer = new BattleExecuterCheck();
            executer.Init(record.battleInitData, infoGetter, null);
            try
            {
                executer.TickToLast(record);
                var report = executer.battle.CreateReport();
                Assert.That(report, Is.Not.Null);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            finally
            {
                executer.UnInit();
            }*/
        }

        private BattleInfoGetterBase CreateBattleInfoGetter()
        {
            var infoGetter = new BattleInfoGetterProto();
            infoGetter.ReadAllFormFolder(BattleProtoDir, LoadBuffer);
            infoGetter.Init(null);
            return infoGetter;
        }

        private BattleInfoGetterBase CreateBattleInfoGetter(string battleDataDir)
        {
            var infoGetter = new BattleInfoGetterProto();
            infoGetter.ReadAllFormFolderByFile(battleDataDir);
            infoGetter.Init(null);
            return infoGetter;
        }

        private byte[] OnLoadBufferByKey(string key)
        {
            string path = $"{ConfigDataDir}\\{key}.bytes";
            return LoadBuffer(path);
        }

        private byte[] LoadBuffer(string path)
        {
            using (FileStream fs = File.Open(path, FileMode.Open))
            {
                byte[] buffer = new byte[fs.Length];
                fs.Read(buffer, 0, buffer.Length);
                return buffer;
            }
        }

       /* private BattleRecord LoadBattleRecord(string path)
        {
            if (!File.Exists(path))
            {
                return null;
            }
            BattleRecord record = null;
            try
            {
                using (StreamReader sr = new StreamReader(path, Encoding.UTF8))
                {
                    record = JsonConvert.DeserializeObject<BattleRecord>(sr.ReadToEnd());
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return record;
        }*/
    }
}
