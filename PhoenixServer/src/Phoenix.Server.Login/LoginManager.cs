// Copyright (c) Phoenix.All Rights Reserved.

using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Common;
using Phoenix.Server.Common.Actor;
using Phoenix.Server.CommonDataStructure;
using Phoenix.Server.CommonDataStructure.Common;
using Phoenix.Server.Model;
using InternalLoggerFactory = Phoenix.Server.Common.InternalLoggerFactory;

namespace Phoenix.Server.Login;

[RpcModule(Name = "LoginMgr")]
public class LoginManager : Actor<string>, IHostedService
{
    public static LoginManager Instance { get; private set; } = null!;

    public LoginManager(IGameDatabaseService gameDatabaseService, IUniqueIdentifierGenerator uniqueIdentifierGenerator)
    {
        m_gameDatabaseService = gameDatabaseService;
        m_uniqueIdentifierGenerator = uniqueIdentifierGenerator;
        Instance = this;

        RpcModuleMethodFinder.Instance.RegModule(this);
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        await Active();
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        await Inactive();
    }

    /// <summary>
    ///     Handles the LoginByAuthToken request.
    /// </summary>
    /// <param name="reqInfo"></param>
    /// <param name="message"></param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the LoginByAuthTokenAck.</returns>
    [RpcMethod(Name = "LoginByAuthTokenReq")]
    public Task LoginByAuthTokenReq(RpcRequestInfo reqInfo, MsgPackStructBase message)
    {
        // handle the request in thread pool
        var t = Task.Run(async () =>
        {
            var request = (LoginByAuthTokenReq)message;
            m_logger.LogDebug("LoginByAuthToken is starting,request={request}", request);

            AuthToken authToken = AuthToken.From(request.AuthToken, true);
            string platformToken = authToken.PlatformToken;

            try
            {
                await VerifyAuthToken(platformToken);
                AccountInfo accountInfo = await GetOrAddAccount(authToken.PlatformAuthId);
                var ack = new LoginByAuthTokenAck
                {
                    ErrCode = (int)ErrCode.ErrCodeOk,
                    SessionToken = SessionTokenGenerator.GenToken(accountInfo.AuthId, accountInfo.PlayerId,
                        authToken.PlatformAuthId, authToken.PlatformName, authToken.PlatformId, authToken.DeviceId)
                };
                m_logger.LogDebug("LoginByAuthToken finished,errCode={errCode}", ack.ErrCode);
                reqInfo.SendResponse(ack);
            }
            catch (Exception ex)
            {
                m_logger.LogError(ex, "LoginByAuthToken failed");
                var ack = new LoginByAuthTokenAck { ErrCode = (int)ErrCode.ErrCodeInternalServerErr };
                reqInfo.SendResponse(ack);
            }
        }).ConfigureAwait(false);
        return Task.CompletedTask;
        //}
    }

    /// <summary>
    ///     Verifies the platform authentication token.
    /// </summary>
    /// <param name="_">The platform authentication token.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a boolean indicating the
    ///     verification result.
    /// </returns>
    private Task<bool> VerifyAuthToken(string _) =>
        // TODO verify platformToken
        Task.FromResult(true);

    /// <summary>
    ///     Retrieves the account information for the specified authentication id if it exists; otherwise, creates a new account.
    /// </summary>
    /// <param name="platformAuthId">The platform authentication id.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the AccountInfo.</returns>
    private async Task<AccountInfo> GetOrAddAccount(string platformAuthId)
    {
        AccountModel? queryResult = await m_gameDatabaseService.GetAccountByAuthId(platformAuthId);
        if (queryResult != null)
        {
            return new AccountInfo(queryResult.AuthId, queryResult.PlayerId);
        }

        Int64 playerId = m_uniqueIdentifierGenerator.CreateId();
        await m_gameDatabaseService.CreateAccountAsync(new AccountModel
        {
            PlayerId = playerId,
            AuthId = platformAuthId
        });

        m_logger.LogDebug("create new account ok,playerId={playerId} authId={authId}", playerId,
            platformAuthId);
        return new AccountInfo(platformAuthId, playerId);
    }

    /// <summary>
    ///     The logger used to log messages.
    /// </summary>
    private static readonly ILogger m_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<LoginManager>();

    /// <summary>
    ///     The service used to interact with the game database.
    /// </summary>
    private readonly IGameDatabaseService m_gameDatabaseService = null!;

    /// <summary>
    ///     The generator used to create unique identifiers.
    /// </summary>
    private readonly IUniqueIdentifierGenerator m_uniqueIdentifierGenerator;

    public override string GetActorId() => "LoginMgr";
}
