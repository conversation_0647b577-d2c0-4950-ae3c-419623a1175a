// Copyright (c) Phoenix.All Rights Reserved.

using Microsoft.Extensions.Hosting;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Common;
using Phoenix.Server.Common.Actor;
using Phoenix.Server.Utils;

namespace Phoenix.Server.Login;

public class LoginBootstrap : SingletonBase<LoginBootstrap>, IHostedService
{
    private void BindDelegates()
    {
        RpcService.Instance.DelegateOnRpcMessageRequest += OnRpcMessageRequest;
    }

    private void OnRpcMessageRequest(string uri, string funcName, MsgPackStructBase message, RpcRequestInfo reqInfo)
    {
        if (uri == RpcUriConstants.LOGIN_MGR_URI)
        {
            var actor = ActorRegistry.Instance.GetActor("LoginMgr");
            actor?.PostMessage(new RpcReqMessage(reqInfo.CorrelationId, reqInfo, "LoginMgr", funcName, null, message));
        }
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        BindDelegates();
        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}
