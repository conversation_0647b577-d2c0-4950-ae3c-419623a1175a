// Copyright (c) Phoenix.  All Rights Reserved.

using System;
using MongoDB.Bson.Serialization.Attributes;

namespace Phoenix.Server.Model;

/// <summary>
///     Represents an account in the account collection.
/// </summary>
public sealed class AccountModel
{
    /// <summary>
    ///     Gets the player's ID. This is the unique identifier for the player.
    /// </summary>
    [BsonId]
    public long PlayerId { get; init; }

    /// <summary>
    ///     Gets the authentication ID of the player.
    /// </summary>
    [BsonElement("AuthId")]
    public string AuthId { get; init; } = string.Empty;

    /// <summary>
    ///     Gets the time when the player was banned.
    /// </summary>
    /// <remarks>
    ///     The BsonDateTimeOptions attribute specifies that the DateTime is stored in the local time zone in the database.
    /// </remarks>
    [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
    [BsonElement("BanTime")]
    public DateTime BanTime { get; init; }

    /// <summary>
    ///     Gets the reason why the player was banned.
    /// </summary>
    [BsonElement("BanReason")]
    public string BanReason { get; init; } = string.Empty;
}
