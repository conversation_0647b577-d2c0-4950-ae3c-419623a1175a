// Copyright (c) Phoenix.  All Rights Reserved.

using System;
using MongoDB.Bson.Serialization.Attributes;

namespace Phoenix.Server.Model;

/// <summary>
///     Represents the model for a game player's name in the system.
/// </summary>
/// <remarks>
///     This class is marked with the BsonIgnoreExtraElements attribute, which instructs the MongoDB driver to ignore any
///     extra elements in the BSON document that do not correspond to properties in the class.
/// </remarks>
[BsonIgnoreExtraElements(true)]
public sealed class GamePlayerNameModel
{
    /// <summary>
    ///     Gets the unique identifier for the game player.
    /// </summary>
    [BsonId]
    public Int64 Id { get; init; }

    /// <summary>
    ///     Gets the name of the game player.
    /// </summary>
    /// <value>
    ///     The name of the game player. This property is initialized to an empty string and can only be set during object
    ///     initialization (due to the init accessor).
    /// </value>
    public string Name { get; init; } = string.Empty;
}
