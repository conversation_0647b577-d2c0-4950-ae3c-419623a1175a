// Copyright (c) Phoenix.  All Rights Reserved.

using Phoenix.Server.MongoDBWrapper;

namespace Phoenix.GameModel.Server;

/// <summary>
/// Builds server collections for the game.
/// </summary>
public class ServerCollectionBuilder
{
    /// <summary>
    /// Initializes a new instance of the <see cref="ServerCollectionBuilder"/> class.
    /// </summary>
    /// <param name="filterField">The field to filter on in the player database.</param>
    public ServerCollectionBuilder(long filterField) => PlayerPartialUpdateBuilder =
        new MongoPartialDBUpdateDocumentBuilder<GamePlayerData>(filterField);

    /// <summary>
    /// Gets or sets the builder for updating partial fields of the player database.
    /// </summary>
    public MongoPartialDBUpdateDocumentBuilder<GamePlayerData> PlayerPartialUpdateBuilder { get; set; }
}
