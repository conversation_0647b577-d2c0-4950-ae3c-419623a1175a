// Copyright (c) Phoenix.  All Rights Reserved.

using System;
using MongoDB.Bson.Serialization.Attributes;
using Phoenix.GameModel.Base;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.GameModel.Server;

/// <summary>
///     Represents a game player's model in the game.
/// </summary>
[BsonIgnoreExtraElements(true)]
public sealed partial class GamePlayerData
{
    /// <summary>
    ///     Gets or sets the ID of the game player model.
    /// </summary>
    [BsonId]
    public Int64 Id { get; set; }

    /// <summary>
    ///     Gets or sets the basic model of the game player.
    /// </summary>
    [PersistComp(CompNames.Basic)]
    [BsonElement("Basic")]
    public ServerPlayerCompDataBasicInfo Basic { get; set; } = new();

    /// <summary>
    ///     Gets or sets the world model of the game player.
    /// </summary>
    [PersistComp(CompNames.World)]
    [BsonElement("World")]
    public ServerPlayerCompDataWorldInfo World { get; set; } = new();

    [PersistComp(CompNames.Test)]
    [BsonElement("Example")]
    public MsgPack_DB_ExampleData ExampleData { get; set; } = new();

    [PersistComp(CompNames.Hakoniwa)]
    [BsonElement("HakoniwaInfo")]
    public ServerPlayerCompDataHakoniwaInfo HakoniwaInfo { get; set; } = new();
}
