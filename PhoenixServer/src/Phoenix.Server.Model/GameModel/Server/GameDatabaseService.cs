// Copyright (c) Phoenix.  All Rights Reserved.

using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using Phoenix.GameModel.Server;
using Phoenix.Server.Common;
//using AccountModel = Phoenix.Server.Model.AccountModel;

namespace Phoenix.Server.Model;

/// <summary>
///     Interface for game database service.
/// </summary>
public interface IGameDatabaseService
{
    /// <summary>
    ///     Retrieves an account by the specified AuthId.
    /// </summary>
    /// <param name="authId">The unique identifier for authentication.</param>
    /// <returns>Returns null if retrieval fails, otherwise returns the account.</returns>
    Task<AccountModel?> GetAccountByAuthId(string authId);

    /// <summary>
    ///     Creates an account.
    /// </summary>
    /// <param name="account">The account information.</param>
    /// <returns>A Task representing the asynchronous operation.</returns>
    Task CreateAccountAsync(AccountModel account);

    /// <summary>
    /// Updates a player in the database.
    /// </summary>
    /// <param name="playerId">The ID of the player to update.</param>
    /// <param name="update">The update definition for the player.</param>
    Task UpdatePlayerAsync(long playerId, UpdateDefinition<GamePlayerData> update);

    /// <summary>
    /// Creates a new player in the database.
    /// </summary>
    /// <param name="player">The player model to create.</param>
    Task CreatePlayerAsync(GamePlayerData player);

    /// <summary>
    /// Retrieves a player from the database.
    /// </summary>
    /// <param name="playerId">The ID of the player to retrieve.</param>
    Task<GamePlayerData?> GetPlayerAsync(long playerId);
}

/// <summary>
///     Implementation of the game database service.
/// </summary>
public sealed class GameDatabaseService : IGameDatabaseService, IHostedService
{
    /// <summary>
    ///     A MongoDB collection of AccountModel objects, representing the account collection in the database.
    /// </summary>
    private readonly IMongoCollection<AccountModel> m_account;

    /// <summary>
    ///     Initializes a new instance of the GameDatabaseService class.
    /// </summary>
    /// <param name="settings">The database settings.</param>
    /// <param name="logger">The logger.</param>
    public GameDatabaseService(IOptions<DatabaseSettingsOptions> settings)
    {
        Instance = this;

        string connection = settings.Value.ConnectionString;
        string dbName = settings.Value.DatabaseName;
        Logger.LogInformation("game database service is staring,connection={connection} databaseName={name}",
            connection, dbName);

        MongoClient? client = new(connection);
        IMongoDatabase database = client.GetDatabase(dbName);

        m_account = database.GetCollection<AccountModel>(CollectionNameConstants.Account);
        m_player = database.GetCollection<GamePlayerData>(CollectionNameConstants.Player);
    }

    /// <summary>
    ///     Singleton instance of the GameDatabaseService class.
    /// </summary>
    public static IGameDatabaseService Instance { get; private set; } = default!;

    /// <summary>
    ///     Retrieves an account by the specified AuthId.
    /// </summary>
    /// <param name="authId">The unique identifier for authentication.</param>
    /// <returns>Returns null if retrieval fails, otherwise returns the account.</returns>
    public async Task<AccountModel?> GetAccountByAuthId(string authId)
    {
        FilterDefinition<AccountModel>? filter = Builders<AccountModel>.Filter.Eq(a => a.AuthId, authId);
        return await m_account.Find(filter).FirstOrDefaultAsync();
    }

    /// <summary>
    ///     Creates an account.
    /// </summary>
    /// <param name="account">The account information.</param>
    /// <returns>A Task representing the asynchronous operation.</returns>
    public async Task CreateAccountAsync(AccountModel account) => await m_account.InsertOneAsync(account);

    /// <summary>
    /// Updates a player in the database.
    /// </summary>
    /// <param name="playerId">The ID of the player to update.</param>
    /// <param name="update">The update definition for the player.</param>
    public async Task UpdatePlayerAsync(long playerId, UpdateDefinition<GamePlayerData> update)
    {
        FilterDefinition<GamePlayerData>? filter = Builders<GamePlayerData>.Filter.Eq(c => c.Id, playerId);

        await m_player.UpdateOneAsync(filter, update);
    }

    /// <summary>
    /// Creates a new player in the database.
    /// </summary>
    /// <param name="player">The player model to create.</param>
    public async Task CreatePlayerAsync(GamePlayerData player)
    {
        await m_player.InsertOneAsync(player);
    }

    /// <summary>
    /// Retrieves a player from the database.
    /// </summary>
    /// <param name="playerId">The ID of the player to retrieve.</param>
    public async Task<GamePlayerData?> GetPlayerAsync(long playerId)
    {
        FilterDefinition<GamePlayerData>? filter =
            Builders<GamePlayerData>.Filter.Eq(c => c.Id, playerId);
        return await m_player.Find(filter).FirstOrDefaultAsync();
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        IndexKeysDefinition<GamePlayerData>? indexKeysDefinition =
            Builders<GamePlayerData>.IndexKeys.Ascending(n => n.Basic.Name);
        var indexOptions = new CreateIndexOptions { Unique = true };
        var indexModel = new CreateIndexModel<GamePlayerData>(indexKeysDefinition, indexOptions);
        await m_player.Indexes.CreateOneAsync(indexModel, cancellationToken: cancellationToken);
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// The MongoDB collection for the game players.
    /// </summary>
    private readonly IMongoCollection<GamePlayerData> m_player;

    private static readonly ILogger Logger = InternalLoggerFactory.LoggerFactory.CreateLogger<GamePlayerData>();
}
