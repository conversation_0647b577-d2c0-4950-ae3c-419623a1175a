// Copyright (c) Phoenix All Rights Reserved.

using System.Threading.Tasks;
using Phoenix.GameModel.Base;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Common.MessageHandlerRegistry;

namespace Phoenix.GameModel.Server;

public sealed class GamePlayerCompBasic : GamePlayerCompBasicBase, IServerDataComp
{
    public GamePlayerCompBasic()
    {
    }

    public override bool Init()
    {
        if (!base.Init())
        {
            return false;
        }

        // Owner.RegisterCallback<MsgPack_DuelReply_Req>((req) =>
        // {
        //     Owner.CompTransport.SendMessageToClient(new MsgPack_Duel_Ack { Token = req.Token });
        //     return Task.CompletedTask;
        // });

        m_isInited = true;
        return true;
    }

    [MessageHandler(typeof(MsgPack_DuelReply_Req))]
    public Task OnMsgPack_DuelReplyReq_C2S(MsgPack_DuelReply_Req req)
    {
        Owner.CompTransport.SendMessageToClient(new MsgPack_Duel_Ack { Token = req.Token });
        return Task.CompletedTask;
    }

    void IServerDataComp.SyncToClient()
    {
        PlayerBasicCompDataNtf dsNtf = new();
        dsNtf.BasicInfo = m_baseData;
        Owner.CompTransport.SendMessageToClient(dsNtf);
    }

    public void InitFromConfig() {

    }

    public void InitFromDB(object data)
    {
        m_baseData = (data as ServerPlayerCompDataBasicInfo)!;
    }

    void IServerDataComp.Persistent()
    {
        // if (!m_baseData.CheckDirty())
        // {
        //     return;
        // }
        // Owner.CompPersistent.CollectionBuilder.PlayerPartialUpdateBuilder.Set(m => m.Basic, Data);
        // m_baseData.ResetDirty();
        Owner.CompPersistent.PersistIfDirty(Data, m => m.Basic);
    }

    public void SetNewbieFlag(bool isNewbie)
    {
        Data.IsNewbie = isNewbie;
        // SetDataDirty();
        Data.MarkDirty();
    }

    public bool IsNewbie()
    {
        return Data.IsNewbie;
    }

    internal string GetName()
    {
        return Data.Name;
    }

    private new GamePlayer Owner => (base.Owner as GamePlayer)!;

    private ServerPlayerCompDataBasicInfo Data => (m_baseData as ServerPlayerCompDataBasicInfo)!;
}
