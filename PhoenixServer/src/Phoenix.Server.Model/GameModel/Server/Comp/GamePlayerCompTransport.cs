// Copyright (c) Phoenix All Rights Reserved.

using Microsoft.Extensions.Logging;
using Phoenix.GameModel.Base;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Common;

namespace Phoenix.GameModel.Server;

public delegate void SendToClientFunc(MsgPackStructBase message);

public sealed class GamePlayerCompTransport : GamePlayerCompBase
{
    public GamePlayerCompTransport()
    {
    }

    public void BindDelegates(SendToClientFunc sendToClientFunc)
    {
        m_sendToClientFunc = sendToClientFunc;
        m_isInited = true;
    }

    public void SendMessageToClient(MsgPackStructBase message)
    {
        if (message.HasToken()  && (message.GetToken() == null || message.GetToken() == 0))
        {
            s_logger.LogError("SendMessageToClient token is null ProtoCode={ProtoCode}", message.ProtoCode);
            System.Diagnostics.Debug.Assert(false, $"SendMessageToClient token is null ProtoCode={message.ProtoCode}");
        }
        if (m_sendToClientFunc != null)
        {
            m_sendToClientFunc(message);
        }
    }

    public override string Name => CompNames.Transport;

    private SendToClientFunc? m_sendToClientFunc = null;

    private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<GamePlayerCompTransport>();
}
