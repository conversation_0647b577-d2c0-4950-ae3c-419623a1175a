namespace Phoenix.GameModel.Server;

public interface IServerDataComp
{
    /// <summary>
    /// 从db数据初始化
    /// </summary>
    /// <param name="data"></param>
    void InitFromDB(object data);

    /// <summary>
    /// 持久化数据
    /// </summary>
    void Persistent();

    /// <summary>
    /// 同步数据到客户端
    /// </summary>
    void SyncToClient();

    /// <summary>
    /// 创建时的初始化
    /// </summary>
    void InitFromConfig();
}
