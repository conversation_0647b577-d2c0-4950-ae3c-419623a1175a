// Copyright (c) Phoenix All Rights Reserved.

using System;
using System.Linq.Expressions;
using Phoenix.GameModel.Base;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.GameModel.Server;


public sealed class GamePlayerCompPersistent : GamePlayerCompBase, IGamePlayerCompPersistent
{
    public bool Init(long playerId)
    {
        CollectionBuilder = new ServerCollectionBuilder(playerId);
        return true;
    }

    public void PersistIfDirty<T>(T model, Expression<Func<GamePlayerData, T>> field) where T : DBCacheObject
    {
        if (!model.CheckDirty())
        {
            return;
        }

        CollectionBuilder.PlayerPartialUpdateBuilder.Set(field, model);
        model.ResetDirty();
    }

    public ServerCollectionBuilder CollectionBuilder { get; private set; } = null!;

    public override string Name => CompNames.Persistent;
}
