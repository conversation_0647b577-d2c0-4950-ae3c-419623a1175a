// Copyright (c) Phoenix All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Phoenix.ConfigData;
using Phoenix.GameModel.Base;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Common.MessageHandlerRegistry;
using Phoenix.Server.CommonDataStructure;

namespace Phoenix.GameModel.Server
{
    public sealed class GamePlayerCompHakoniwa : GamePlayerCompHakoniwaBase, IServerDataComp
    {
        public GamePlayerCompHakoniwa()
        {
        }

        public override bool Init()
        {
            if (!base.Init())
            {
                return false;
            }

            m_isInited = true;
            return true;
        }

        void IServerDataComp.SyncToClient()
        {
            // MsgPack_HakoniwaInfoDataSection_Ntf dsNtf = new();
            // dsNtf.HakoniwaInfo = m_baseData;
            // Owner.CompTransport.SendMessageToClient(dsNtf);
        }

        public void InitFromConfig()
        {
            // 初始化默认箱庭数据
            // s_logger.LogInformation("Initializing default Hakoniwa data for player {PlayerId}", Owner.PlayerId);
        }

        public void InitFromDB(object data)
        {
            m_baseData = (data as ServerPlayerCompDataHakoniwaInfo)!;
            // s_logger.LogInformation("Loaded Hakoniwa data for player {PlayerId}, active hakoniwa count: {Count}",
                // Owner.PlayerId, m_baseData.HakoniwaList.Count);
        }

        void IServerDataComp.Persistent()
        {
            Owner.CompPersistent.PersistIfDirty(Data, m => m.HakoniwaInfo);
        }

        [MessageHandler(typeof(HakoniwaCreateReq))]
        public Task OnHakoniwaCreateReq(HakoniwaCreateReq req)
        {
            var ack = new HakoniwaCreateAck();
            ack.ErrCode = (int)ErrCode.ErrCodeOk;
            ack.HakoniwaId = req.HakoniwaId;
            ack.SetToken(req.Token);
            if (!CanCreateHakoniwa(req.HakoniwaId))
            {
                goto ToClient;
            }

            var info = CreateExploringHakoniwaInfo(req.HakoniwaId);
            OnCreateHakoniwaSuccess(info);
            ack.HakoniwaInfo = info;
ToClient:
            Owner.CompTransport.SendMessageToClient(ack);
            return Task.CompletedTask;
        }

        /// <summary>
        /// 创建箱庭
        /// </summary>
        /// <param name="hakoniwaId"></param>
        /// <returns></returns>
        private ExploringHakoniwaInfo CreateExploringHakoniwaInfo(int hakoniwaId)
        {
            var hakoniwaInfo = new ExploringHakoniwaInfo
            {
                HakoniwaId = hakoniwaId,
                CreateTime = Owner.GetCurrentTime(),
            };
            var conf = ConfigDataManager.instance.GetPhoenixHakoniwa(hakoniwaId)!;
            var initLocationId = conf.InitLocation;
            hakoniwaInfo.UnlockLocationIds.Add(initLocationId);
            var initLocationConf = conf.InitLocationConfig;
            hakoniwaInfo.SceneId = initLocationConf.HakoSceneId;
            hakoniwaInfo.PosInfo = new PosInfo()
            {
                X = initLocationConf.Location.X,
                Y = initLocationConf.Location.Y,
                Z = initLocationConf.Location.Z,
            };
            return hakoniwaInfo;
        }

        private new GamePlayer Owner => (base.Owner as GamePlayer)!;

        private ServerPlayerCompDataHakoniwaInfo Data => (m_baseData as ServerPlayerCompDataHakoniwaInfo)!;
    }
}
