// Copyright (c) Phoenix All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Phoenix.GameModel.Base;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.GameModel.Server;
public sealed class GamePlayerCompTest : GamePlayerCompTestBase, IServerDataComp
{
    public GamePlayerCompTest()
    {
    }

    public override bool Init()
    {
        if (!base.Init())
        {
            return false;
        }
        m_isInited = true;
        return true;
    }

    public void SendToClientExampleData()
    {
        m_baseData.SByteValue = 1;
        m_baseData.ByteValue = 2;
        m_baseData.Int16Value = 3;
        m_baseData.UInt16Value = 4;
        m_baseData.Int32Value = 5;
        m_baseData.UInt32Value = 6;
        m_baseData.Int64Value = 7;
        m_baseData.UInt64Value = 8;
        m_baseData.FloatValue = 9.0f;
        m_baseData.SingleValue = 10.0f;
        m_baseData.DoubleValue = 11.0d;
        m_baseData.StringValue = "Test_AAAAA";
        m_baseData.BooleanValue = true;
        for (UInt64 i = 0; i < 5; i++)
        {
            UInt64 uInt64 = (i + 1) * 10;
            if (m_baseData.ListExample.Contains(uInt64))
            {
                continue;
            }
            m_baseData.ListExample.Add(uInt64);
        }
        for (UInt64 i = 0; i < 5; i++)
        {
            Double uInt64 = (i + 1) * 100.1d;
            if (m_baseData.DictExample.ContainsKey(i))
            {
                continue;
            }
            m_baseData.DictExample.Add(i, uInt64);
        }
        for (Int32 i = 0; i < 5; i++)
        {
            MsgPack_DB_Item msgPack_DB_Item = new MsgPack_DB_Item();
            msgPack_DB_Item.Id = (i + 1) * 1000;
            msgPack_DB_Item.Type = (i + 1) * 1002;
            msgPack_DB_Item.Count = (i + 1) * 1001;
            msgPack_DB_Item.Name = $"item_{msgPack_DB_Item.Id}";
            AddItem(msgPack_DB_Item);
        }

        MsgPack_DB_Item msgPack_DB_Item987 = new MsgPack_DB_Item();
        msgPack_DB_Item987.Id = 99999;
        msgPack_DB_Item987.Type = 99998;
        msgPack_DB_Item987.Count = 99997;
        msgPack_DB_Item987.Name = $"item_{msgPack_DB_Item987.Id}";
        AddItem(msgPack_DB_Item987);

        MsgPack_ExampleData_Ntf dsNtf = new();
        dsNtf.ExampleData = m_baseData;
        Owner.CompTransport.SendMessageToClient(dsNtf);
    }

    void IServerDataComp.SyncToClient() { }
    public void InitFromConfig() {
    }
    public void InitFromDB(object data)
    {
        m_baseData = (data as MsgPack_DB_ExampleData)!;
    }

    void IServerDataComp.Persistent()
    {
        Owner.CompPersistent.PersistIfDirty(Data, m => m.ExampleData);
    }

    private new GamePlayer Owner => (base.Owner as GamePlayer)!;

    private MsgPack_DB_ExampleData Data => m_baseData!;
}

