// Copyright (c) Phoenix All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Phoenix.GameModel.Base;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Common.MessageHandlerRegistry;

namespace Phoenix.GameModel.Server
{
    public sealed class GamePlayerCompWorld : GamePlayerCompWorldBase, IServerDataComp
    {
        public GamePlayerCompWorld()
        {
        }

        public override bool Init()
        {
            if (!base.Init())
            {
                return false;
            }

            m_isInited = true;
            return true;
        }

        void IServerDataComp.SyncToClient()
        {
            PlayerWorldCompDataNtf dsNtf = new();
            dsNtf.WorldInfo = m_baseData;
            Owner.CompTransport.SendMessageToClient(dsNtf);
        }

        public void InitFromConfig() {
            m_baseData.CompletedStoryIds.Add(999);
            m_baseData.MarkDirty();
        }

        public void InitFromDB(object data)
        {
            m_baseData = (data as ServerPlayerCompDataWorldInfo)!;
        }

        void IServerDataComp.Persistent()
        {
            Owner.CompPersistent.PersistIfDirty(Data, m => m.World);
        }

        /// <summary>
        /// 完成一个故事
        /// </summary>
        /// <param name="storyId">故事ID</param>
        public void CompleteStory(int storyId)
        {
            if (!m_baseData.CompletedStoryIds.Contains(storyId))
            {
                m_baseData.CompletedStoryIds.Add(storyId);
                m_baseData.MarkDirty();
            }
        }

        private new GamePlayer Owner => (base.Owner as GamePlayer)!;

        private ServerPlayerCompDataWorldInfo Data => (m_baseData as ServerPlayerCompDataWorldInfo)!;
    }
}
