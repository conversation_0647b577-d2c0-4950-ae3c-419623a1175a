// Copyright (c) Phoenix All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Phoenix.GameModel.Base;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.Common;
using Phoenix.Server.Model;

namespace Phoenix.GameModel.Server;

public class GamePlayer : GamePlayerBase
{
    public GamePlayer(GamePlayerData playerModel)
    {
        PlayerId = playerModel.Id;

        // Add comps
        CompTransport = AddComp<GamePlayerCompTransport>();
        CompPersistent = AddComp<GamePlayerCompPersistent>();
        CompBasic = AddComp<GamePlayerCompBasic>();
        CompWorld = AddComp<GamePlayerCompWorld>();
        CompTest = AddComp<GamePlayerCompTest>();
        CompHakoniwa = AddComp<GamePlayerCompHakoniwa>();

        // Init Comp DB Data
        foreach (var comp in m_comps)
        {
            if (comp is IServerDataComp dataComp)
            {
                dataComp.InitFromDB(playerModel.GetCompPersistData(comp.Name)!);
            }
        }

        m_nextSave2DBTime = GetCurrentTime().AddSeconds(Save2DBPeriod);
    }

    public override async Task Tick()
    {
        var now = GetCurrentTime();

        await base.Tick();

        if (now >= m_nextSave2DBTime)
        {
            await Flush2DB();
        }
    }

    public DateTime GetCurrentTime()
    {
        return DateTime.Now;
    }

    public static int Save2DBPeriod => GameServerConfigureService.Instance.GamePlayerContextOptions.Save2DBPeriod;

    public void SyncToClient()
    {
        foreach (var comp in m_comps)
        {
            if (comp is IServerDataComp dataComp)
            {
                dataComp.SyncToClient();
            }
        }

        CompTransport.SendMessageToClient(NetMessageConstants.PlayerDataSectionSyncEndNtf);
    }

    public async Task Flush2DB()
    {
        foreach (var comp in m_comps)
        {
            if (comp is IServerDataComp dataComp)
            {
                dataComp.Persistent();
            }
        }

        try
        {
            var update = CompPersistent.CollectionBuilder.PlayerPartialUpdateBuilder.PartialBuilder;
            if (update != null)
            {
                await GameDatabaseService.Instance.UpdatePlayerAsync(PlayerId, update);
                s_logger.LogInformation("Player {PlayerId} data saved to DB", PlayerId);
            }
            CompPersistent.CollectionBuilder.PlayerPartialUpdateBuilder.Clear();
        }
        catch (Exception e)
        {
            s_logger.LogError(e, "Failed to persistent player data");
            throw;
            //todo xcm 如果mongo保存失败处理
        }

        m_nextSave2DBTime = GetCurrentTime().AddSeconds(Save2DBPeriod);
    }

    // public void RegisterCallback<TReq>(GamePlayerMessageCallback<TReq>.MsgPackMessageTCallback callback)
    //     where TReq : MsgPackStructBase
    // {
    //     int msgId = MsgPackProtoHelper.GetCmdIndexByType(typeof(TReq));
    //     m_clientMessageCallbacks.Add(msgId, new GamePlayerMessageCallback<TReq>(callback));
    // }

    // public Task OnClientMessage(MsgPackStructBase message)
    // {
    //     // m_clientMessageCallbacks.TryGetValue(message.GetType(), out var callback);
    //     // if (callback != null)
    //     // {
    //     //     // return callback.OnRpcMessage(message);
    //     // }
    //     //
    //     // s_logger.LogError($"Unhandled message: {message.ProtoCode}");
    //
    //     // 使用注册表尝试处理消息
    //     var handled = MessageHandlerRegistry.Instance.TryGetHandler(message);
    //     if (handled != null)
    //     {
    //         object instance = GetMessageExecInstance(handled.ExecType);
    //         return handled.Handler(instance, message);
    //     }
    //     s_logger.LogError($"Unhandled message: {message.ProtoCode}");
    //     return Task.CompletedTask;
    // }
    //
    // /// <summary>
    // /// 获取消息的执行实例，目前支持在Comp或者直接定义在GamePlayer
    // /// </summary>
    // /// <param name="execType"></param>
    // /// <returns></returns>
    // private object GetMessageExecInstance(Type execType)
    // {
    //     // 如果是组件的类型，直接返回组件实例
    //     if (m_compsByType.TryGetValue(execType, out var comp))
    //     {
    //         return comp;
    //     }
    //
    //     return this;
    // }

    public Task OnRpcMessage(RpcRequestInfo reqInfo, MsgPackStructBase message)
    {
        m_rpcMessageCallbacks.TryGetValue((int)message.ProtoCode, out IGamePlayerRpcMessageCallback? callback);
        {
            if (callback != null)
            {
                return callback.OnRpcMessage(reqInfo, message);
            }
        }
        return Task.CompletedTask;
    }

    public async Task TrySetName(string nickName)
    {
        await Flush2DB();

        if(CompBasic.IsNewbie())
        {
            string orgName = CompBasic.GetName();
            CompBasic.SetName(nickName);
            CompBasic.SetNewbieFlag(false);
            try
            {
                await Flush2DB();
            }
            catch (MongoDuplicateKeyException e)
            {
                CompBasic.SetName(orgName);
                CompBasic.SetNewbieFlag(true);
                s_logger.LogError(e, "Failed to set name");
            }

            //新号创建初始化数据
            foreach (var comp in m_comps)
            {
                if (comp is IServerDataComp dataComp)
                {
                    dataComp.InitFromConfig();
                }
            }

            await Flush2DB();
        }
    }

    private static readonly ILogger s_logger = InternalLoggerFactory.LoggerFactory.CreateLogger<GamePlayer>();

    //private readonly Dictionary<int, IGamePlayerMessageCallback> m_clientMessageCallbacks = new();

    private static readonly Dictionary<Type, Func<GamePlayerCompBase, MsgPackStructBase, Task>> m_clientMessageCallbacks = new();

    private readonly Dictionary<int, IGamePlayerRpcMessageCallback> m_rpcMessageCallbacks = new();

    public GamePlayerCompTransport CompTransport { get; private set; }

    public GamePlayerCompPersistent CompPersistent { get; private set; }

    public GamePlayerCompBasic CompBasic { get; protected set; }

    public GamePlayerCompTest CompTest { get; protected set; }

    public GamePlayerCompWorld CompWorld { get; private set; }
    
    public GamePlayerCompHakoniwa CompHakoniwa { get; private set; }
    
    private DateTime m_nextSave2DBTime;
}

public interface IGamePlayerMessageCallback
{
    Task OnRpcMessage(MsgPackStructBase message);
}

public sealed class GamePlayerMessageCallback<TReq> : IGamePlayerMessageCallback
    where TReq : MsgPackStructBase
{
    public delegate Task MsgPackMessageTCallback(TReq message);

    private readonly MsgPackMessageTCallback m_callback;

    public GamePlayerMessageCallback(MsgPackMessageTCallback callback) => m_callback = callback;

    public Task OnRpcMessage(MsgPackStructBase message)
    {
        TReq concrete = (TReq)message;
        return m_callback(concrete);
    }
}

// public interface IGamePlayerMessageDispatcher
// {
    // void RegisterCallback<TReq>(GamePlayerMessageCallback<TReq>.MsgPackMessageTCallback callback)
    //     where TReq : MsgPackStructBase;

    // Task OnClientMessage(MsgPackStructBase message);
// }

public interface IGamePlayerRpcMessageCallback
{
    Task OnRpcMessage(RpcRequestInfo reqInfo, MsgPackStructBase message);
}

public sealed class GamePlayerRpcMessageCallback<TReq> : IGamePlayerRpcMessageCallback
    where TReq : MsgPackStructBase
{
    public delegate Task MsgPackMessageTCallback(RpcRequestInfo reqInfo, TReq message);

    private readonly MsgPackMessageTCallback m_callback;

    public GamePlayerRpcMessageCallback(MsgPackMessageTCallback callback) => m_callback = callback;

    public Task OnRpcMessage(RpcRequestInfo reqInfo, MsgPackStructBase message)
    {
        TReq concrete = (TReq)message;
        return m_callback(reqInfo, concrete);
    }
}
