<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>disable</ImplicitUsings>
    <RootNamespace>Phoenix.Server.Model</RootNamespace>
    <Nullable>enable</Nullable>
    <Configurations>Debug;Release</Configurations>
    <Platforms>x64</Platforms>
    <AssemblyName>Phoenix.Server.Model</AssemblyName>
    <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
    <LangVersion>10</LangVersion>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <DefineConstants>$(DefineConstants);PHOENIX_SERVER</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <DefineConstants>$(DefineConstants);PHOENIX_SERVER</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MongoDB.Bson" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MongoDBWrapper\MongoDBWrapper.csproj" />
    <ProjectReference Include="..\Phoenix.Server.Common\Phoenix.Server.Common.csproj" />
    <ProjectReference Include="..\Phoenix.Server.Protocol\Phoenix.Server.Protocol.csproj" />
    <ProjectReference Include="..\Phoenix.SourceGenerator.PersistComp\Phoenix.SourceGenerator.PersistComp.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="**\*.meta" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="**\*.meta" />
  </ItemGroup>

</Project>
