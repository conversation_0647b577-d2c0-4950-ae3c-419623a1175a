<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AssemblyName>Phoenix.Server.Protocol</AssemblyName>
    <RootNamespace>Phoenix.Server.Protocol</RootNamespace>
    <LangVersion>10</LangVersion>
  </PropertyGroup>

  <PropertyGroup>
    <NoWarn>CS1591;CS8618</NoWarn>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DefineConstants>TRACE;PHOENIX_SERVER</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DefineConstants>TRACE;PHOENIX_SERVER</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="..\SourceGeneratorFiles\PropSourceGeneratorClient\PropGenerator\**\*.cs">
      <Link>%(Filename)%(Extension)</Link>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MessagePack" />
    <PackageReference Include="MessagePack.Annotations" />
    <PackageReference Include="MongoDB.Bson" />
  </ItemGroup>
</Project>
