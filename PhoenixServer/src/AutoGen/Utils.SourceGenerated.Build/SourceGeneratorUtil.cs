// Copyright (c) Phoenix.  All Rights Reserved.

using Scriban;

namespace Utils.SourceGenerated.Build
{
    /// <summary>
    /// Provides utility methods for generating source code.
    /// </summary>
    public static class SourceGeneratorUtil
    {
        /// <summary>
        /// Generates output content from a template file.
        /// </summary>
        /// <param name="templateFileName">The name of the template file.</param>
        /// <param name="model">The model to use for rendering the template.</param>
        /// <returns>The rendered content.</returns>
        public static string BuildOutputContextFromTemplateFile(string templateFileName, object model)
        {
            // Get the template from the TemplateManager.
            Template template = TemplateManager.GetTemplate(templateFileName);

            // Render the code from the template using the model and return the result.
            return template.RenderCode(model);
        }
    }
}
