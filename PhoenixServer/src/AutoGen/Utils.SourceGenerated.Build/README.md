### 简介

这是一个代码生成器的工具库，主要包含了一些常用的类，以及一些常用的模板.

### 模板文件语法

使用 [scriban](https://github.com/scriban/scriban)
作为模板生成引擎,具体的模板文件语法可以查看 [doc](https://github.com/scriban/scriban/tree/master/doc), 有详细的描述.

### 注意事项

1. 模板文件统一放至在工程中的 templates 目录下，模板文件名的后缀名为 sbntxt. 例如 example.sbntxt, 文件的模板名称为
   example.
2. 在工程 csproj 中需要添加如下配置，以便在运行是会将模板文件拷贝至相应目录下.

```
<ItemGroup>
    <None Include="$(ProjectDir)templates\*.sbntxt" CopyToOutputDirectory="Always" />
</ItemGroup>
```
