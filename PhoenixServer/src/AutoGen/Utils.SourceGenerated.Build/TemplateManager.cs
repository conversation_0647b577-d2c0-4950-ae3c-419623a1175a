// Copyright (c) Phoenix.  All Rights Reserved.

using System;
using System.IO;
using Scriban;
using Scriban.Runtime;

namespace Utils.SourceGenerated.Build
{
    /// <summary>
    ///     Manages templates for source generation.
    /// </summary>
    internal static class TemplateManager
    {
        /// <summary>
        ///     The path to the directory containing the templates.
        /// </summary>
        private static readonly string s_templatePath;

        /// <summary>
        ///     Initializes the <see cref="TemplateManager" /> class.
        /// </summary>
        static TemplateManager() => s_templatePath = Path.Combine(AppContext.BaseDirectory, "templates");

        /// <summary>
        ///     Tries to get the template string from a file.
        /// </summary>
        /// <param name="templateName">The name of the template.</param>
        /// <param name="result">The template string, if found.</param>
        /// <returns>true if the template string was found; otherwise, false.</returns>
        private static bool TryGetTemplateString(string templateName, out string result)
        {
            // Combine the template path with the template name to get the full path.
            string fullPath = Path.Combine(s_templatePath, $"{templateName}.sbntxt");

            // If the file exists, read the text and return true.
            if (File.Exists(fullPath))
            {
                result = File.ReadAllText(fullPath);
                return true;
            }

            // If the file does not exist, set the result to null and return false.
            result = null;
            return false;
        }

        /// <summary>
        ///     Gets the template string from a file.
        /// </summary>
        /// <param name="templateName">The name of the template.</param>
        /// <returns>The template string.</returns>
        /// <exception cref="FileNotFoundException">Thrown when the template file is not found.</exception>
        private static string GetTemplateString(string templateName)
        {
            // Try to get the template string.
            if (TryGetTemplateString(templateName, out string strTpl))
            {
                return strTpl;
            }

            // If the template string was not found, throw an exception.
            throw new FileNotFoundException($"can't find {templateName}.sbntxt in paths:{s_templatePath}");
        }

        /// <summary>
        ///     Gets the parsed template from a file.
        /// </summary>
        /// <param name="templateName">The name of the template.</param>
        /// <returns>The parsed template.</returns>
        public static Template GetTemplate(string templateName) => Template.Parse(GetTemplateString(templateName));
    }

    /// <summary>
    ///     Represents an extension of the <see cref="ScriptObject" /> class for type templates.
    /// </summary>
    internal class TypeTemplateExtends : ScriptObject
    {
    }

    /// <summary>
    ///     Provides an extension method for rendering code from a template.
    /// </summary>
    public static class RenderExtension
    {
        /// <summary>
        ///     Renders code from a template using a model.
        /// </summary>
        /// <param name="template">The template.</param>
        /// <param name="model">The model.</param>
        /// <returns>The rendered code.</returns>
        public static string RenderCode(this Template template, object model)
        {
            // Create a new template context.
            TemplateContext ctx = new TemplateContext
            {
                LoopLimit = 0, NewLine = "\n", MemberRenamer = member => member.Name
            };

            // Create a new environment for the template.
            TypeTemplateExtends env = new TypeTemplateExtends { ["x"] = model };

            // Push the environment onto the context's global stack.
            ctx.PushGlobal(env);

            // Render the template with the context and return the result.
            return template.Render(ctx);
        }
    }
}
