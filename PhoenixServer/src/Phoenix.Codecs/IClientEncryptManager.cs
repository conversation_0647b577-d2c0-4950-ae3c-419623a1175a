// Copyright (c) Phoenix All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Phoenix.Codecs;
public interface IClientEncryptManager
{
    /// <summary>
    /// Generate XOR random byte code
    /// </summary>
    byte GenerateXORCode();

    /// <summary>
    /// Generate XOR random byte code by source string
    /// </summary>
    /// <param name="strSource"></param>
    byte GenerateXORCode(String strSource);

    /// <summary>
    /// set XOR by id
    /// </summary
    /// <param name="id"></param>
    /// <param name="rCode"></param>
    void SetXORCode(String id, byte rCode);

    /// <summary>
    /// get XOR by id
    /// </summary
    /// <param name="id"></param>
    byte GetXORCode(String id);

    /// <summary>
    /// remove XOR by id
    /// </summary
    /// <param name="id"></param>
    void RemoveXORCode(String id);

    /// <summary>
    /// check protoCode is except to XOR
    /// </summary
    /// <param name="iProtoCode"></param>
    bool IsExceptProtoCode(int iProtoCode);
}
