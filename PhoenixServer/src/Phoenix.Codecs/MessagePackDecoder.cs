using System;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Codecs
{
    public sealed class MessagePackDecoder<T> : MessageToMessageDecoder<IByteBuffer>
    {
        protected override void Decode(IChannelHandlerContext context, IByteBuffer message, List<object> output)
        {
            Contract.Requires(context != null);
            Contract.Requires(message != null);
            Contract.Requires(output != null);
            Contract.Requires(message != null && message.ReadableBytes > 0);

            if (message == null)
            {
                return;
            }

            if (message.ReadableBytes < 4)
            {
                // 如果没有足够字节，退出
                return;
            }

            try
            {
                int protoCode = message.ReadInt();
                var memory = new ReadOnlyMemory<byte>(message.Array, message.ArrayOffset + message.ReaderIndex,
                    message.ReadableBytes);
                MsgPackStructBase? msgPack = MsgPackProtoHelper.Deserialize(protoCode, memory);
                if (msgPack != null)
                {
                    output!.Add(msgPack);
                }
            }
            catch (Exception exception)
            {
                throw new CodecException(exception);
            }
        }
    }
}
