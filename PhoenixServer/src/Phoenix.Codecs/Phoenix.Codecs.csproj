<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>disable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Configurations>Release;Debug</Configurations>
    <Platforms>x64</Platforms>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DotNetty.Buffers" />
    <PackageReference Include="DotNetty.Codecs" />
    <PackageReference Include="DotNetty.Common" />
    <PackageReference Include="MessagePack" />
    <PackageReference Include="Microsoft.Extensions.ObjectPool" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Phoenix.Server.Protocol\Phoenix.Server.Protocol.csproj" />
  </ItemGroup>

</Project>
