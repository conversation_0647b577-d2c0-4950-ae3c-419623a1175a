// Copyright (c) Phoenix.  All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;
namespace Phoenix.Codecs;

public class EncryptionEncode : MessageToMessageEncoder<IByteBuffer>
{
    private readonly IClientSecretsManager m_clientSecretsManager;
    private Aes? m_aes = null;

    public EncryptionEncode(IClientSecretsManager clientSecretsManager)
    {
        m_clientSecretsManager = clientSecretsManager;
    }

    private void SetAesKey(string aesKey)
    {
        m_aes = Aes.Create();
        m_aes.Key = Convert.FromBase64String(aesKey);
    }

    protected override void Encode(IChannelHandlerContext context, IByteBuffer message, List<object> output)
    {
        short messageId = message.ReadShortLE();
        switch (messageId)
        {
            case MessageConstants.HandShakeAckMsgId:
                // handshake message, no encrypt
                message.SetReaderIndex(0);
                output.Add(message.Retain());
                return;

            // these two cases only running in mini client
            case MessageConstants.HandShakeReqMsgId:
            case MessageConstants.LoginByAuthTokenReqMsgId:
                // LoginByAuthTokenReqMsg should RsaEncrypt by client, miniClient doesn't do RsaEncrypt
                message.SetReaderIndex(0);
                output.Add(message.Retain());
                return;
        }

        if (m_aes == null)
        {
            string sessionId = context.GetAttribute(MessageConstants.SessionId).Get();
            string aesKey = m_clientSecretsManager.GetAesKey(sessionId);
            if (aesKey == string.Empty)
            {
                throw new CodecException("encrypt key not set");
            }
            SetAesKey(aesKey);
        }

        // AES encrypt
        ushort flags = message.ReadUnsignedShortLE();
        flags |= MessageConstants.AesEncrypted;
        try
        {
            byte[] cipherText = m_aes!.EncryptEcb(message.GetIoBuffer().ToArray(), PaddingMode.PKCS7);

            // allocate buffer
            IByteBuffer buffer = context.Allocator.Buffer(MessageConstants.IdLength + MessageConstants.FlagLength + cipherText.Length);
            // write message id
            buffer.WriteShortLE(messageId);
            // write flags
            buffer.WriteUnsignedShortLE(flags);
            // write message body
            buffer.WriteBytes(cipherText);
            output.Add(buffer);
        }
        catch (Exception exception)
        {
            throw new CodecException(exception);
        }

    }
}
