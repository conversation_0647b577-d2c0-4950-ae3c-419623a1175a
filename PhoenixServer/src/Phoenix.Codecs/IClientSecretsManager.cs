// Copyright (c) Phoenix.  All Rights Reserved.

using System.Security.Cryptography;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Phoenix.Codecs;

public interface IClientSecretsManager
{
    /// <summary>
    /// Generate RSA key
    /// </summary>
    /// <param name="id"> client id </param>
    void GenerateRsaSecrets(string id);

    /// <summary>
    /// Get public key
    /// </summary>
    /// <param name="id"></param>
    /// <param name="pubExp">Exponent in little endian</param>
    /// <param name="modulus">Modulus in little endian</param>
    void GetLittleEndianPubKey(string id, ref string pubExp, ref string modulus);

    /// <summary>
    /// Get private key
    /// </summary>
    /// <param name="id"></param>
    /// <returns>RSA key value in Xml format</returns>
    string GetPriKeyXml(string id);

    /// <summary>
    /// Set AES key
    /// </summary>
    /// <param name="id"></param>
    /// <param name="inAesKey"></param>
    void SetAesKey(string id, string inAesKey);

    /// <summary>
    /// Get AES key
    /// </summary>
    /// <param name="id"></param>
    /// <returns>AES key</returns>
    string GetAesKey(string id);

    /// <summary>
    /// Remove secret
    /// </summary>
    /// <param name="id"></param>
    void RemoveSecret(string id);

    /// <summary>
    /// Generate Rsa keys
    /// </summary>
    /// <param name="keyPairs">num of key pair to prepare</param>
    void PrepareRsaKeys(int keyPairs);
}

