// Copyright (c) Phoenix.  All Rights Reserved.

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Security.Cryptography;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;

namespace Phoenix.Codecs;

public class EncryptionDecode : MessageToMessageDecoder<IByteBuffer>
{
    private readonly IClientSecretsManager m_clientSecretsManager;
    private RSACryptoServiceProvider? m_rsaDecrypt = null;
    private Aes? m_aes = null;
    public EncryptionDecode(IClientSecretsManager clientSecretsManager)
    {
        m_clientSecretsManager = clientSecretsManager;
    }

    private void SetAesKey(string inAesKey)
    {
        m_aes = Aes.Create();
        m_aes.Key = Convert.FromBase64String(inAesKey);
    }

    private void SetRsaKey(string xmlKey)
    {
        m_rsaDecrypt = new RSACryptoServiceProvider();
        m_rsaDecrypt.FromXmlString(xmlKey);
    }

    private byte[] RsaDecrypt(IChannelHandlerContext context, byte[] cipherText)
    {
        if (m_rsaDecrypt != null)
        {
            return m_rsaDecrypt!.Decrypt(cipherText, false);
        }

        string xmlKey = m_clientSecretsManager.GetPriKeyXml(context.GetAttribute(MessageConstants.SessionId).Get());
        if (xmlKey == string.Empty)
        {
            throw new CodecException("encrypt key not set");
        }

        SetRsaKey(xmlKey);

        return m_rsaDecrypt!.Decrypt(cipherText, false);
    }

    private byte[] AesDecrypt(IChannelHandlerContext context, byte[] cipherText)
    {
        if (m_aes != null)
        {
            return m_aes!.DecryptEcb(cipherText, PaddingMode.PKCS7);
        }

        string aesKey = m_clientSecretsManager.GetAesKey(context.GetAttribute(MessageConstants.SessionId).Get());
        if (aesKey == string.Empty)
        {
            throw new CodecException("encrypt key not set");
        }

        SetAesKey(aesKey);

        return m_aes!.DecryptEcb(cipherText, PaddingMode.PKCS7);
    }
    protected override void Decode(IChannelHandlerContext context, IByteBuffer message, List<object> output)
    {
        short messageId = message.ReadShortLE();
        ushort flags = message.ReadUnsignedShortLE();
        if ((flags & MessageConstants.RsaEncrypted) == 0 && (flags & MessageConstants.AesEncrypted) == 0)
        {
            message.SetReaderIndex(0);
            output.Add(message.Retain());
            return;
        }

        try
        {
            byte[] plainText = Array.Empty<byte>();
            if ((flags & MessageConstants.RsaEncrypted) != 0)
            {
                plainText = RsaDecrypt(context, message.GetIoBuffer().ToArray());
            }

            if ((flags & MessageConstants.AesEncrypted) != 0)
            {
                plainText = AesDecrypt(context, message.GetIoBuffer().ToArray());
            }

            // allocate buffer
            IByteBuffer buffer = context.Allocator.Buffer(MessageConstants.IdLength + MessageConstants.FlagLength + plainText.Length);
            // write message id
            buffer.WriteShortLE(messageId);
            // write flags
            buffer.WriteUnsignedShortLE(flags);
            // write message body
            buffer.WriteBytes(plainText);
            output.Add(buffer);
        }
        catch (Exception e)
        {
            throw new CodecException(e);
        }
    }
}
