// Copyright (c) Phoenix.  All Rights Reserved.

using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;

namespace Phoenix.Codecs
{
    /// <summary>
    /// CompressDecoder is a class that extends MessageToMessageDecoder and is used to decompress messages.
    /// </summary>
    public class CompressDecoder : MessageToMessageDecoder<IByteBuffer>
    {
        /// <summary>
        /// Initializes a new instance of the CompressDecoder class with default compression level and threshold.
        /// </summary>
        public CompressDecoder() : this(CompressionLevel.Fastest, 1024)
        {
        }

        /// <summary>
        /// Initializes a new instance of the CompressDecoder class with specified compression level and threshold.
        /// </summary>
        /// <param name="compressionLevel">The compression level to use.</param>
        /// <param name="compressionThreshold">The threshold in bytes above which messages will be compressed.</param>
        public CompressDecoder(CompressionLevel compressionLevel, int compressionThreshold)
        {
            m_compressionLevel = compressionLevel;
            m_compressionThreshold = compressionThreshold;
        }

        /// <summary>
        /// Gets a value indicating whether the current instance is shareable.
        /// </summary>
        public override bool IsSharable => true;

        /// <summary>
        /// Decodes the specified message into another message and adds it to the list of inbound messages.
        /// </summary>
        /// <param name="context">The context of the channel handler.</param>
        /// <param name="message">The message to decode.</param>
        /// <param name="output">The list of inbound messages.</param>
        protected override void Decode(IChannelHandlerContext context, IByteBuffer message, List<object> output)
        {
            try
            {
                // Read message ID and flags
                short messageId = message.ReadShortLE();
                ushort flags = message.ReadUnsignedShortLE();

                // Check if the message is compressed
                if ((flags & MessageConstants.IsCompressed) == 0)
                {
                    // Not compressed, reset reader index and pass through
                    message.SetReaderIndex(0);
                    output.Add(message.Retain());
                    return;
                }

                // Message is compressed, read the uncompressed size
                int uncompressedSize = message.ReadIntLE();

                // Validate uncompressed size
                if (uncompressedSize <= 0 || uncompressedSize > MessageConstants.NeedCompressSize)
                {
                    throw new DecompressionException($"Invalid uncompressed size: {uncompressedSize}");
                }

                // Get the compressed data
                byte[] compressedData = new byte[message.ReadableBytes];
                message.ReadBytes(compressedData);

                // Decompress the data
                byte[] decompressedData = DecompressData(compressedData, uncompressedSize);

                // Create a new buffer with the decompressed data
                IByteBuffer buffer = context.Allocator.Buffer(MessageConstants.IdLength + MessageConstants.FlagLength + decompressedData.Length);

                // Write message ID
                buffer.WriteShortLE(messageId);

                // Write flags without compression flag
                buffer.WriteUnsignedShortLE((ushort)(flags & ~MessageConstants.IsCompressed));

                // Write decompressed data
                buffer.WriteBytes(decompressedData);

                output.Add(buffer);
            }
            catch (Exception e)
            {
                throw new DecompressionException("Decompression failed", e);
            }
        }

        /// <summary>
        /// Decompresses the data using GZip algorithm.
        /// </summary>
        /// <param name="compressedData">The compressed data.</param>
        /// <param name="uncompressedSize">The expected uncompressed size.</param>
        /// <returns>The decompressed data.</returns>
        private byte[] DecompressData(byte[] compressedData, int uncompressedSize)
        {
            byte[] decompressedData = new byte[uncompressedSize];

            using (var compressedStream = new MemoryStream(compressedData))
            using (var decompressStream = new GZipStream(compressedStream, CompressionMode.Decompress))
            {
                int bytesRead = decompressStream.Read(decompressedData, 0, uncompressedSize);

                if (bytesRead != uncompressedSize)
                {
                    throw new DecompressionException($"Decompressed size mismatch. Expected: {uncompressedSize}, Actual: {bytesRead}");
                }
            }

            return decompressedData;
        }

        private readonly CompressionLevel m_compressionLevel;
        private readonly int m_compressionThreshold;
    }

    /// <summary>
    /// Exception thrown when decompression fails.
    /// </summary>
    public class DecompressionException : Exception
    {
        public DecompressionException(string message) : base(message)
        {
        }

        public DecompressionException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }
}
