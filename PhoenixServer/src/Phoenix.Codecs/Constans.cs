using DotNetty.Common.Utilities;

namespace Phoenix.Codecs;

/// <summary>
/// Contains constant values related to protobuf messages.
/// </summary>
public static class MessageConstants
{
    /// <summary>
    /// The size in bytes of the message ID in the message packet.
    /// </summary>
    public const int IdLength = 2;

    /// <summary>
    /// The size in bytes that requires compression.
    /// </summary>
    public const int NeedCompressSize = 1024;

    /// <summary>
    /// Flag indicating whether the message is compressed.
    /// </summary>
    public const ushort IsCompressed = 1 << 1;

    /// <summary>
    /// Flag indicating whether the message is encrypted with RSA.
    /// </summary>
    public const ushort RsaEncrypted = 1 << 2;

    /// <summary>
    /// Flag indicating whether the message is encrypted with AES.
    /// </summary>
    public const ushort AesEncrypted = 1 << 3;

    /// <summary>
    /// The length in bytes of the flag.
    /// </summary>
    public const int FlagLength = 2;

    /// <summary>
    /// The message ID for the handshake request.
    /// </summary>
    public const int HandShakeReqMsgId = 132;

    /// <summary>
    /// The message ID for the handshake acknowledgement.
    /// </summary>
    public const int HandShakeAckMsgId = 133;

    /// <summary>
    /// The message ID for the login request by auth token.
    /// </summary>
    public const int LoginByAuthTokenReqMsgId = 1;

    /// <summary>
    /// The session ID attribute key.
    /// </summary>
    public static readonly AttributeKey<string> SessionId =
        AttributeKey<string>.ValueOf("SessionId");

    /// <summary>
    /// Key used to store the XORCode.
    /// </summary>
    public static readonly AttributeKey<string> XORCode =
        AttributeKey<string>.ValueOf("XORCode");
}
