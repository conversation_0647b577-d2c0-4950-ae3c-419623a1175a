using System;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using System.IO;
using System.Threading;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Codecs
{
    public sealed class MessagePackEncoder<T> : MessageToMessageEncoder<T> where T : MsgPackStructBase
    {
        protected override void Encode(IChannelHandlerContext context, T message, List<object> output)
        {
            Contract.Requires(context != null);
            Contract.Requires(message != null);
            Contract.Requires(output != null);

            IByteBuffer? buffer = null;

            try
            {
                var stream = m_localMemoryStream.Value!;
                stream.Position = 0;
                stream.SetLength(0);

                int protoCode = (int)message!.ProtoCode;
                MsgPackProtoHelper.Serialize(stream, message);
                stream.Flush();
                buffer = context!.Allocator.Buffer(4 + (int)stream.Length);
                buffer.WriteInt(protoCode);
                if (stream.TryGetBuffer(out ArraySegment<byte> segment))
                {
                    buffer.WriteBytes(segment.Array, segment.Offset, (int)stream.Length);
                }
                output!.Add(buffer);
                buffer = null;

                if (message is ForwardClientMessageToServerNtf forwardMessage)
                {
                    ForwardClientMessageToServerNtfPool.Instance.Return(forwardMessage);
                }
            }
            catch (Exception exception)
            {
                throw new CodecException(exception);
            }
            finally
            {
                buffer?.Release();
            }
        }

        private readonly ThreadLocal<MemoryStream> m_localMemoryStream = new(() => new MemoryStream());
    }
}
