// Copyright (c) Phoenix.  All Rights Reserved.

using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;

namespace Phoenix.Codecs
{
    /// <summary>
    /// CompressEncoder is a class that extends MessageToMessageEncoder and is used to compress messages.
    /// </summary>
    public class CompressEncoder : MessageToMessageEncoder<IByteBuffer>
    {
        /// <summary>
        /// Initializes a new instance of the CompressEncoder class with default compression level and threshold.
        /// </summary>
        public CompressEncoder() : this(CompressionLevel.Fastest, 1024)
        {
        }

        /// <summary>
        /// Initializes a new instance of the CompressEncoder class with specified compression level and threshold.
        /// </summary>
        /// <param name="compressionLevel">The compression level to use.</param>
        /// <param name="compressionThreshold">The threshold in bytes above which messages will be compressed.</param>
        public CompressEncoder(CompressionLevel compressionLevel, int compressionThreshold)
        {
            m_compressionLevel = compressionLevel;
            m_compressionThreshold = compressionThreshold;
        }

        /// <summary>
        /// Gets a value indicating whether the current instance is shareable.
        /// </summary>
        public override bool IsSharable => true;

        /// <summary>
        /// Encodes the specified message into another message and adds it to the list of outbound messages.
        /// </summary>
        /// <param name="context">The context of the channel handler.</param>
        /// <param name="message">The message to encode.</param>
        /// <param name="output">The list of outbound messages.</param>
        protected override void Encode(IChannelHandlerContext context, IByteBuffer message, List<object> output)
        {
            try
            {
                // If the message is smaller than the compression threshold, add it to the output as is
                if (message.ReadableBytes <= m_compressionThreshold)
                {
                    output.Add(message.Retain());
                    return;
                }

                // Read message ID and flags
                short messageId = message.ReadShortLE();
                ushort flags = message.ReadUnsignedShortLE();

                // Get the uncompressed data
                int uncompressedSize = message.ReadableBytes;
                byte[] uncompressedData = new byte[uncompressedSize];
                message.ReadBytes(uncompressedData);

                // Compress the data
                byte[] compressedData = CompressData(uncompressedData);

                // If compression didn't reduce the size, use the original message
                if (compressedData.Length >= uncompressedSize)
                {
                    message.SetReaderIndex(0);
                    output.Add(message.Retain());
                    return;
                }

                // Create a new buffer with the compressed data
                IByteBuffer buffer = context.Allocator.Buffer(
                    MessageConstants.IdLength +
                    MessageConstants.FlagLength +
                    sizeof(int) +  // Uncompressed size
                    compressedData.Length);

                // Write message ID
                buffer.WriteShortLE(messageId);

                // Write flags with compression flag
                buffer.WriteUnsignedShortLE((ushort)(flags | MessageConstants.IsCompressed));

                // Write uncompressed size
                buffer.WriteIntLE(uncompressedSize);

                // Write compressed data
                buffer.WriteBytes(compressedData);

                output.Add(buffer);
            }
            catch (Exception)
            {
                // s_logger.LogError(ex, "Error compressing message");
                // In case of error, pass through the original message
                message.SetReaderIndex(0);
                output.Add(message.Retain());
            }
        }

        /// <summary>
        /// Compresses the data using GZip algorithm.
        /// </summary>
        /// <param name="uncompressedData">The uncompressed data.</param>
        /// <returns>The compressed data.</returns>
        private byte[] CompressData(byte[] uncompressedData)
        {
            using var memoryStream = new MemoryStream();
            using (var gzipStream = new GZipStream(memoryStream, m_compressionLevel))
            {
                gzipStream.Write(uncompressedData, 0, uncompressedData.Length);
            }

            return memoryStream.ToArray();
        }

        private readonly CompressionLevel m_compressionLevel;
        private readonly int m_compressionThreshold;
    }
}
