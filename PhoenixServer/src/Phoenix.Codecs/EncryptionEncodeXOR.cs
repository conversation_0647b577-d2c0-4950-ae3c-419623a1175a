// Copyright (c) Phoenix.  All Rights Reserved.

using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;

namespace Phoenix.Codecs;

public class EncryptionEncodeXOR : MessageToMessageEncoder<IByteBuffer>
{
    private readonly IClientEncryptManager m_clientEncryptManager;

    public EncryptionEncodeXOR(IClientEncryptManager clientEncryptManager)
    {
        m_clientEncryptManager = clientEncryptManager;
    }

    protected override void Encode(IChannelHandlerContext context, IByteBuffer message, List<object> output)
    {
        IByteBuffer? buffer = null;
        try
        {
            Int32 protoCode = message.ReadInt();
            Int32 iOffset = message.ArrayOffset + message.ReaderIndex;
            if (!m_clientEncryptManager.IsExceptProtoCode(protoCode))
            {
                string strXORCode = context.GetAttribute(MessageConstants.XORCode).Get();
                byte xorCode = 0;
                byte.TryParse(strXORCode, out xorCode);
                for (int i = 0; i < message.ReadableBytes; i++)
                {
                    message.Array[iOffset + i] ^= xorCode;
                }
            }

            buffer = context!.Allocator.Buffer(4 + message.ReadableBytes);
            buffer.WriteInt(protoCode);
            buffer.WriteBytes(message.Array, iOffset, message.ReadableBytes);
            output!.Add(buffer);

            buffer = null;
        }
        catch (Exception e)
        {
            throw new CodecException(e);
        }
        finally
        {
            buffer?.Release();
        }


    }
}
