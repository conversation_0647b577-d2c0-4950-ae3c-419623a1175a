using System;
using System.Buffers;
using System.Collections.Generic;
using System.Diagnostics.Contracts;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Transport.Channels;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Codecs
{
    public sealed class MessagePackDecoder4Gate<T> : MessageToMessageDecoder<IByteBuffer>
    {
        protected override void Decode(IChannelHandlerContext context, IByteBuffer message, List<object> output)
        {
            Contract.Requires(context != null);
            Contract.Requires(message != null);
            Contract.Requires(output != null);
            Contract.Requires(message != null && message.ReadableBytes > 0);

            if (message == null)
            {
                return;
            }

            if (message.ReadableBytes < 4)
            {
                // 如果没有足够字节，退出
                return;
            }

            try
            {
                int messageId = message.ReadInt();
                var memory = new ReadOnlyMemory<byte>(message.Array, message.ArrayOffset + message.ReaderIndex,
                    message.ReadableBytes);
                if (MsgPackProtoHelper.IsProtocolFromClient(messageId))
                {
                    MsgPackStructBase? msgPack;
                    if (IsHandleInGate(messageId))
                    {
                        msgPack = MsgPackProtoHelper.Deserialize(messageId, memory);
                    }
                    else
                    {
                        var forwardClientMsg = ForwardClientMessageToServerNtfPool.Instance.Get();
                        forwardClientMsg.MsgProtoCode = messageId;
                        byte[] data = ArrayPool<byte>.Shared.Rent(memory.Length);
                        memory.CopyTo(data);
                        forwardClientMsg.Data = data;
                        msgPack = forwardClientMsg;
                    }

                    if (msgPack != null)
                    {
                        output!.Add(msgPack);
                    }
                }
                else
                {
                    throw new ProtocolSourceInvalidException(messageId);
                }
            }
            catch (ProtocolSourceInvalidException)
            {
                throw;
            }
            catch (Exception exception)
            {
                throw new CodecException(exception);
            }
        }

        private static bool IsHandleInGate(int messageId)
        {
            return messageId == (int)EProtoCode.GLOBALSYNC_HEARTBEAT_REQ ||
                   messageId == (int)EProtoCode.LOGIN_LOGINBYAUTHTOKENREQ ||
                   messageId == (int)EProtoCode.LOGIN_LOGINBYSESSIONTOKENREQ;
        }
    }

    public class ProtocolSourceInvalidException : Exception
    {
        public ProtocolSourceInvalidException(int protoId) : base($"ProtocolSourceInvalidException ProtoId:{protoId}")
        {
        }
    }
}
