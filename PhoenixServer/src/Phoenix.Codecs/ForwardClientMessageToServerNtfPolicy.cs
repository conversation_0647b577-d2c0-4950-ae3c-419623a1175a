// Copyright (c) Phoenix All Rights Reserved.

using System;
using System.Buffers;
using Microsoft.Extensions.ObjectPool;
using Phoenix.MsgPackLogic.Protocol;

namespace Phoenix.Codecs;

internal sealed class ForwardClientMessageToServerNtfPolicy : IPooledObjectPolicy<ForwardClientMessageToServerNtf>
{
    public ForwardClientMessageToServerNtf Create()
    {
        return new ForwardClientMessageToServerNtf();
    }

    public bool Return(ForwardClientMessageToServerNtf message)
    {
        ArrayPool<byte>.Shared.Return(message.Data);
        message.Data = Array.Empty<byte>();
        message.FromGate = 0;
        message.PlayerId = 0;
        message.MsgProtoCode = 0;
        return true;
    }
}
