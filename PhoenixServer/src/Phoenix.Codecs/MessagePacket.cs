/*
using System;
using MessagePack;

namespace Phoenix.Common.Network
{
    [MessagePackObject]
    public struct MessagePacket
    {
        public MessagePacket()
        {
        }

        [Key(0)]
        public int MessageTypeId { get; set; } // 消息类型ID

        [Key(1)]
        public int MessageId { get; set; } // 消息唯一ID

        [Key(2)]
        public byte[] Data { get; set; } = Array.Empty<byte>(); // 消息内容（byte数组）
    }
}
*/
