<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <Configurations>Debug;Release</Configurations>
        <Platforms>x64</Platforms>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <OutputPath>bin\x64\Debug\</OutputPath>
      <PlatformTarget>x64</PlatformTarget>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <OutputPath>bin\x64\Release\</OutputPath>
      <PlatformTarget>x64</PlatformTarget>
    </PropertyGroup>

    <ItemGroup>
      <Folder Include="ProtocolGen\" />
      <Folder Include="Protocol\" />
    </ItemGroup>
    
    <ItemGroup>
        <None Remove="**\*.meta" />
    </ItemGroup>
    <ItemGroup>
      <PackageReference Include="MessagePack" />
    </ItemGroup>
</Project>
