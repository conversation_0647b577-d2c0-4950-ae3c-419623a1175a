namespace Phoenix.Server.MongoDBWrapper;

/// <summary>
///     需要写数据库的Collections
/// </summary>
public class MongoDBCollectionBuilder
{
    /// <summary>
    ///     string为colletion名
    /// </summary>
    public Dictionary<string, MongoDBDeleteDocumentsBuilder> deleteCollections = new();

    /// <summary>
    ///     string为colletion名
    /// </summary>
    public Dictionary<string, MongoDBInsertDocumentsBuilder> insertCollections = new();

    /// <summary>
    ///     string1为colletion名, string2是document的filter名
    /// </summary>
    public Dictionary<string, Dictionary<string, MongoDBUpdateDocumentBuilder>> updateCollections = new();
}
