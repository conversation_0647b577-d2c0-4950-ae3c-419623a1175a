namespace Phoenix.Server.MongoDBWrapper
{
    public class MongoDBConfigInfo
    {
        /// <summary>
        /// 数据库id
        /// </summary>
        public Int32 Id { get; set; }

        /// <summary>
        /// 是否全局数据库
        /// </summary>
        public bool IsGlobal { get; set; }

        /// <summary>
        /// 数据库名
        /// </summary>
        public String DataBase { get; set; } = "";

        /// <summary>
        /// 目标的ip:port信息
        /// </summary>
        public String ConnectHost { get; set; } = "";

        /// <summary>
        /// 使用的ReplicaSet name，生产环境必须使用
        /// </summary>
        public String ReplicaSet { get; set; } = "";

        /// <summary>
        /// 用户名
        /// </summary>
        public String UserName { get; set; } = "";

        /// <summary>
        /// 密码
        /// </summary>
        public String Password { get; set; } = "";

        /// <summary>
        /// 是否连接的是mongos
        /// </summary>
        public bool IsMongos { get; set; }

        /// <summary>
        /// 检查两个数据库配置项是否一致
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        public bool CheckSameDBConfig(MongoDBConfigInfo info)
        {
            if (Id != info.Id ||
                DataBase != info.DataBase ||
                ConnectHost != info.ConnectHost ||
                ReplicaSet != info.ReplicaSet ||
                UserName != info.UserName ||
                Password != info.Password ||
                IsMongos != info.IsMongos)
            {
                return false;
            }
            return true;
        }
    }
}
