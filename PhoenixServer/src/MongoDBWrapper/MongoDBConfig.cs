using Newtonsoft.Json;

namespace Phoenix.Server.MongoDBWrapper;

public record MongoDBConfig
{
    /// <summary>
    ///     数据库名, 例如: GameDB_Lu
    /// </summary>
    [JsonRequired]
    public string DataBaseName { get; set; } = string.Empty;

    /// <summary>
    ///     复制集信息
    /// </summary>
    public string? ReplicaSet { get; set; }

    /// <summary>
    ///     数据库域名，例如:host1:host2:host3
    /// </summary>
    [JsonRequired]
    public string Host { get; set; } = string.Empty;

    /// <summary>
    ///     数据库端口,例如:port1:port2:port3
    /// </summary>
    [JsonRequired]
    public string Port { get; set; } = string.Empty;

    /// <summary>
    ///     数据库账号名,例如:admin
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    ///     数据库密码,例如:password
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    ///     是否启动分片
    /// </summary>
    public bool IsMongos { get; set; }
}
