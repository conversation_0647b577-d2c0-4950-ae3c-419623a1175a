using System.Collections.Concurrent;
using System.Linq.Expressions;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Phoenix.Server.MongoDBWrapper;

/// <summary>
///     MongoDB helper
/// </summary>
public class MongoDBOperationHelper
{
    /// <summary>
    ///     配置文件读到的数据库配置
    /// </summary>
    private readonly MongoDBConfig m_dbConfig = default!;

    private IMongoDatabase m_adminDBEntry = default!;

    private readonly ConcurrentDictionary<string, Tuple<int, DateTime>> m_estimatedCollectionRecords = new();

    /// <summary>
    ///     由配置文件配置数据生成的数据库entry，保存在这里，后面再使用的时候直接从这里取，提升效率
    /// </summary>
    private IMongoDatabase m_gameDBEntry;

    private bool m_gameDBEntryInit;

    /// <summary>
    ///     是否连接到mongos，决定是否需要使用分片逻辑
    /// </summary>
    private bool m_isConnectMongos;

    /// <summary>
    ///     构造函数
    /// </summary>
    public MongoDBOperationHelper(MongoDBConfig dbConfig, ReadPreference? readPreference = null)
    {
        m_dbConfig = dbConfig;
        m_gameDBEntry = GetDataBaseEntry(readPreference);
    }

    /// <summary>
    ///     根据配置文件中的配置项获取游戏服务器的数据库操作实体
    /// </summary>
    /// <returns></returns>
    private IMongoDatabase GetDataBaseEntry(ReadPreference? readPreference = null)
    {
        // 如果已经有该dbName对应的MongoDatabase的记录，那么就直接返回
        if (m_gameDBEntryInit)
        {
            return m_gameDBEntry;
        }

        //先从配置中取得配置值
        string? replicaSetName = m_dbConfig.ReplicaSet;
        string host = m_dbConfig.DataBaseName; //"host1:host2:host3"
        string port = m_dbConfig.Port; //"port1:port2:port3"
        string? username = m_dbConfig.UserName;
        string? password = m_dbConfig.Password;
        string dbName = m_dbConfig.DataBaseName;
        // 保存配置
        m_isConnectMongos = m_dbConfig.IsMongos;

        //生成mongoDB的连接配置
        MongoClientSettings settings = new MongoClientSettings();
        List<MongoServerAddress> servers = new List<MongoServerAddress>();

        // 解析对应的host和端口
        string[] hostList = host.Split(':');
        string[] portList = port.Split(':');
        if (hostList.Length != portList.Length)
        {
            throw new ArgumentException("host and port count is not match.");
        }

        // 添加所有的ip和port
        for (int index = 0; index < hostList.Length; index++)
        {
            servers.Add(new MongoServerAddress(hostList[index], Convert.ToUInt16(portList[index])));
        }

        // 赋值server信息
        settings.Servers = servers;

        if (!string.IsNullOrEmpty(replicaSetName))
        {
            settings.ReplicaSetName = replicaSetName;
            settings.ReadPreference = ReadPreference.PrimaryPreferred;
        }
        //settings.ClusterConfigurator = builder =>
        //{
        //    builder.ConfigureCluster(a => a.With(serverSelectionTimeout: TimeSpan.FromSeconds(10)));
        //};

        // This property must be set to false if you specify more than one host name.
        settings.DirectConnection = false;
        settings.MaxConnectionPoolSize = 50;
        // settings.WaitQueueSize = 20000;
        if (readPreference != null)
        {
            settings.ReadPreference = readPreference;
        }

        if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
        {
            settings.Credential = MongoCredential.CreateCredential("admin", username, password);
        }

        settings.Freeze();


        //获得数据库entry
        m_gameDBEntry = new MongoClient(settings).GetDatabase(dbName);
        m_adminDBEntry = new MongoClient(settings).GetDatabase("admin");
        m_gameDBEntryInit = true;
        return m_gameDBEntry;
    }

    /// <summary>
    ///     get Collection
    /// </summary>
    /// <param name="collectionName"></param>
    /// <returns></returns>
    public IMongoCollection<T> GetCollection<T>(string collectionName) =>
        GetDataBaseEntry().GetCollection<T>(collectionName);

    /// <summary>
    ///     按条件获取一定数量的随机样本
    /// </summary>
    /// <typeparam name="TDocument"></typeparam>
    /// <param name="collectionName"></param>
    /// <param name="count"></param>
    /// <param name="filter"></param>
    /// <param name="projection">mongodb3.2不支持除了_id外的其他字段exclude</param>
    /// <returns></returns>
    public List<TDocument> CollectionConditionalSamples<TDocument>(string collectionName, int count,
        Expression<Func<TDocument, bool>>? filter = null, ProjectionDefinition<TDocument, TDocument>? projection = null)
    {
        // https://github.com/mongodb/mongo/blob/master/src/mongo/db/pipeline/pipeline_d.cpp#L96-L139
        const double kMaxSampleRatioForRandCursor = 0.05;

        IMongoCollection<TDocument>? coll = GetCollection<TDocument>(collectionName);

        int _CalculateAdjustedSampleCount()
        {
            int sc = Math.Min(count * 100, 1000);
            if (!m_estimatedCollectionRecords.TryGetValue(collectionName, out Tuple<int, DateTime>? records_info))
            {
                return sc;
            }

            int records = records_info.Item1;
            DateTime update_time = records_info.Item2;

            if (DateTime.Now - update_time > new TimeSpan(0, 10, 0))
            {
                m_estimatedCollectionRecords.TryUpdate(
                    collectionName,
                    new Tuple<int, DateTime>((int)coll.CountDocuments(Builders<TDocument>.Filter.Empty), DateTime.Now),
                    records_info
                );
            }

            bool is_random_cursor = sc <= records * kMaxSampleRatioForRandCursor;
            return is_random_cursor ? sc : Math.Max(1, (int)Math.Floor(records * kMaxSampleRatioForRandCursor));
        }

        try
        {
            int sample_count = _CalculateAdjustedSampleCount();

            //https://docs.mongodb.com/manual/reference/operator/aggregation/sample/index.html#pipe._S_sample
            //尽量让Sample成为第一个Stage，这样效率最高。当数据量非常大的时候，多取点再Match基本上也能获取到足够多的样本。

            //不允许使用磁盘，因为使用磁盘的场合，性能必然是比较差的
            IAggregateFluent<TDocument>? agg = coll.Aggregate(new AggregateOptions { AllowDiskUse = false });

            if (filter != null)
            {
                //注意：project为空的会很影响性能 保留需要的字段即可
                if (projection != null)
                {
                    agg = agg.Sample(sample_count).Project(projection).Match(filter).Limit(count);
                }
                else
                {
                    agg = agg.Sample(sample_count).Match(filter).Limit(count);
                }
            }
            else
            {
                agg = agg.Sample(count);
            }

            return agg.ToList();
        }
        catch (MongoCommandException commandException)
        {
            if (commandException.Code != 16820)
            {
                throw;
            }

            //16820
            //Sort exceeded memory limit of 104857600 bytes,
            //but did not opt in to external sorting. Aborting operation. Pass allowDiskUse:true to opt in.
            Tuple<int, DateTime> recordsInfo =
                new Tuple<int, DateTime>((int)coll.CountDocuments(Builders<TDocument>.Filter.Empty), DateTime.Now);
            m_estimatedCollectionRecords.AddOrUpdate(
                collectionName,
                recordsInfo,
                (k, v) => recordsInfo);

            int sampleCount = _CalculateAdjustedSampleCount();

            IAggregateFluent<TDocument>? agg = coll.Aggregate(new AggregateOptions { AllowDiskUse = false });
            agg = agg.Sample(sampleCount);
            if (projection != null)
            {
                agg = agg.Project(projection);
            }

            List<TDocument>? results = agg.ToList();

            if (filter == null)
            {
                return results;
            }

            List<TDocument> filtered_results = new List<TDocument>();
            Func<TDocument, bool> filter_func = filter.Compile();
            foreach (TDocument result in results.Where(filter_func))
            {
                if (filtered_results.Count >= count)
                {
                    break;
                }

                filtered_results.Add(result);
            }

            return filtered_results;
        }
    }


    #region New Api

    public async Task<UpdateResult> CollectionFindAndModifyAsync<TDocument>(string collectionName,
        FilterDefinition<TDocument> query, UpdateDefinition<TDocument> update, UpdateOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        return await collection.UpdateManyAsync(query, update, options, cancellationToken);
    }

    public async Task<TDocument> CollectionFindOneAsync<TDocument>(string collectionName,
        FilterDefinition<TDocument> query, FindOptions<TDocument>? options = null,
        CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        return await (await collection.FindAsync(query, options, cancellationToken)).FirstOrDefaultAsync(
            cancellationToken);
    }

    public async Task<TProjection> CollectionFindOneAsync<TDocument, TProjection>(string collectionName,
        FilterDefinition<TDocument> query, FindOptions<TDocument, TProjection>? options = null,
        CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        return await (await collection.FindAsync(query, options, cancellationToken)).FirstOrDefaultAsync(
            cancellationToken);
    }

    public async Task<IAsyncCursor<TDocument>> CollectionFindAsync<TDocument>(string collectionName,
        FilterDefinition<TDocument> query, FindOptions<TDocument>? options = null,
        CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        return await collection.FindAsync(query, options, cancellationToken);
    }

    public async Task<IAsyncCursor<TProjection>> CollectionFindAsync<TDocument, TProjection>(string collectionName,
        FilterDefinition<TDocument> query, FindOptions<TDocument, TProjection>? options = null,
        CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        return await collection.FindAsync(query, options, cancellationToken);
    }

    public async Task<TDocument> CollectionFindOneAndUpdateAsync<TDocument>(string collectionName,
        FilterDefinition<TDocument> query, UpdateDefinition<TDocument> update,
        FindOneAndUpdateOptions<TDocument>? options = null, CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        return await collection.FindOneAndUpdateAsync(query, update, options, cancellationToken);
    }

    public async Task<TProjection> CollectionFindOneAndUpdateAsync<TDocument, TProjection>(string collectionName,
        FilterDefinition<TDocument> query, UpdateDefinition<TDocument> update,
        FindOneAndUpdateOptions<TDocument, TProjection>? options = null, CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        return await collection.FindOneAndUpdateAsync(query, update, options, cancellationToken);
    }

    public async ValueTask<long> CollectionEstimatedDocumentCount<TDocument>(string collectionName,
        EstimatedDocumentCountOptions? options = null, CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        return await collection.EstimatedDocumentCountAsync(options, cancellationToken);
    }

    public async Task<DeleteResult> CollectionDeleteOneAsync<TDocument>(string collectionName,
        FilterDefinition<TDocument> filter, DeleteOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        return await collection.DeleteOneAsync(filter, options, cancellationToken);
    }

    public async Task<DeleteResult> CollectionDeleteManyAsync<TDocument>(string collectionName,
        FilterDefinition<TDocument> filter, DeleteOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        return await collection.DeleteManyAsync(filter, options, cancellationToken);
    }

    public async Task<TDocument> CollectionFindOneAndDeleteAsync<TDocument>(string collectionName,
        FilterDefinition<TDocument> filter, FindOneAndDeleteOptions<TDocument>? options = null,
        CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        return await collection.FindOneAndDeleteAsync(filter, options, cancellationToken);
    }

    public async Task<UpdateResult> CollectionUpdateOneAsync<TDocument>(string collectionName,
        FilterDefinition<TDocument> filter, UpdateDefinition<TDocument> update, UpdateOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        return await collection.UpdateOneAsync(filter, update, options, cancellationToken);
    }

    public async Task<ReplaceOneResult> CollectionReplaceOneAsync<TDocument>(string collectionName,
        FilterDefinition<TDocument> filter, TDocument replacement, ReplaceOptions? options = default,
        CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        return await collection.ReplaceOneAsync(filter, replacement, options, cancellationToken);
    }

    public async Task CollectionInsertOneAsync<TDocument>(string collectionName, TDocument document,
        InsertOneOptions? options = null, CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        await collection.InsertOneAsync(document, options, cancellationToken);
    }

    public async Task CollectionInsertManyAsync<TDocument>(string collectionName, IEnumerable<TDocument> documents,
        InsertManyOptions? options = null, CancellationToken cancellationToken = default)
    {
        IMongoCollection<TDocument> collection = GetCollection<TDocument>(collectionName);
        await collection.InsertManyAsync(documents, options, cancellationToken);
    }

    #endregion


    /// <summary>
    ///
    /// </summary>
    /// <param name="collectionName"></param>
    /// <param name="indexes"></param>
    /// <returns></returns>
    public IEnumerable<string> CreateCollectionsIndex(string collectionName, List<string> indexes)
    {
        var collectionExists = m_gameDBEntry.ListCollections(new ListCollectionsOptions { Filter = new BsonDocument("name", collectionName) }).Any();
        if (collectionExists)
        {
            return Enumerable.Empty<string>();
        }

        // create index when collection not exists
        var collection = m_gameDBEntry.GetCollection<BsonDocument>(collectionName);
        var indexModels = new List<CreateIndexModel<BsonDocument>>();
        foreach (var index in indexes)
        {
            var keys = Builders<BsonDocument>.IndexKeys.Ascending(index);
            var indexModel = new CreateIndexModel<BsonDocument>(keys);
            indexModels.Add(indexModel);
        }
        return collection.Indexes.CreateMany(indexModels);
    }


    #region ShardMongoDB

    /// <summary>
    ///     设置允许某一个数据库允许分片
    /// </summary>
    /// <param name="db"></param>
    /// <param name="dbName"></param>
    /// <returns></returns>
    private async Task<BsonDocument?> EnableShardForDataBase(string dbName)
    {
        try
        {
            return await m_adminDBEntry.RunCommandAsync<BsonDocument>(new BsonDocument("enableSharding", $"{dbName}"));
        }
        catch (Exception)
        {
            return default;
        }
    }

    /// <summary>
    ///     设置一个数据库中，一个
    /// </summary>
    /// <param name="collectionName"></param>
    /// <param name="shardKey"></param>
    /// <param name="keyHashed"></param>
    /// <returns></returns>
    public async Task<BsonDocument?> EnableShardForCollections(string collectionName, string shardKey,
        bool keyHashed = false)
    {
        // 如果不连接到mongos，则不用设置分片，直接返回
        if (!m_isConnectMongos)
        {
            return new BsonDocument();
        }

        // 先创建数据库和表
        try
        {
            m_gameDBEntry.CreateCollection(collectionName);
        }
        catch (Exception)
        {
            // 如果已经有数据库了，直接忽略吧
        }

        // 查询数据库分片状态
        BsonDocument? cmd = new BsonDocument("collStats", collectionName);
        BsonDocument? ret = await m_gameDBEntry.RunCommandAsync<BsonDocument>(cmd);

        if (ret.TryGetValue("sharded", out BsonValue? value) && value != null && value.AsBoolean)
        {
            // 已经shard过了
            return default;
        }

        // 先对数据库进行shard允许
        await EnableShardForDataBase(m_dbConfig.DataBaseName);

        if (keyHashed)
        {
            cmd = new BsonDocument("shardCollection", string.Format("{0}.{1}", m_dbConfig.DataBaseName, collectionName))
                .Add("key", new BsonDocument(shardKey, "hashed"));
        }
        else
        {
            cmd = new BsonDocument("shardCollection", string.Format("{0}.{1}", m_dbConfig.DataBaseName, collectionName))
                .Add("key", new BsonDocument(shardKey, 1));
        }

        try
        {
            ret = await m_adminDBEntry.RunCommandAsync<BsonDocument>(cmd);
        }
        catch (MongoCommandException ex)
        {
            // 可能出现创建冲突的情况，即使前面已经判断过了
            if (ex.Code == 20) // 重复的key
            {
                return new BsonDocument();
            }

            throw;
        }

        return ret;
    }

    #endregion
}
