using System.Linq.Expressions;
using MongoDB.Driver;

namespace Phoenix.Server.MongoDBWrapper;

/// <summary>
///     partial update fields of document
/// </summary>
/// <typeparam name="TDocument"></typeparam>
public class MongoPartialDBUpdateDocumentBuilder<TDocument> : IDBUpdateBuilder
{
    public MongoPartialDBUpdateDocumentBuilder(dynamic filterField) => FilterField = filterField;

    /// <summary>
    ///     documents的filter field
    /// </summary>
    public dynamic FilterField { get; set; }

    /// <summary>
    ///     key为filed名
    /// </summary>
    public UpdateDefinition<TDocument>? PartialBuilder { get; set; }

    /// <summary>
    ///     是否需要更新
    /// </summary>
    /// <returns></returns>
    public bool IsNeedUpdate() => PartialBuilder != null;

    /// <summary>
    ///     清理
    /// </summary>
    public void Clear() => PartialBuilder = null;

    /// <summary>
    ///     设置field
    /// </summary>
    /// <typeparam name="TField"></typeparam>
    /// <param name="field"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    public MongoPartialDBUpdateDocumentBuilder<TDocument> Set<TField>(Expression<Func<TDocument, TField>> field,
        TField value)
    {
        PartialBuilder = PartialBuilder == null
            ? Builders<TDocument>.Update.Set(field, value)
            : PartialBuilder.Set(field, value);

        return this;
    }
}
