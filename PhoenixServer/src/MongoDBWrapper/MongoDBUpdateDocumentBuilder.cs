using MongoDB.Bson;

namespace Phoenix.Server.MongoDBWrapper;

/// <summary>
///     需要update的document
/// </summary>
public class MongoDBUpdateDocumentBuilder : IDBUpdateBuilder
{
    public MongoDBUpdateDocumentBuilder(string filterField)
    {
        FilterField = filterField;
        builderDefinitions = new Dictionary<string, BsonDocument>();
    }

    /// <summary>
    ///     documents的filter field
    /// </summary>
    public string FilterField { get; private set; }

    /// <summary>
    ///     key为filed名
    /// </summary>
    public Dictionary<string, BsonDocument> builderDefinitions { get; set; }

    /// <summary>
    ///     是否需要更新
    /// </summary>
    /// <returns></returns>
    public bool IsNeedUpdate() => builderDefinitions.Count != 0;

    /// <summary>
    ///     清理
    /// </summary>
    public void Clear() => builderDefinitions.Clear();
}
