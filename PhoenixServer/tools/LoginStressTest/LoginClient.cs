using System.Diagnostics;
using System.Net;
using DotNetty.Codecs.Protobuf;
using DotNetty.Handlers.Logging;
using DotNetty.Transport.Bootstrapping;
using DotNetty.Transport.Channels;
using DotNetty.Transport.Channels.Sockets;
using Phoenix.Codecs;
using Phoenix.Framework.Utilities.Concurrent;
using Phoenix.MsgPackLogic.Protocol;
using Phoenix.Server.CommonDataStructure;
using Serilog;

namespace LoginStressTest;

public class LoginClient
{
    private readonly int m_clientId;
    private readonly string m_host;
    private readonly int m_port;
    private readonly string m_account;
    private readonly string m_clientVersion;
    private readonly string m_deviceId;
    private readonly string m_language;
    private readonly ClientStatistics m_statistics;
    private readonly bool m_verbose;

    private IChannel? m_channel;
    private IEventLoopGroup? m_group;
    private readonly Stopwatch m_loginStopwatch = new();
    private readonly Stopwatch m_authTokenStopwatch = new();
    private readonly Stopwatch m_sessionTokenStopwatch = new();

    private string? m_sessionToken;
    private readonly Random m_random = new();

    // 添加Echo相关的字段
    private Timer? m_echoTimer;
    private readonly Dictionary<uint, Stopwatch> m_pendingEchoRequests = new();
    private readonly List<double> m_echoLatencies = new();
    private readonly object m_echoLock = new();

    public LoginClient(
        int clientId,
        string host,
        int port,
        string account,
        string clientVersion,
        string deviceId,
        string language,
        ClientStatistics statistics,
        bool verbose)
    {
        m_clientId = clientId;
        m_host = host;
        m_port = port;
        m_account = account;
        m_clientVersion = clientVersion;
        m_deviceId = deviceId;
        m_language = language;
        m_statistics = statistics;
        m_verbose = verbose;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        try
        {
            // 重置统计
            m_statistics.Reset();

            // 创建事件循环组
            m_group = new MultithreadEventLoopGroup();

            // 创建引导程序
            var bootstrap = new Bootstrap();
            bootstrap
                .Group(m_group)
                .Channel<TcpSocketChannel>()
                .Option(ChannelOption.TcpNodelay, true)
                .Handler(new ActionChannelInitializer<ISocketChannel>(channel =>
                {
                    var pipeline = channel.Pipeline;

                    // 添加日志处理器（仅在详细模式下）
                    if (m_verbose)
                    {
                        pipeline.AddLast(new LoggingHandler($"Client-{m_clientId}"));
                    }

                    pipeline.AddLast("framing-size-dec", new ProtobufVarint32FrameDecoder());

                    pipeline.AddLast("framing-size-enc", new ProtobufVarint32LengthFieldPrepender());

                    // Add a handler for decoding the data packet with msgpack.
                    pipeline.AddLast("framing-msgpack-dec", new MessagePackDecoder<MsgPackStructBase>());

                    // Add a handler for encoding the data packet with msgpack.
                    pipeline.AddLast("framing-msgpack-enc", new MessagePackEncoder<MsgPackStructBase>());

                    /*// 添加消息帧解码器
                    pipeline.AddLast("framing-size-dec", new ProtobufVarint32FrameDecoder());
                    pipeline.AddLast("framing-size-enc", new ProtobufVarint32LengthFieldPrepender());*/

                    /*// 添加消息包解码器
                    pipeline.AddLast("framing-pb-dec", new MessagePackDecoder<MsgPackStructBase>());
                    pipeline.AddLast("framing-pb-enc", new MessagePackEncoder<MsgPackStructBase>());*/

                    // 添加客户端处理器
                    pipeline.AddLast("client-handler", new LoginClientHandler(this));
                }));

            // 开始计时
            m_loginStopwatch.Start();

            // 连接到服务器
            LogVerbose("正在连接到服务器 {Host}:{Port}", m_host, m_port);
            m_channel = await bootstrap.ConnectAsync(new IPEndPoint(IPAddress.Parse(m_host), m_port));

            // 更新统计
            m_statistics.IsConnected = true;
            LogVerbose("已连接到服务器");

            // 发送 AuthToken 登录请求
            await SendLoginByAuthTokenRequestAsync();

            // 等待取消信号
            await Task.Delay(Timeout.Infinite, cancellationToken);
        }
        catch (OperationCanceledException)
        {
            // 正常取消，不需要处理
            LogVerbose("客户端已取消");
        }
        catch (Exception ex)
        {
            // 记录错误
            Log.Error(ex, "客户端 {ClientId} 发生错误", m_clientId);
            m_statistics.ErrorCount++;
        }
        finally
        {
            // 关闭连接
            await CloseAsync();
        }
    }

    public async Task CloseAsync()
    {
        try
        {
            // 停止Echo定时器
            m_echoTimer?.Dispose();
            m_echoTimer = null;

            if (m_channel != null)
            {
                await m_channel.CloseAsync();
                m_channel = null;
            }

            if (m_group != null)
            {
                await m_group.ShutdownGracefullyAsync(TimeSpan.FromMilliseconds(100), TimeSpan.FromSeconds(1));
                m_group = null;
            }
        }
        catch (Exception ex)
        {
            Log.Error(ex, "关闭客户端 {ClientId} 时发生错误", m_clientId);
        }
    }

    public async Task SendLoginByAuthTokenRequestAsync()
    {
        if (m_channel == null || !m_channel.Active)
        {
            LogVerbose("无法发送 AuthToken 请求，通道未连接");
            return;
        }

        try
        {
            // 创建 AuthToken
            string authToken = $"{m_account}|fake|Phoenix|001|{m_deviceId}|xxxxxxxxx";

            // 创建登录请求
            var loginRequest = new LoginByAuthTokenReq
            {
                AuthToken = authToken,
                ClientVersion = m_clientVersion,
                ClientDeviceId = m_deviceId,
                Localization = m_language
            };

            // 开始计时
            m_authTokenStopwatch.Restart();

            // 发送请求
            LogVerbose("发送 AuthToken 登录请求");
            loginRequest.SetToken((uint)m_atomicToken.Increment());
            await m_channel.WriteAndFlushAsync(loginRequest);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "客户端 {ClientId} 发送 AuthToken 请求时发生错误", m_clientId);
            m_statistics.ErrorCount++;
        }
    }

    public async Task SendLoginBySessionTokenRequestAsync()
    {
        if (m_channel == null || !m_channel.Active || string.IsNullOrEmpty(m_sessionToken))
        {
            LogVerbose("无法发送 SessionToken 请求，通道未连接或 SessionToken 为空");
            return;
        }

        try
        {
            // 创建登录请求
            var loginRequest = new LoginBySessionTokenReq
            {
                SessionToken = m_sessionToken,
                ClientVersion = m_clientVersion,
                Localization = m_language,
                GameService = "default"
            };

            // 开始计时
            m_sessionTokenStopwatch.Restart();

            // 发送请求
            LogVerbose("发送 SessionToken 登录请求");
            loginRequest.SetToken((uint)m_atomicToken.Increment());
            await m_channel.WriteAndFlushAsync(loginRequest);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "客户端 {ClientId} 发送 SessionToken 请求时发生错误", m_clientId);
            m_statistics.ErrorCount++;
        }
    }

    public void HandleLoginByAuthTokenResponse(LoginByAuthTokenAck response)
    public void HandleLoginByAuthTokenResponse(MsgPack_LoginByAuthTokenAck response)
    {
        // 停止计时
        m_authTokenStopwatch.Stop();

        // 更新统计
        m_statistics.AuthTokenLatencyMs = m_authTokenStopwatch.Elapsed.TotalMilliseconds;

        if (response.ErrCode == (int)ErrCode.ErrCodeOk)
        {
            // 登录成功
            m_statistics.AuthTokenSuccess = true;
            m_sessionToken = response.SessionToken;

            LogVerbose("AuthToken 登录成功，延迟: {LatencyMs}ms", m_statistics.AuthTokenLatencyMs);

            // 发送 SessionToken 登录请求
            _ = SendLoginBySessionTokenRequestAsync();
        }
        else
        {
            // 登录失败
            Log.Warning("客户端 {ClientId} AuthToken 登录失败，错误码: {ErrorCode}", m_clientId, response.ErrCode);
            m_statistics.ErrorCount++;
        }
    }

    public void HandleLoginBySessionTokenResponse(LoginBySessionTokenAck response)
    {
        // 停止计时
        m_sessionTokenStopwatch.Stop();
        m_loginStopwatch.Stop();

        // 更新统计
        m_statistics.SessionTokenLatencyMs = m_sessionTokenStopwatch.Elapsed.TotalMilliseconds;
        m_statistics.TotalLoginLatencyMs = m_loginStopwatch.Elapsed.TotalMilliseconds;

        if (response.ErrCode == (int)ErrCode.ErrCodeOk)
        {
            // 登录成功
            m_statistics.SessionTokenSuccess = true;
            m_statistics.LoginComplete = true;

            LogVerbose("SessionToken 登录成功，延迟: {LatencyMs}ms，总登录时间: {TotalLatencyMs}ms",
                m_statistics.SessionTokenLatencyMs,
                m_statistics.TotalLoginLatencyMs);

            // 启动Echo定时器，每秒发送一次Echo请求
            m_echoTimer = new Timer(_ =>
            {
                _ = SendEchoRequestAsync();
            }, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
        }
        else
        {
            // 登录失败
            Log.Warning("客户端 {ClientId} SessionToken 登录失败，错误码: {ErrorCode}", m_clientId, response.ErrCode);
            m_statistics.ErrorCount++;
        }
    }

    private void LogVerbose(string message, params object[] args)
    {
        if (m_verbose)
        {
            Log.Debug($"客户端 {m_clientId}: {message}", args);
        }
    }

    private AtomicInt m_atomicToken = new AtomicInt(1);

    // 添加处理Echo响应的方法
    public void HandleEchoResponse(MsgPack_Echo_Ack response)
    {
        lock (m_echoLock)
        {
            if (m_pendingEchoRequests.TryGetValue(response.Token, out var stopwatch))
            {
                stopwatch.Stop();
                double latencyMs = stopwatch.Elapsed.TotalMilliseconds;
                m_echoLatencies.Add(latencyMs);
                m_pendingEchoRequests.Remove(response.Token);

                // 更新统计信息
                m_statistics.EchoCount++;
                m_statistics.EchoTotalLatencyMs += latencyMs;
                if (latencyMs < m_statistics.EchoMinLatencyMs || m_statistics.EchoMinLatencyMs == 0)
                    m_statistics.EchoMinLatencyMs = latencyMs;
                if (latencyMs > m_statistics.EchoMaxLatencyMs)
                    m_statistics.EchoMaxLatencyMs = latencyMs;

                LogVerbose("收到Echo响应，延迟: {LatencyMs}ms", latencyMs);
            }
        }
    }

    // 添加发送Echo请求的方法
    public async Task SendEchoRequestAsync()
    {
        if (m_channel == null || !m_channel.Active)
        {
            LogVerbose("无法发送Echo请求，通道未连接");
            return;
        }

        try
        {
            // 创建Echo请求
            var echoReq = new MsgPack_Echo_Req
            {
                Content = $"Echo from client {m_clientId} at {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}"
            };

            // 设置Token并记录发送时间
            uint token = (uint)m_atomicToken.Increment();
            echoReq.SetToken(token);

            lock (m_echoLock)
            {
                var stopwatch = new Stopwatch();
                stopwatch.Start();
                m_pendingEchoRequests[token] = stopwatch;
            }

            // 发送请求
            LogVerbose("发送Echo请求");
            await m_channel.WriteAndFlushAsync(echoReq);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "客户端 {ClientId} 发送Echo请求时发生错误", m_clientId);
            m_statistics.ErrorCount++;
        }
    }

    // 添加获取Echo统计信息的方法
    public List<double> GetEchoLatencies()
    {
        lock (m_echoLock)
        {
            return new List<double>(m_echoLatencies);
        }
    }
}
