using CommandLine;

namespace LoginStressTest;

public class CommandLineOptions
{
    [Option('h', "host", Required = true, HelpText = "服务器主机地址")]
    public string Host { get; set; } = "127.0.0.1";
    
    [Option('p', "port", Required = true, HelpText = "服务器端口")]
    public int Port { get; set; } = 8888;
    
    [Option('c', "clients", Required = false, Default = 100, HelpText = "并发客户端数量")]
    public int ClientCount { get; set; } = 100;
    
    [Option('r', "ramp-up", Required = false, Default = 10, HelpText = "客户端启动的时间间隔（秒）")]
    public int RampUpSeconds { get; set; } = 10;
    
    [Option('d', "duration", Required = false, Default = 60, HelpText = "压测持续时间（秒）")]
    public int DurationSeconds { get; set; } = 60;
    
    [Option('a', "account-prefix", Required = false, Default = "stresstest", HelpText = "账号前缀")]
    public string AccountPrefix { get; set; } = "stresstest";
    
    [Option('v', "version", Required = false, Default = "1.0.0", HelpText = "客户端版本")]
    public string ClientVersion { get; set; } = "1.0.0";
    
    [Option('l', "language", Required = false, Default = "zh-CN", HelpText = "客户端语言")]
    public string Language { get; set; } = "zh-CN";
    
    [Option("log-file", Required = false, HelpText = "日志文件路径")]
    public string? LogFile { get; set; }
    
    [Option("verbose", Required = false, Default = false, HelpText = "是否输出详细日志")]
    public bool Verbose { get; set; } = false;
}
