using DotNetty.Transport.Channels;
using Phoenix.MsgPackLogic.Protocol;
using Serilog;

namespace LoginStressTest;

public class LoginClientHandler : SimpleChannelInboundHandler<MsgPackStructBase>
{
    private readonly LoginClient m_client;

    public LoginClientHandler(LoginClient client)
    {
        m_client = client;
    }

    protected override void ChannelRead0(IChannelHandlerContext ctx, MsgPackStructBase msg)
    {
        // 根据消息类型处理不同的响应
        switch (msg.ProtoCode)
        {
            case EProtoCode.LOGIN_LOGINBYAUTHTOKENACK:
                m_client.HandleLoginByAuthTokenResponse((LoginByAuthTokenAck)msg);
                break;

            case EProtoCode.LOGIN_LOGINBYSESSIONTOKENACK:
                m_client.HandleLoginBySessionTokenResponse((LoginBySessionTokenAck)msg);
                break;

            case EProtoCode.LOGIN_SERVERDISCONNECTNTF:
                // 服务器断开连接通知
                var disconnectMsg = (ServerDisconnectNtf)msg;
                Log.Warning("服务器断开连接，原因码: {ReasonCode}", disconnectMsg.ReasonCode);
                break;

            case EProtoCode.TEST_ECHO_ACK:
                // 处理Echo响应
                m_client.HandleEchoResponse((MsgPack_Echo_Ack)msg);
                break;

            default:
                // 其他消息，忽略
                break;
        }
    }

    public override void ExceptionCaught(IChannelHandlerContext context, Exception exception)
    {
        Log.Error(exception, "客户端处理消息时发生异常");
        context.CloseAsync();
    }

    public override void ChannelInactive(IChannelHandlerContext context)
    {
        Log.Debug("连接已断开");
        base.ChannelInactive(context);
    }
}
