<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CommandLineParser"/>
    <PackageReference Include="DotNetty.Buffers"/>
    <PackageReference Include="DotNetty.Codecs"/>
    <!--<PackageReference Include="DotNetty.Codecs.Protobuf"/>-->
    <PackageReference Include="DotNetty.Common"/>
    <PackageReference Include="DotNetty.Handlers"/>
    <PackageReference Include="DotNetty.Transport"/>
    <PackageReference Include="MessagePack"/>
    <PackageReference Include="Serilog"/>
    <PackageReference Include="Serilog.Extensions.Logging"/>
    <PackageReference Include="Serilog.Sinks.Console"/>
    <PackageReference Include="Serilog.Sinks.File"/>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Phoenix.Codecs\Phoenix.Codecs.csproj" />
    <ProjectReference Include="..\..\src\Phoenix.Server.CommonDataStructure\Phoenix.Server.CommonDataStructure.csproj" />
    <ProjectReference Include="..\..\src\Phoenix.Server.Protocol\Phoenix.Server.Protocol.csproj" />
  </ItemGroup>

  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="@echo off&#xA;&#xA;    del /F /Q &quot;..\..\Bin\LoginStressTest\*&quot;&#xA;&#xA;    xcopy /E /Y &quot;$(TargetDir)&quot; &quot;..\..\Bin\LoginStressTest\&quot;" />
  </Target>

</Project>
