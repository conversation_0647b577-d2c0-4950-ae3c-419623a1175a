using System.Collections.Concurrent;
using System.Diagnostics;
using Serilog;

namespace LoginStressTest;

public class StressTestManager
{
    private readonly CommandLineOptions m_options;
    private readonly ConcurrentDictionary<int, LoginClient> m_clients = new();
    private readonly ConcurrentDictionary<int, ClientStatistics> m_statistics = new();
    private readonly CancellationTokenSource m_cts = new();
    private readonly Stopwatch m_stopwatch = new();

    public StressTestManager(CommandLineOptions options)
    {
        m_options = options;
    }

    public async Task RunAsync()
    {
        Log.Information("开始登录压力测试");
        Log.Information("服务器地址: {Host}:{Port}", m_options.Host, m_options.Port);
        Log.Information("客户端数量: {ClientCount}", m_options.ClientCount);
        Log.Information("启动时间: {RampUpSeconds}秒", m_options.RampUpSeconds);
        Log.Information("测试时长: {DurationSeconds}秒", m_options.DurationSeconds);

        // 启动统计任务
        var statsTask = Task.Run(StatisticsReportingTask);

        // 启动压测
        m_stopwatch.Start();
        var clientTasks = new List<Task>();

        // 计算客户端启动间隔
        double intervalMs = m_options.RampUpSeconds * 1000.0 / m_options.ClientCount;

        // 创建并启动客户端
        for (int i = 0; i < m_options.ClientCount; i++)
        {
            var clientId = i;
            var account = $"{m_options.AccountPrefix}{i}";
            var deviceId = $"device_{i}";

            // 创建客户端统计对象
            m_statistics[clientId] = new ClientStatistics();

            // 创建客户端
            var client = new LoginClient(
                clientId,
                m_options.Host,
                m_options.Port,
                account,
                m_options.ClientVersion,
                deviceId,
                m_options.Language,
                m_statistics[clientId],
                m_options.Verbose
            );

            m_clients[clientId] = client;

            // 启动客户端（考虑启动间隔）
            var delay = (int)(i * intervalMs);
            clientTasks.Add(Task.Run(async () =>
            {
                await Task.Delay(delay).ConfigureAwait(false);
                await client.StartAsync(m_cts.Token);
            }));
        }

        // 等待指定的测试时长
        await Task.Delay(TimeSpan.FromSeconds(m_options.DurationSeconds));

        // 停止测试
        m_cts.Cancel();

        // 等待所有客户端任务完成
        await Task.WhenAll(clientTasks);

        // 等待统计任务完成
        await statsTask;

        // 输出最终统计结果
        ReportFinalStatistics();
    }

    private async Task StatisticsReportingTask()
    {
        try
        {
            while (!m_cts.Token.IsCancellationRequested)
            {
                // 每秒报告一次统计信息
                await Task.Delay(1000, m_cts.Token);
                ReportCurrentStatistics();
            }
        }
        catch (OperationCanceledException)
        {
            // 正常取消，不需要处理
        }
    }

    private void ReportCurrentStatistics()
    {
        var totalConnections = m_statistics.Values.Count(s => s.IsConnected);
        var totalAuthTokenSuccess = m_statistics.Values.Count(s => s.AuthTokenSuccess);
        var totalSessionTokenSuccess = m_statistics.Values.Count(s => s.SessionTokenSuccess);
        var totalLoginComplete = m_statistics.Values.Count(s => s.LoginComplete);
        var totalErrors = m_statistics.Values.Sum(s => s.ErrorCount);

        Log.Information(
            "统计 [{ElapsedSeconds}秒] - 连接: {ConnectedCount}/{TotalCount}, " +
            "AuthToken成功: {AuthTokenSuccess}, SessionToken成功: {SessionTokenSuccess}, " +
            "登录完成: {LoginComplete}, 错误: {ErrorCount}",
            m_stopwatch.Elapsed.TotalSeconds.ToString("F1"),
            totalConnections,
            m_options.ClientCount,
            totalAuthTokenSuccess,
            totalSessionTokenSuccess,
            totalLoginComplete,
            totalErrors
        );
    }

    private void ReportFinalStatistics()
    {
        var elapsedSeconds = m_stopwatch.Elapsed.TotalSeconds;

        var totalConnections = m_statistics.Values.Count(s => s.IsConnected);
        var totalAuthTokenSuccess = m_statistics.Values.Count(s => s.AuthTokenSuccess);
        var totalSessionTokenSuccess = m_statistics.Values.Count(s => s.SessionTokenSuccess);
        var totalLoginComplete = m_statistics.Values.Count(s => s.LoginComplete);
        var totalErrors = m_statistics.Values.Sum(s => s.ErrorCount);

        var authTokenLatencies = m_statistics.Values
            .Where(s => s.AuthTokenLatencyMs > 0)
            .Select(s => s.AuthTokenLatencyMs)
            .ToList();

        var sessionTokenLatencies = m_statistics.Values
            .Where(s => s.SessionTokenLatencyMs > 0)
            .Select(s => s.SessionTokenLatencyMs)
            .ToList();

        var totalLatencies = m_statistics.Values
            .Where(s => s.TotalLoginLatencyMs > 0)
            .Select(s => s.TotalLoginLatencyMs)
            .ToList();

        Log.Information("========== 最终测试结果 ==========");
        Log.Information("测试时长: {ElapsedSeconds}秒", elapsedSeconds.ToString("F1"));
        Log.Information("客户端总数: {ClientCount}", m_options.ClientCount);
        Log.Information("连接成功: {ConnectedCount} ({ConnectedPercent}%)",
            totalConnections,
            totalConnections * 100.0 / m_options.ClientCount);
        Log.Information("AuthToken成功: {AuthTokenSuccess} ({AuthTokenPercent}%)",
            totalAuthTokenSuccess,
            totalAuthTokenSuccess * 100.0 / m_options.ClientCount);
        Log.Information("SessionToken成功: {SessionTokenSuccess} ({SessionTokenPercent}%)",
            totalSessionTokenSuccess,
            totalSessionTokenSuccess * 100.0 / m_options.ClientCount);
        Log.Information("登录完成: {LoginComplete} ({LoginCompletePercent}%)",
            totalLoginComplete,
            totalLoginComplete * 100.0 / m_options.ClientCount);
        Log.Information("错误总数: {ErrorCount}", totalErrors);

        if (authTokenLatencies.Any())
        {
            Log.Information("AuthToken延迟 - 平均: {AvgMs}ms, 最小: {MinMs}ms, 最大: {MaxMs}ms, 中位数: {MedianMs}ms",
                authTokenLatencies.Average().ToString("F2"),
                authTokenLatencies.Min(),
                authTokenLatencies.Max(),
                GetMedian(authTokenLatencies).ToString("F2"));
        }

        if (sessionTokenLatencies.Any())
        {
            Log.Information("SessionToken延迟 - 平均: {AvgMs}ms, 最小: {MinMs}ms, 最大: {MaxMs}ms, 中位数: {MedianMs}ms",
                sessionTokenLatencies.Average().ToString("F2"),
                sessionTokenLatencies.Min(),
                sessionTokenLatencies.Max(),
                GetMedian(sessionTokenLatencies).ToString("F2"));
        }

        if (totalLatencies.Any())
        {
            Log.Information("总登录延迟 - 平均: {AvgMs}ms, 最小: {MinMs}ms, 最大: {MaxMs}ms, 中位数: {MedianMs}ms",
                totalLatencies.Average().ToString("F2"),
                totalLatencies.Min(),
                totalLatencies.Max(),
                GetMedian(totalLatencies).ToString("F2"));

            // 计算每秒登录数 (TPS)
            var loginTps = totalLoginComplete / elapsedSeconds;
            Log.Information("每秒登录数 (TPS): {LoginTps}", loginTps.ToString("F2"));
        }

        // 收集所有Echo延迟数据
        var allEchoLatencies = new List<double>();
        foreach (var client in m_clients.Values)
        {
            allEchoLatencies.AddRange(client.GetEchoLatencies());
        }

        // 输出Echo统计信息
        if (allEchoLatencies.Count > 0)
        {
            allEchoLatencies.Sort();
            double totalEchos = allEchoLatencies.Count;

            Log.Information("Echo统计信息:");
            Log.Information("总Echo请求数: {Count}", totalEchos);
            Log.Information("平均延迟: {AvgMs}ms", allEchoLatencies.Average().ToString("F2"));
            Log.Information("最小延迟: {MinMs}ms", allEchoLatencies.First().ToString("F2"));
            Log.Information("最大延迟: {MaxMs}ms", allEchoLatencies.Last().ToString("F2"));

            // 计算百分位数
            Log.Information("50%延迟(中位数): {MedianMs}ms",
                allEchoLatencies[(int)(totalEchos * 0.5)].ToString("F2"));
            Log.Information("90%延迟: {P90Ms}ms",
                allEchoLatencies[(int)(totalEchos * 0.9)].ToString("F2"));
            Log.Information("95%延迟: {P95Ms}ms",
                allEchoLatencies[(int)(totalEchos * 0.95)].ToString("F2"));
            Log.Information("99%延迟: {P99Ms}ms",
                allEchoLatencies[(int)(totalEchos * 0.99)].ToString("F2"));

            // 计算每秒Echo数 (TPS)
            double echoTps = totalEchos / elapsedSeconds;
            Log.Information("每秒Echo数 (TPS): {EchoTps}", echoTps.ToString("F2"));
        }
        else
        {
            Log.Information("没有收集到Echo统计数据");
        }

        Log.Information("================================");
    }

    private double GetMedian(List<double> values)
    {
        if (values == null || !values.Any())
            return 0;

        var sortedValues = values.OrderBy(v => v).ToList();
        int count = sortedValues.Count;

        if (count % 2 == 0)
        {
            // 偶数个元素，取中间两个的平均值
            return (sortedValues[count / 2 - 1] + sortedValues[count / 2]) / 2;
        }
        else
        {
            // 奇数个元素，取中间值
            return sortedValues[count / 2];
        }
    }
}
