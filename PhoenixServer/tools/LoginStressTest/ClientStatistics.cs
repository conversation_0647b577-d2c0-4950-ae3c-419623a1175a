namespace LoginStressTest;

public class ClientStatistics
{
    // 连接状态
    public bool IsConnected { get; set; }

    // 登录状态
    public bool AuthTokenSuccess { get; set; }
    public bool SessionTokenSuccess { get; set; }
    public bool LoginComplete { get; set; }

    // 延迟统计（毫秒）
    public double AuthTokenLatencyMs { get; set; }
    public double SessionTokenLatencyMs { get; set; }
    public double TotalLoginLatencyMs { get; set; }

    // 错误计数
    public int ErrorCount { get; set; }

    // Echo相关统计
    public int EchoCount { get; set; }
    public double EchoTotalLatencyMs { get; set; }
    public double EchoMinLatencyMs { get; set; }
    public double EchoMaxLatencyMs { get; set; }

    public double EchoAvgLatencyMs => EchoCount > 0 ? EchoTotalLatencyMs / EchoCount : 0;

    // 重置统计
    public void Reset()
    {
        IsConnected = false;
        AuthTokenSuccess = false;
        SessionTokenSuccess = false;
        LoginComplete = false;
        AuthTokenLatencyMs = 0;
        SessionTokenLatencyMs = 0;
        TotalLoginLatencyMs = 0;
        ErrorCount = 0;

        // 重置Echo统计
        EchoCount = 0;
        EchoTotalLatencyMs = 0;
        EchoMinLatencyMs = 0;
        EchoMaxLatencyMs = 0;
    }
}
