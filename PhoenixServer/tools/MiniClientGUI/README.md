## MiniClientGUI

### 用途

> 模拟client 发送消息给到服务器，用于测试服务器的功能,并将服务器返回信息显示在界面上.

### 使用方法

1. 有新增、修改 protobuf 文件的需求时，请重新编译`MiniClientGUI`工程，
2. 运行`MiniClientGUI`工程
3. 在`服务器`输入框中输入服务器(GateServer)的信息，格式 IP:PORT.
4. 在`账号名`输入框中账号名.
5. 其它选项根据需要进行选择.一般情况下不需要修改,使用默认值即可.
6. 点击 `登录` 按钮，连接服务器,自动进入登录、账号初始化流程(自动创建角色、初始化Player).
7. 若是需要测试创建角色流程，不勾选 `自动创建角色（该账号无角色情况下)`，然后点击 `登录` 按钮，连接服务器,只会进行登录流程用户验证.
8. 在选取框内选择发送的消息名，并填写消息内容，然后点击 `发送` 按钮，将消息发送到服务器.
9. 当服务器返回消息时，运行监控面板上进行显示.

### 原理

#### 网络通讯

1. 使用 [DotNetty](https://github.com/Azure/DotNetty) 网络库进行网络通讯
2. 发送给服务器的消息包的定义结构如下:
    ```
       ///    BEFORE ENCODE                            AFTER ENCODE (306 bytes)
       /// +-------------------+      +-----------------+------------+---------------+
       /// | Protobuf IMessage |----->| Length          | Message Id | Protobuf Data |
       /// |                   |      | 0xAC02(4 bytes) | (2 bytes)  |  (300 bytes)  |
       /// +-------------------+      +-----------------+------------+---------------+
       
        业务层将 protobuf 定义的好的各类 Message 写入到channel 中，通过 pipeline 编码成符合协议要求的网络数据包.
    ```
3. 客服端接受到的消息包的定义结构如下:
    ```
       ///      BEFORE DECODE (306 bytes)                             AFTER DECODE
       /// +-----------------+------------+---------------+      +-------------------+
       /// | Length          | Message Id | Protobuf Data |----->| Protobuf IMessage |
       /// | 0xAC02(4 bytes) | (2 bytes)  |  (300 bytes)  |      |                   |
       /// +-----------------+------------+---------------+      +-------------------+
   
        网络层收到数据后，通过 pipeline 解码成 protobuf 定义的各类 Message 给到业务层.
    ```

#### Protobuf <==> Json

1. 使用 Json 作为数据的显示格式,
   通过输入的消息名、[Google.Protobuf.JsonFormatter](https://protobuf.dev/reference/csharp/api-docs/class/google/protobuf/json-formatter)
   将 Protobuf 格式的数据转换成 Json 格式的数据，然后显示在界面上.(问题:嵌套的消息类型无法格式，因为其值为null，需要修复)
2. 使用 Protobuf 作为数据的传输格式,
   通过输入的消息名、[Google.Protobuf.JsonParse](https://protobuf.dev/reference/csharp/api-docs/class/google/protobuf/json-parser)
   将 Json 格式的数据转换成 Protobuf 格式的数据，然后发送给服务器.

#### Code Generator

网络数据包中使用整形 id
做为消息的唯一标识，而我们在业务层使用 [IMessage](https://github.com/protocolbuffers/protobuf/blob/main/csharp/src/Google.Protobuf/IMessage.cs)
,保存这一映射在解码时根据id找对正确的消息，编码时候通过消息找到正确的Id。在此我们使用[CodeGenerator](https://learn.microsoft.com/en-us/dotnet/csharp/roslyn-sdk/source-generators-overview)
通过解析`PreservedGrpcDescriptionList.txt`文件中的内容结合代码模板来生成预期的代码给到业务层使用.
代码生成工程为:`Client.SourceGenerated.Mini`

### RoadMap

- [] 支持cache
- [] 支持replay

### 问题排查

1. 开启 ENABLE_TRACE_DOTNETTY 宏，会将相关的网络日志输出到面板中 DotNetty 开启下面的两段代码注释，开启日志输出，排查网络问题.
2. 调试代码生成器，查看生成的代码是否正确,配置如下.
   https://github.com/JoanComasFdz/dotnet-how-to-debug-source-generator-vs2022

### 引用 //TODO

* [DotNetty](https://github.com/Azure/DotNetty)
* [CodeGenerator](https://learn.microsoft.com/en-us/dotnet/csharp/roslyn-sdk/source-generators-overview)
* [Grpc.Tools](https://github.com/grpc/grpc/blob/master/src/csharp/BUILD-INTEGRATION.md)
* [DotNetty.Codecs.Protobuf](https://github.com/Azure/DotNetty/tree/dev/src/DotNetty.Codecs.Protobuf)
* [Serilog](https://github.com/serilog/serilog)
* [Windows Forms](https://learn.microsoft.com/en-us/dotnet/desktop/winforms/overview/?view=netdesktop-7.0)
* [Reference local projects in Source Generator](https://github.com/dotnet/roslyn/discussions/47517)
