namespace MiniClientGUI;

partial class Form1
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        AccountTextBox = new TextBox();
        VersionTextBox = new TextBox();
        DeviceIdTextBox = new TextBox();
        LangueTextBox = new TextBox();
        AccountLabel = new Label();
        VersionLabel = new Label();
        DevicedIdLabel = new Label();
        LangueLabel = new Label();
        LoginButton = new Button();
        MessageNameComboBox = new ComboBox();
        SearchMessageTextBox = new TextBox();
        SearchMessageLabel = new Label();
        MessageNameLabel = new Label();
        SendMessageButton = new Button();
        MessageDefineTextBox = new TextBox();
        MessageDefineLabel = new Label();
        AutoCreateCharacterCheckBox = new CheckBox();
        label1 = new Label();
        SeverHostTextBox = new TextBox();
        MonitorTextBox = new TextBox();
        groupBox1 = new GroupBox();
        groupBox2 = new GroupBox();
        groupBox3 = new GroupBox();
        groupBox1.SuspendLayout();
        groupBox2.SuspendLayout();
        groupBox3.SuspendLayout();
        SuspendLayout();
        // 
        // AccountTextBox
        // 
        AccountTextBox.Location = new Point(62, 64);
        AccountTextBox.Margin = new Padding(2);
        AccountTextBox.Name = "AccountTextBox";
        AccountTextBox.Size = new Size(144, 23);
        AccountTextBox.TabIndex = 0;
        AccountTextBox.Text = "g42";
        // 
        // VersionTextBox
        // 
        VersionTextBox.Location = new Point(62, 119);
        VersionTextBox.Margin = new Padding(2);
        VersionTextBox.Name = "VersionTextBox";
        VersionTextBox.Size = new Size(144, 23);
        VersionTextBox.TabIndex = 1;
        VersionTextBox.Text = "1.0.1";
        // 
        // DeviceIdTextBox
        // 
        DeviceIdTextBox.Location = new Point(62, 186);
        DeviceIdTextBox.Margin = new Padding(2);
        DeviceIdTextBox.Name = "DeviceIdTextBox";
        DeviceIdTextBox.Size = new Size(144, 23);
        DeviceIdTextBox.TabIndex = 2;
        DeviceIdTextBox.Text = "xxxxxxxxx";
        // 
        // LangueTextBox
        // 
        LangueTextBox.Location = new Point(62, 251);
        LangueTextBox.Margin = new Padding(2);
        LangueTextBox.Name = "LangueTextBox";
        LangueTextBox.Size = new Size(144, 23);
        LangueTextBox.TabIndex = 3;
        LangueTextBox.Text = "CN";
        // 
        // AccountLabel
        // 
        AccountLabel.AutoSize = true;
        AccountLabel.Location = new Point(7, 64);
        AccountLabel.Margin = new Padding(2, 0, 2, 0);
        AccountLabel.Name = "AccountLabel";
        AccountLabel.Size = new Size(47, 17);
        AccountLabel.TabIndex = 4;
        AccountLabel.Text = "账号名:";
        // 
        // VersionLabel
        // 
        VersionLabel.AutoSize = true;
        VersionLabel.Location = new Point(3, 119);
        VersionLabel.Margin = new Padding(2, 0, 2, 0);
        VersionLabel.Name = "VersionLabel";
        VersionLabel.Size = new Size(47, 17);
        VersionLabel.TabIndex = 5;
        VersionLabel.Text = "版本号:";
        // 
        // DevicedIdLabel
        // 
        DevicedIdLabel.AutoSize = true;
        DevicedIdLabel.Location = new Point(4, 191);
        DevicedIdLabel.Margin = new Padding(2, 0, 2, 0);
        DevicedIdLabel.Name = "DevicedIdLabel";
        DevicedIdLabel.Size = new Size(47, 17);
        DevicedIdLabel.TabIndex = 6;
        DevicedIdLabel.Text = "设备Id:";
        // 
        // LangueLabel
        // 
        LangueLabel.AutoSize = true;
        LangueLabel.Location = new Point(7, 255);
        LangueLabel.Margin = new Padding(2, 0, 2, 0);
        LangueLabel.Name = "LangueLabel";
        LangueLabel.Size = new Size(35, 17);
        LangueLabel.TabIndex = 7;
        LangueLabel.Text = "语言:";
        // 
        // LoginButton
        // 
        LoginButton.Location = new Point(234, 175);
        LoginButton.Margin = new Padding(2);
        LoginButton.Name = "LoginButton";
        LoginButton.Size = new Size(188, 97);
        LoginButton.TabIndex = 8;
        LoginButton.Text = "登录";
        LoginButton.UseVisualStyleBackColor = true;
        LoginButton.Click += LoginButton_Click;
        // 
        // MessageNameComboBox
        // 
        MessageNameComboBox.FormattingEnabled = true;
        MessageNameComboBox.Location = new Point(74, 30);
        MessageNameComboBox.Margin = new Padding(2);
        MessageNameComboBox.Name = "MessageNameComboBox";
        MessageNameComboBox.Size = new Size(192, 25);
        MessageNameComboBox.TabIndex = 9;
        MessageNameComboBox.SelectedIndexChanged += MessageNameComboBox_SelectedIndexChanged;
        // 
        // SearchMessageTextBox
        // 
        SearchMessageTextBox.Location = new Point(74, 73);
        SearchMessageTextBox.Margin = new Padding(2);
        SearchMessageTextBox.Name = "SearchMessageTextBox";
        SearchMessageTextBox.Size = new Size(190, 23);
        SearchMessageTextBox.TabIndex = 10;
        SearchMessageTextBox.TextChanged += SearchMessageTextBox_TextChanged;
        // 
        // SearchMessageLabel
        // 
        SearchMessageLabel.AutoSize = true;
        SearchMessageLabel.Location = new Point(6, 70);
        SearchMessageLabel.Margin = new Padding(2, 0, 2, 0);
        SearchMessageLabel.Name = "SearchMessageLabel";
        SearchMessageLabel.Size = new Size(71, 17);
        SearchMessageLabel.TabIndex = 11;
        SearchMessageLabel.Text = "搜索消息名:";
        // 
        // MessageNameLabel
        // 
        MessageNameLabel.AutoSize = true;
        MessageNameLabel.Location = new Point(4, 30);
        MessageNameLabel.Margin = new Padding(2, 0, 2, 0);
        MessageNameLabel.Name = "MessageNameLabel";
        MessageNameLabel.Size = new Size(71, 17);
        MessageNameLabel.TabIndex = 12;
        MessageNameLabel.Text = "选择消息名:";
        // 
        // SendMessageButton
        // 
        SendMessageButton.Location = new Point(300, 21);
        SendMessageButton.Margin = new Padding(2);
        SendMessageButton.Name = "SendMessageButton";
        SendMessageButton.Size = new Size(274, 94);
        SendMessageButton.TabIndex = 13;
        SendMessageButton.Text = "发送消息";
        SendMessageButton.UseVisualStyleBackColor = true;
        SendMessageButton.Click += SendMessageButton_Click;
        // 
        // MessageDefineTextBox
        // 
        MessageDefineTextBox.Location = new Point(6, 123);
        MessageDefineTextBox.Margin = new Padding(2);
        MessageDefineTextBox.Multiline = true;
        MessageDefineTextBox.Name = "MessageDefineTextBox";
        MessageDefineTextBox.Size = new Size(676, 209);
        MessageDefineTextBox.TabIndex = 14;
        // 
        // MessageDefineLabel
        // 
        MessageDefineLabel.AutoSize = true;
        MessageDefineLabel.Location = new Point(6, 97);
        MessageDefineLabel.Margin = new Padding(2, 0, 2, 0);
        MessageDefineLabel.Name = "MessageDefineLabel";
        MessageDefineLabel.Size = new Size(59, 17);
        MessageDefineLabel.TabIndex = 15;
        MessageDefineLabel.Text = "消息填充:";
        // 
        // AutoCreateCharacterCheckBox
        // 
        AutoCreateCharacterCheckBox.AutoSize = true;
        AutoCreateCharacterCheckBox.Checked = true;
        AutoCreateCharacterCheckBox.CheckState = CheckState.Checked;
        AutoCreateCharacterCheckBox.Location = new Point(234, 120);
        AutoCreateCharacterCheckBox.Margin = new Padding(2);
        AutoCreateCharacterCheckBox.Name = "AutoCreateCharacterCheckBox";
        AutoCreateCharacterCheckBox.Size = new Size(215, 21);
        AutoCreateCharacterCheckBox.TabIndex = 16;
        AutoCreateCharacterCheckBox.Text = "自动创建角色(该账号无角色情况下)";
        AutoCreateCharacterCheckBox.UseVisualStyleBackColor = true;
        // 
        // label1
        // 
        label1.AutoSize = true;
        label1.Location = new Point(4, 26);
        label1.Margin = new Padding(2, 0, 2, 0);
        label1.Name = "label1";
        label1.Size = new Size(47, 17);
        label1.TabIndex = 17;
        label1.Text = "服务器:";
        // 
        // SeverHostTextBox
        // 
        SeverHostTextBox.Location = new Point(62, 24);
        SeverHostTextBox.Margin = new Padding(2);
        SeverHostTextBox.Name = "SeverHostTextBox";
        SeverHostTextBox.Size = new Size(144, 23);
        SeverHostTextBox.TabIndex = 18;
        SeverHostTextBox.Text = "127.0.0.1:6666";
        // 
        // MonitorTextBox
        // 
        MonitorTextBox.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
        MonitorTextBox.BackColor = Color.LightCyan;
        MonitorTextBox.Location = new Point(7, 20);
        MonitorTextBox.Margin = new Padding(2);
        MonitorTextBox.Multiline = true;
        MonitorTextBox.Name = "MonitorTextBox";
        MonitorTextBox.ReadOnly = true;
        MonitorTextBox.ScrollBars = ScrollBars.Vertical;
        MonitorTextBox.Size = new Size(1169, 364);
        MonitorTextBox.TabIndex = 19;
        MonitorTextBox.TextChanged += MonitorTextBox_TextChanged;
        // 
        // groupBox1
        // 
        groupBox1.Controls.Add(label1);
        groupBox1.Controls.Add(AccountTextBox);
        groupBox1.Controls.Add(VersionTextBox);
        groupBox1.Controls.Add(SeverHostTextBox);
        groupBox1.Controls.Add(DeviceIdTextBox);
        groupBox1.Controls.Add(LangueTextBox);
        groupBox1.Controls.Add(AutoCreateCharacterCheckBox);
        groupBox1.Controls.Add(AccountLabel);
        groupBox1.Controls.Add(VersionLabel);
        groupBox1.Controls.Add(DevicedIdLabel);
        groupBox1.Controls.Add(LangueLabel);
        groupBox1.Controls.Add(LoginButton);
        groupBox1.Location = new Point(8, 8);
        groupBox1.Margin = new Padding(2);
        groupBox1.Name = "groupBox1";
        groupBox1.Padding = new Padding(2);
        groupBox1.Size = new Size(475, 344);
        groupBox1.TabIndex = 21;
        groupBox1.TabStop = false;
        groupBox1.Text = "基本信息";
        // 
        // groupBox2
        // 
        groupBox2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
        groupBox2.Controls.Add(MessageDefineTextBox);
        groupBox2.Controls.Add(MessageNameComboBox);
        groupBox2.Controls.Add(SendMessageButton);
        groupBox2.Controls.Add(MessageNameLabel);
        groupBox2.Controls.Add(SearchMessageTextBox);
        groupBox2.Controls.Add(MessageDefineLabel);
        groupBox2.Controls.Add(SearchMessageLabel);
        groupBox2.Location = new Point(486, 8);
        groupBox2.Margin = new Padding(2);
        groupBox2.Name = "groupBox2";
        groupBox2.Padding = new Padding(2);
        groupBox2.Size = new Size(702, 344);
        groupBox2.TabIndex = 22;
        groupBox2.TabStop = false;
        groupBox2.Text = "消息信息";
        // 
        // groupBox3
        // 
        groupBox3.AutoSize = true;
        groupBox3.Controls.Add(MonitorTextBox);
        groupBox3.Location = new Point(8, 357);
        groupBox3.Margin = new Padding(2);
        groupBox3.Name = "groupBox3";
        groupBox3.Padding = new Padding(2);
        groupBox3.Size = new Size(1180, 586);
        groupBox3.TabIndex = 23;
        groupBox3.TabStop = false;
        groupBox3.Text = "运行监控";
        // 
        // Form1
        // 
        AutoScaleDimensions = new SizeF(7F, 17F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(1191, 752);
        Controls.Add(groupBox3);
        Controls.Add(groupBox2);
        Controls.Add(groupBox1);
        FormBorderStyle = FormBorderStyle.FixedSingle;
        Margin = new Padding(2);
        Name = "Form1";
        Text = "Form1";
        Load += Form1_Load;
        groupBox1.ResumeLayout(false);
        groupBox1.PerformLayout();
        groupBox2.ResumeLayout(false);
        groupBox2.PerformLayout();
        groupBox3.ResumeLayout(false);
        groupBox3.PerformLayout();
        ResumeLayout(false);
        PerformLayout();
    }

    #endregion

    private TextBox AccountTextBox;
    private TextBox VersionTextBox;
    private TextBox DeviceIdTextBox;
    private TextBox LangueTextBox;
    private Label AccountLabel;
    private Label VersionLabel;
    private Label DevicedIdLabel;
    private Label LangueLabel;
    private Button LoginButton;
    private ComboBox MessageNameComboBox;
    private TextBox SearchMessageTextBox;
    private Label SearchMessageLabel;
    private Label MessageNameLabel;
    private Button SendMessageButton;
    private TextBox MessageDefineTextBox;
    private Label MessageDefineLabel;
    private CheckBox AutoCreateCharacterCheckBox;
    private Label label1;
    private TextBox SeverHostTextBox;
    private TextBox MonitorTextBox;
    private GroupBox groupBox1;
    private GroupBox groupBox2;
    private GroupBox groupBox3;
}
