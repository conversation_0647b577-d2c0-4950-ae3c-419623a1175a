using DotNetty.Transport.Channels;

namespace MiniClientGUI;

public class HeartbeatHandler : ChannelHandlerAdapter
{
    private void SendHeartBeatMessage(IChannelHandlerContext ctx)
    {
        if (!ctx.Channel.Active)
        {
            return;
        }

        // ClientTime 透传值，这里直接填写0，不影响心跳逻辑.
        // ctx.SendMessage(new ClientHeartbeatReq { ClientTime = 0 });

        ctx.Channel.EventLoop.ScheduleAsync(() => { SendHeartBeatMessage(ctx); },
            new TimeSpan(0, 0, 3));
    }

    public override void UserEventTriggered(IChannelHandlerContext ctx, object evt)
    {
        if (evt is LoggedInSuccessEvent)
        {
            // SendHeartBeatMessage(ctx);
        }
    }
}
