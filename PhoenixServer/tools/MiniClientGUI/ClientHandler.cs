using DotNetty.Transport.Channels;
using Google.Protobuf;
using Google.Protobuf.Reflection;
using Serilog;
using Serilog.Context;
using IMessage = Google.Protobuf.IMessage;
using Phoenix.MsgPackLogic.Protocol;

namespace MiniClientGUI;

public sealed class ClientHandler : SimpleChannelInboundHandler<MsgPackStructBase>
{
    private readonly MsgPackMessageDispatcher m_dispatcher;

    private readonly JsonFormatter m_jsonFormatter =
        new(JsonFormatter.Settings.Default.WithFormatDefaultValues(true).WithIndentation());

    public ClientHandler(MsgPackMessageDispatcher dispatcher)
    {
        m_dispatcher = dispatcher;
    }

    public override void ChannelActive(IChannelHandlerContext context)
    {
        string sessionId = "MiniClient";
        LogContext.PushProperty("SessionId", sessionId);
        //context.WriteAndFlushAsync(new HandShakeReq());
    }

    public override void ChannelInactive(IChannelHandlerContext ctx)
    {
        Log.Logger.Warning("Connection closed - Remote endpoint: {0}", ctx.Channel.RemoteAddress);
        base.ChannelInactive(ctx);
    }

   /* //TODO 这个函数先防在业务层.
    private IMessage? TryParseGame2DSNtfMessage(Game2DsNtf message)
    {
        int messageId = message.MsgId;

        // get the message parser
        MessageDescriptor? messageDescriptor =
            DSMessageRegistry.Instance.GetMessageDescriptor((short)messageId);
        if (messageDescriptor == null)
        {
            Phoenix.Server.Log.Logger.Warning("ClientHandler::TryParseGame2DSNtfMessage failed,invalid message id,id={Id}",messageId);
            return null;
        }

        return messageDescriptor.Parser.ParseFrom(message.Data);
    }
*/

    protected override void ChannelRead0(IChannelHandlerContext ctx, MsgPackStructBase message)
    {
/*        if (message is Game2DsNtf ntf)
        {
            IMessage? realMessage = TryParseGame2DSNtfMessage(ntf);
            if (realMessage != null)
            {
                Phoenix.Server.Log.Logger.Debug("Received Game2DSNtf message,id={0},name={1},data={2},ctx={3}",
                    ntf.MsgId, realMessage.Descriptor.Name, m_jsonFormatter.Format(realMessage),
                    m_jsonFormatter.Format(ntf.Context)
                );
                m_dispatcher.OnProtobufMessage(ctx, message);
            }
        }
        else*/
        {
/*            if (message is not ClientHeartbeatAck)
            {
                Phoenix.Server.Log.Logger.Debug("Received name = {0}, message = {1}", message.Descriptor.Name,
                    m_jsonFormatter.Format(message));
            }*/

            m_dispatcher.OnProtobufMessage(ctx, message);
        }
    }

    public override void ExceptionCaught(IChannelHandlerContext ctx, Exception e)
    {
        Log.Logger.Error(e, "ClientHandler ExceptionCaught");
        ctx.CloseAsync();
    }
}
