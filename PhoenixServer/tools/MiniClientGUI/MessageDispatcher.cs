using System.Diagnostics;
using DotNetty.Transport.Channels;
using Google.Protobuf;
using Google.Protobuf.Reflection;
using Newtonsoft.Json;
using Phoenix.MsgPackLogic.Protocol;
using Serilog;

namespace MiniClientGUI;

public interface IMessageCallback
{
    public void OnMessage(IChannelHandlerContext context, IMessage message);
}

public class MessageCallback<T> : IMessageCallback where T : class, IMessage, new()
{
    private readonly Action<IChannelHandlerContext, T> _callback;

    public MessageCallback(Action<IChannelHandlerContext, T> callback) => _callback = callback;

    public void OnMessage(IChannelHandlerContext context, IMessage message)
    {
        T? concrete = message as T;
        Debug.Assert(concrete != null);
        _callback(context, concrete);
    }
}

public class MessageDispatcher
{
    private readonly Action<IChannelHandlerContext, IMessage> _defaultMessageCallback;
    private readonly Dictionary<MessageDescriptor, IMessageCallback> _messageCallbacks = new();

    public MessageDispatcher(Action<IChannelHandlerContext, IMessage> defaultMessageCallback) =>
        _defaultMessageCallback = defaultMessageCallback;

    public void OnProtobufMessage(IChannelHandlerContext context, IMessage message)
    {
        if (_messageCallbacks.TryGetValue(message.Descriptor, out IMessageCallback? callback))
        {
            callback.OnMessage(context, message);
        }
        else
        {
            _defaultMessageCallback(context, message);
        }
    }

    public void RegisterMessageCallback<T>(Action<IChannelHandlerContext, T> callback) where T : class, IMessage, new()
    {
        MessageDescriptor descriptor = new T().Descriptor;
        _messageCallbacks[descriptor] = new MessageCallback<T>(callback);
        Log.Logger.Debug("RegisterMessageCallback successfully, name={Name}", descriptor.Name);
    }
}

public interface IMsgPackMessageCallback
{
    public void OnMessage(IChannelHandlerContext context, MsgPackStructBase message);
}

public class MsgPackMessageCallback<T> : IMsgPackMessageCallback where T : MsgPackStructBase, new()
{
    private readonly Action<IChannelHandlerContext, T> _callback;

    public MsgPackMessageCallback(Action<IChannelHandlerContext, T> callback) => _callback = callback;

    public void OnMessage(IChannelHandlerContext context, MsgPackStructBase message)
    {
        T? concrete = message as T;
        Debug.Assert(concrete != null);
        _callback(context, concrete);
    }
}

public class MsgPackMessageDispatcher
{
    // private readonly Action<IChannelHandlerContext, MsgPackStructBase> _defaultMessageCallback;
    private readonly Dictionary<int, IMsgPackMessageCallback> _messageCallbacks = new();

    public MsgPackMessageDispatcher(){}

    public void OnProtobufMessage(IChannelHandlerContext context, MsgPackStructBase message)
    {
        if (_messageCallbacks.TryGetValue((int)message.ProtoCode, out IMsgPackMessageCallback? callback))
        {
            callback!.OnMessage(context, message);
        }
        else
        {
            var protoCode = message.ProtoCode;
            var msgType = MsgPackProtoHelper.GetTypeByProtoId((int)message.ProtoCode);
            Log.Logger.Information("ProtoCode:{ProtoCode} {Type} {MsgContent}", (int)protoCode, msgType?.Name,
                JsonConvert.SerializeObject(message, Formatting.Indented));
            // _defaultMessageCallback(context, message);
        }
    }

    public void RegisterMessageCallback<T>(int protoCode, Action<IChannelHandlerContext, T> callback) where T : MsgPackStructBase, new()
    {
        //MessageDescriptor descriptor = new T().Descriptor;
        _messageCallbacks[protoCode] = new MsgPackMessageCallback<T>(callback);
        // Phoenix.Server.Log.Logger.Debug("RegisterMessageCallback successfully, name={Name}", descriptor.Name);
    }
}
