using DotNetty.Transport.Channels;
using Google.Protobuf;
using Phoenix.MsgPackLogic.Protocol;
using Newtonsoft.Json;
using Serilog;

namespace MiniClientGUI;

public static class ChannelHandlerContextExtensions
{
    private static readonly JsonFormatter JsonFormatter =
        new(JsonFormatter.Settings.Default.WithFormatDefaultValues(true).WithIndentation());

    public static void SendMessage(this IChannelHandlerContext ctx, IMessage message)
    {
        /*if (message is not ClientHeartbeatReq)
        {
            Log.Logger.Debug("Send name = {0}, message = {1}", message.Descriptor.Name, JsonFormatter.Format(message));
        }*/

        ctx.WriteAndFlushAsync(message);
    }

    public static void SendMessage(this IChannelHandlerContext ctx, MsgPackStructBase message)
    {
        /*        if (message is not ClientHeartbeatReq)
                {
                    Phoenix.Server.Log.Logger.Debug("Send name = {0}, message = {1}", message.Descriptor.Name, JsonFormatter.Format(message));
                }
        */
        var type = MsgPackProtoHelper.GetTypeByProtoId((int)message.ProtoCode);
        var name = type != null ? type.Name : "";
        Log.Logger.Debug("Send name = {Name}, message = {Msg}", name, JsonConvert.SerializeObject(message, Formatting.Indented));
        ctx.WriteAndFlushAsync(message);
    }

}

public static class ChannelExtensions
{
    private static readonly JsonFormatter JsonFormatter =
        new(JsonFormatter.Settings.Default.WithFormatDefaultValues(true).WithIndentation());

    public static void SendMessage(this IChannel channel, IMessage message)
    {
        Log.Logger.Debug("Send name = {0}, message = {1}", message.Descriptor.Name, JsonFormatter.Format(message));
        channel.WriteAndFlushAsync(message);
    }

    public static void SendMessage(this IChannel channel, MsgPackStructBase message)
    {
        if (message.HasToken())
        {
            message.SetToken(seqId++);
        }
        Log.Logger.Debug("Send name = {0}, message = {1}", message.ProtoCode, JsonConvert.SerializeObject(message, Formatting.Indented));
        channel.WriteAndFlushAsync(message);
    }

    private static uint seqId = (uint)DateTime.Now.Ticks;
}
