using System.Text;
using Serilog.Core;
using Serilog.Events;
using Serilog.Formatting;

namespace MiniClientGUI;

public class TextBoxSink : ILogEventSink
{
    private readonly ITextFormatter _formatter;
    private readonly TextBox _textBox;

    public TextBoxSink(TextBox textBox, ITextFormatter formatter)
    {
        _textBox = textBox ?? throw new ArgumentNullException(nameof(textBox));
        _formatter = formatter;
    }

    public void Emit(LogEvent logEvent)
    {
        if (logEvent == null)
        {
            throw new ArgumentNullException(nameof(logEvent));
        }

        StringWriter buffer = new StringWriter(new StringBuilder(256));
        _formatter.Format(logEvent, buffer);
        string formattedLogEventText = buffer.ToString();

        if (_textBox.IsHandleCreated)
        {
            _textBox.Invoke(() => { _textBox.AppendText(formattedLogEventText); });
        }
        else
        {
            EventHandler handlerCreated = default!;
            EventHandler created = handlerCreated;
            handlerCreated = (sender, args) =>
            {
                _textBox.HandleCreated -= created;
                _textBox.Invoke(() => { _textBox.AppendText(formattedLogEventText); });
            };
            _textBox.HandleCreated += handlerCreated;
        }
    }
}
