using System.Net;
using System.Security.Cryptography;
using Phoenix.Codecs;
using DotNetty.Buffers;
using DotNetty.Codecs;
using DotNetty.Codecs.Protobuf;
using DotNetty.Transport.Bootstrapping;
using DotNetty.Transport.Channels;
using DotNetty.Transport.Channels.Sockets;
using FuzzySharp;
using FuzzySharp.Extractor;
using FuzzySharp.SimilarityRatio;
using FuzzySharp.SimilarityRatio.Scorer;
using FuzzySharp.SimilarityRatio.Scorer.StrategySensitive;
using Google.Protobuf;
using Google.Protobuf.Reflection;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Extensions.Logging;
using Phoenix.MsgPackLogic.Protocol;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using Newtonsoft.Json;
using Phoenix.Server.CommonDataStructure;

#if ENABLE_TRACE_DOTNETTY
using DotNetty.Common.Internal.Logging;
using DotNetty.Handlers.Logging;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
#endif

namespace MiniClientGUI;

public partial class Form1 : Form
{
    private IChannel? m_connection;
    private readonly Random m_random = new();

    public static readonly SerilogLoggerFactory LoggerFactory = new();

    private readonly MsgPackMessageDispatcher m_messageDispatcher = new();

    private List<string> m_messageNames = new();
    private Dictionary<string, Type> m_messageName2Type = new();

    // private MsgPack_LoginByAuthToken_Req? m_loginByAuthTokenReq = null;

    private IClientSecretsManager m_clientSecretsManager = new MiniClientSecretManager();
    public Form1()
    {
        InitializeComponent();
        // 配置 serilog 日志
        ConfigureSerilog();
        // 初始化 Message Id 注册表
        // InitMessageIdRegistry();
        // 初始化 Message 分发器
        InitMessageDispatcher();
        foreach (var pair in MsgPackProtoHelper.GetClientProtoId2Types())
        {
            if(!MsgPackProtoHelper.IsProtocolFromClient(pair.Key))
            {
                continue; // 忽略 Ntf 和 Ack 消息
            }
            m_messageNames.Add(pair.Value.Name);
            m_messageName2Type.Add(pair.Value.Name, pair.Value);
        }
        // m_messageNames.AddRange(ClientMessageRegistry.Instance.MessageNames);
        // m_messageNames.AddRange(DSMessageRegistry.Instance.MessageNames);
        // m_messageNames.Sort();

        // 初始化 UI 下拉框 消息列表
        MessageNameComboBox.DataSource = m_messageNames;
    }

    private void ConfigureSerilog()
    {
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.TextBoxWriter(MonitorTextBox)
            .CreateLogger();

#if ENABLE_TRACE_DOTNETTY
        InternalLoggerFactory.DefaultFactory =
            LoggerFactory.Create(builder => builder.AddSerilog().AddFilter((category, level) => true));
#endif
    }

    // private static void OnUnknownMessageType(IChannelHandlerContext ctx, MsgPackStructBase msg)
    // {
    //     Log.Logger.Information("OnUnknownMessageType {Msg}", JsonConvert.SerializeObject(msg, Formatting.Indented));
    //     var type = MsgPackProtoHelper.GetTypeByCmdIndex((int)msg.ProtoCode);
    //     if (type != null)
    //     {
    //         Log.Logger.Warning("OnUnknownMessageType:{ProtoCode}, {Type}", msg.ProtoCode, type);
    //     }
    // }
    private static string GenerateRandomKey(int keySize)
    {
        byte[] key = new byte[keySize / 8]; // 将密钥大小转换为字节数
        using (Aes aesCrypto = Aes.Create())
        {
            aesCrypto.GenerateKey(); // 自动生成随机密钥
            Array.Copy(aesCrypto.Key, key, key.Length); // 将生成的密钥复制到目标数组中
        }
        return Convert.ToBase64String(key);
    }

    /*
    private void OnHandShakeAck(IChannelHandlerContext ctx, HandShakeAck msg)
    {
        if (msg.ErrCode != EErrCode.ErrCodeOk)
        {
            Log.Logger.Error("HandShakeAck failed, ErrCode={ErrCode}", msg.ErrCode);
            ctx.CloseAsync();
            return;
        }

        string aesKey = GenerateRandomKey(256);
        m_loginByAuthTokenReq!.AesKey = aesKey;
        m_clientSecretsManager.SetAesKey("", aesKey);
        ctx.SendMessage(m_loginByAuthTokenReq);

    }*/
    private void OnLoginByAuthTokenAck(IChannelHandlerContext ctx, LoginByAuthTokenAck msg)
    {
        Log.Logger.Information("OnLoginByAuthTokenAck {Msg}", JsonConvert.SerializeObject(msg, Formatting.Indented));
        if (msg.ErrCode != (int)ErrCode.ErrCodeOk)
        {
            Log.Logger.Error("LoginByAuthTokenAck failed, ErrCode={ErrCode}", msg.ErrCode);
            ctx.CloseAsync();
            return;
        }

        Log.Logger.Debug("LoginByAuthToken success");
        ctx.Channel.SendMessage(new LoginBySessionTokenReq { SessionToken = msg.SessionToken });
    }

    private void OnLoginBySessionTokenAck(IChannelHandlerContext ctx, LoginBySessionTokenAck msg)
    {
        Log.Logger.Information("OnLoginBySessionTokenAck {Msg}", JsonConvert.SerializeObject(msg, Formatting.Indented));
        if (msg.ErrCode != (int)ErrCode.ErrCodeOk)
        {
            Log.Logger.Error("LoginBySessionTokenAck failed, ErrCode={ErrCode}", msg.ErrCode);
            ctx.CloseAsync();
            return;
        }

        Log.Logger.Debug("LoginBySessionTokenAck success");

        ctx.Channel.SendMessage(new PlayerInfoInitReq());
    }

    private void OnPlayerInfoInitAck(IChannelHandlerContext ctx, PlayerInfoInitAck msg)
    {
        Log.Logger.Information("OnPlayerInfoInitAck {Msg}", JsonConvert.SerializeObject(msg, Formatting.Indented));
        if (msg.ErrCode != (int)ErrCode.ErrCodeOk)
        {
            // need create character
            if (msg.ErrCode == (int)ErrCode.ErrCodeCharacterNotExist)
            {
                Log.Logger.Warning("PlayerInfoInit failed, ErrCode={ErrCode}, need create character", msg.ErrCode);
                if (AutoCreateCharacterCheckBox.Checked)
                {
                    ctx.Channel.SendMessage(new CharacterCreateReq { NickName = $"Test{m_random.Next(99999):D5}", });
                }
                else
                {
                    Log.Logger.Information("Please Manual Create Character");
                }
            }
            else
            {
                Log.Logger.Warning("PlayerInfoInitAck failed, ErrCode={ErrCode}", msg.ErrCode);
            }

            return;
        }

        Log.Logger.Debug("PlayerInfoInitAck successfully");
        ctx.FireUserEventTriggered(new LoggedInSuccessEvent());
    }

    private void OnCharacterCreateAck(IChannelHandlerContext ctx, CharacterCreateAck msg)
    {
        Log.Logger.Information("OnCharacterCreateAck {Msg}", JsonConvert.SerializeObject(msg, Formatting.Indented));
        if (msg.ErrCode != (int)ErrCode.ErrCodeOk)
        {
            Log.Logger.Warning("CharacterCreate failed, ErrCode:{ErrCode}", msg.ErrCode);
            return;
        }

        ctx.Channel.SendMessage(new PlayerInfoInitReq());
        Log.Logger.Debug("CharacterCreate successfully");
    }

    private void InitMessageDispatcher()
    {
        //m_messageDispatcher.RegisterMessageCallback<HandShakeAck>(OnHandShakeAck);
        m_messageDispatcher.RegisterMessageCallback<LoginByAuthTokenAck>((int)EProtoCode.LOGIN_LOGINBYAUTHTOKENACK, OnLoginByAuthTokenAck);
        m_messageDispatcher.RegisterMessageCallback<LoginBySessionTokenAck>((int)EProtoCode.LOGIN_LOGINBYSESSIONTOKENACK, OnLoginBySessionTokenAck);
        m_messageDispatcher.RegisterMessageCallback<PlayerInfoInitAck>((int)EProtoCode.PLAYERINIT_PLAYERINFOINITACK, OnPlayerInfoInitAck);
        m_messageDispatcher.RegisterMessageCallback<CharacterCreateAck>((int)EProtoCode.PLAYERINIT_CHARACTERCREATEACK, OnCharacterCreateAck);
    }

    private async void LoginButton_Click(object sender, EventArgs e)
    {
        string host = SeverHostTextBox.Text;
        string[] parts = host.Split(':');
        IPAddress ipAddress = IPAddress.Parse(parts[0]);
        int port = int.Parse(parts[1]);

        string account = AccountTextBox.Text;
        string version = VersionTextBox.Text;
        string deviceId = DeviceIdTextBox.Text;
        string language = LangueTextBox.Text;
        var group = new MultithreadEventLoopGroup();
        try
        {
            //string filePath = "./3rd/Oodle/win/oo2core_9_win64.dll";
            var bootstrap = new Bootstrap();
            bootstrap
                .Group(group)
                .Channel<TcpSocketChannel>()
                .Option(ChannelOption.TcpNodelay, true)
                .Handler(new ActionChannelInitializer<ISocketChannel>(channel =>
                {
                    var pipeline = channel.Pipeline;
#if ENABLE_TRACE_DOTNETTY
                    pipeline.AddLast(new LoggingHandler("CONN"));
#endif
                    //pipeline.AddLast("framing-size-dec",
                    //    new LengthFieldBasedFrameDecoder(ByteOrder.LittleEndian, int.MaxValue, 0, 4, -4, 4, true));
                    //pipeline.AddLast("framing-encrypt-dec", new EncryptionDecode(m_clientSecretsManager));
                    //pipeline.AddLast("framing-compress-dec", new CompressDecoder(filePath));
                    //pipeline.AddLast("framing-message-dec", new ProtobufDecoder(ClientMessageRegistry.Instance));

                    //pipeline.AddLast("framing-size-enc", new LengthFieldPrepender(ByteOrder.LittleEndian, 4, 4, false));
                    //pipeline.AddLast("framing-encrypt-enc", new EncryptionEncode(m_clientSecretsManager));
                    //pipeline.AddLast("framing-compress-enc", new CompressEncoder(filePath));
                    //pipeline.AddLast("framing-message-enc", new ProtobufEncoder(ClientMessageRegistry.Instance));
                    pipeline.AddLast("framing-size-dec", new ProtobufVarint32FrameDecoder());


                    // Add a handler for encoding the length of the data packet.
                    /*pipeline.AddLast("framing-size-enc",
                        new LengthFieldPrepender(ByteOrder.LittleEndian, NetworkPackageConstants.LengthFiledSize,
                            NetworkPackageConstants.LengthFiledSize, false));*/

                    pipeline.AddLast("framing-size-enc", new ProtobufVarint32LengthFieldPrepender());

                    /*        // Add a handler for encrypting the data.
                            pipeline.AddLast("framing-encrypt-enc", new EncryptionEncode(m_clientSecretsManager));

                            // Add a handler for decrypting the data.
                            pipeline.AddLast("framing-encrypt-dec", new EncryptionDecode(m_clientSecretsManager));

                            // Add a handler for compressing the data packet.
                            pipeline.AddLast("framing-compress-enc", new CompressEncoder(filePath));

                            // Add a handler for decompressing the data packet.
                            pipeline.AddLast("framing-compress-dec", new CompressDecoder(filePath))*/
                    ;

                    // Add a handler for decoding the data packet with msgpack.
                    pipeline.AddLast("framing-pb-dec", new MessagePackDecoder<MsgPackStructBase>());

                    // Add a handler for encoding the data packet with msgpack.
                    pipeline.AddLast("framing-pb-enc", new MessagePackEncoder<MsgPackStructBase>());
                    pipeline.AddLast("client-handler", new ClientHandler(m_messageDispatcher));
                    //pipeline.AddLast("heartbeat-loop", new HeartbeatHandler());
                }));

            m_connection = await bootstrap.ConnectAsync(new IPEndPoint(ipAddress, port));
            var loginByAuthTokenReq = new LoginByAuthTokenReq
            {
                AuthToken = $"{account}|fake|Phoenix|001|xiaomi|xxxxxxxxx",
                ClientVersion = version,
                ClientDeviceId = deviceId,
                Localization = language
            };
            Log.Logger.Debug("连接服务器({Host})成功,准备进行登录", host);

            m_connection.SendMessage(loginByAuthTokenReq);
            await m_connection.CloseCompletion;
        }
        catch (Exception exception)
        {
            Log.Logger.Error(exception, "连接服务器({Host})失败", host);
        }
        finally
        {
            await group.ShutdownGracefullyAsync();
        }
    }

    private void ChangeMessageDefine(string name)
    {
        if(m_messageName2Type.TryGetValue(name, out Type? messageType))
        {
            var message = Activator.CreateInstance(messageType);
            var settings = new JsonSerializerSettings
            {
                Formatting = Formatting.Indented // 美化格式
            };
            string jsonMessage = JsonConvert.SerializeObject(message, settings);
            MessageDefineTextBox.Text = jsonMessage;
        }
        /*string? jsonMessage = ClientMessageRegistry.Instance.GetDefaultMessageJsonString(name) ??
                              DSMessageRegistry.Instance.GetDefaultMessageJsonString(name);

        if (jsonMessage == null)
        {
            return;
        }

        MessageDefineTextBox.Text = jsonMessage;*/
    }

    private void SearchMessageTextBox_TextChanged(object sender, EventArgs e)
    {
        string keyword = SearchMessageTextBox.Text;

        if (string.IsNullOrEmpty(keyword))
        {
            MessageNameComboBox.DataSource = m_messageNames;
            ChangeMessageDefine(m_messageNames.First());
        }
        else
        {
            IEnumerable<ExtractedResult<string>>? foundItems =
                Process.ExtractTop(keyword, m_messageNames, s => s, limit: 10);
            if (foundItems == null)
            {
                return;
            }

            IEnumerable<string> foundNames = foundItems.Select(item => item.Value).ToList();
            MessageNameComboBox.DataSource = foundNames;
            if (foundNames.Any())
            {
                ChangeMessageDefine(foundNames.First());
            }
        }
    }

    /*private IMessage TryPackageGame2DSNtfMessage(IMessage message)
    {
        if (!DSMessageRegistry.Instance.MessageNames.Contains(message.Descriptor.Name))
        {
            return message;
        }

        var ntf = new Ds2GameNtf();
        short? messageId = DSMessageRegistry.Instance.GetMessageId(message);
        if (messageId == null)
        {
            throw new ArgumentException(
                $"ClientHandler::TryPackageGame2DSNtfMessage failed,not found message id,name={message.Descriptor.Name}");
        }

        ntf.MsgId = (int)messageId;
        ntf.Data = message.ToByteString();
        return ntf;
    }*/

    private void SendMessageButton_Click(object sender, EventArgs e)
    {
        if (m_connection == null)
        {
            Log.Logger.Warning("SendMessage failed, not connection state");
            return;
        }

        string name = MessageNameComboBox.Text;
        string jsonMessage = MessageDefineTextBox.Text;
        if(m_messageName2Type.TryGetValue(name, out Type? messageType))
        {
            try
            {
                var message = JsonConvert.DeserializeObject(jsonMessage, messageType) as MsgPackStructBase;
                if (message != null)
                {
                    m_connection.SendMessage(message);
                }
            }
            catch (Exception exception)
            {
                Log.Logger.Error(exception, "SendMessage failed, name={Name} jsonMessage={JsonMessage}", name, jsonMessage);
                Console.WriteLine(exception);
            }
        }
/*
        IMessage? message = ClientMessageRegistry.Instance.ParseJsonMessage(name, jsonMessage) ??
                            DSMessageRegistry.Instance.ParseJsonMessage(name, jsonMessage);
        if (message == null)
        {
            Phoenix.Server.Log.Logger.Warning("SendMessage failed, unknown messageName = {Name}", name);
            return;
        }*/

        // m_connection.SendMessage(TryPackageGame2DSNtfMessage(message));
    }

    private void MessageNameComboBox_SelectedIndexChanged(object sender, EventArgs e)
    {
        string? name = MessageNameComboBox.SelectedItem as string;
        if (name != null)
        {
            ChangeMessageDefine(name);
        }
    }

    private void MonitorTextBox_TextChanged(object sender, EventArgs e)
    {

    }

    private void Form1_Load(object sender, EventArgs e)
    {

    }
}
