<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Configurations>Debug;Release</Configurations>
    <Platforms>x64</Platforms>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <DefineConstants>TRACE;PHOENIX_MINICLIENT</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <DefineConstants>TRACE;PHOENIX_MINICLIENT</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Protos\**" />
    <EmbeddedResource Remove="Protos\**" />
    <None Remove="Protos\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DotNetty.Codecs" />
    <PackageReference Include="DotNetty.Handlers" />
    <PackageReference Include="FuzzySharp" />
    <PackageReference Include="Google.Protobuf" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="Serilog" />
    <PackageReference Include="Grpc.Tools">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Serilog.Extensions.Logging" />
  </ItemGroup>

  <!-- Code Analyzers -->
  <ItemGroup Condition=" '$(Configuration)' == 'Debug' ">
    <PackageReference Include="SerilogAnalyzer" PrivateAssets="All" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Phoenix.Codecs\Phoenix.Codecs.csproj" />
    <ProjectReference Include="..\..\src\Phoenix.Server.CommonDataStructure\Phoenix.Server.CommonDataStructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Generated\" />
  </ItemGroup>

</Project>
