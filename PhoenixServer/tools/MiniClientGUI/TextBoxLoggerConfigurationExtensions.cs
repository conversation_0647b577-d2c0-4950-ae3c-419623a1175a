using MiniClientGUI;
using Serilog.Configuration;
using Serilog.Core;
using Serilog.Events;
using Serilog.Formatting.Display;

namespace Serilog;

public static class TextBoxLoggerConfigurationExtensions
{
    private const string DefaultOutputTemplate =
        "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level}] {Message}{NewLine}{Exception}";

    public static LoggerConfiguration TextBoxWriter(
        this LoggerSinkConfiguration sinkConfiguration,
        TextBox textBox,
        LogEventLevel restrictedToMinimumLevel = LevelAlias.Minimum,
        string outputTemplate = DefaultOutputTemplate,
        IFormatProvider? formatProvider = null,
        LoggingLevelSwitch? levelSwitch = null)
    {
        if (textBox == null)
        {
            throw new ArgumentNullException(nameof(textBox));
        }

        if (outputTemplate == null)
        {
            throw new ArgumentNullException(nameof(outputTemplate));
        }

        MessageTemplateTextFormatter formatter = new(outputTemplate, formatProvider);
        TextBoxSink sink = new(textBox, formatter);
        return sinkConfiguration.Sink(sink, restrictedToMinimumLevel, levelSwitch);
    }
}
