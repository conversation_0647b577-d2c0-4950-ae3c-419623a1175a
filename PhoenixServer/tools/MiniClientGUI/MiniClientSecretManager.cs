// Copyright (c) Phoenix.  All Rights Reserved.

using Phoenix.Codecs;

namespace MiniClientGUI;

public class MiniClientSecretManager : IClientSecretsManager
{
    public MiniClientSecretManager()
    {
        AesKey = string.Empty;
    }

    private string Aes<PERSON>ey { get; set; }
    public void GenerateRsaSecrets(string id) => throw new NotImplementedException();

    public void GetLittleEndianPubKey(string id, ref string pubExp, ref string modulus) => throw new NotImplementedException();

    public string GetPriKeyXml(string id) => throw new NotImplementedException();

    public void SetAesKey(string id, string inAesKey)
    {
        AesKey = inAesKey;
    }

    public string GetAesKey(string id)
    {
        return AesKey;
    }

    public void RemoveSecret(string id) => throw new NotImplementedException();
    public void PrepareRsaKeys(int keyPairs) => throw new NotImplementedException();
}
